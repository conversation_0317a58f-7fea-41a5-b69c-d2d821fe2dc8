---
description: AI rules derived by <PERSON><PERSON><PERSON><PERSON> from the project AI interaction history
globs: *
---

## HEADERS

## PROJECT RULES

### CONTEXT AND PROVIDERS IN REACT
- **CONTEXT:** A React feature for sharing data across components without prop drilling.
- **PROVIDER:** A component that wraps your component tree and provides the context value to child components.
- **CONTEXTPROVIDER:** A custom component combining context and provider logic for easier use.

### WHEN TO USE CONTEXT
- **GOOD USE CASES:** User authentication state, theme settings, language/locale settings, shopping cart state, app-wide configuration.
- **BAD USE CASES:** Frequently changing data, data used by only 2-3 components, complex state management.

### IMPLEMENTATION GUIDELINES
- **Read First, Code Later:** Before implementing any new feature or making changes, always read and understand the necessary code.
- **Seek Approval:** After understanding the code and formulating a plan, always present the plan for approval before starting implementation. **DON'T CODE YET**.
- **Coding Standard Enforcement:** Always adhere to the established coding standards.
- **Translation Keys:** When updating text, always update the related translation keys in both `en.json` and `ta.json`.

### SKU (Stock Keeping Unit) Management
- **SKU creation and edition should utilize the same form and components, maintaining a consistent UI.**
- **SKU ID Requirement:** While creating both child and parent SKUs, `skuId` is a required field which is updated by the user (only number).
- **Selection of either child or parent must be done in the SKU creation form.**
- **Based on the UI fields will be shown.**

#### Parent SKUs:
 - **Categories:** Allow selection of categories.
 - **Images:** Allow image, and multiple images
 - **Description:** Allow adding a description.
 - **Status:** Allow setting Active/Inactive status.
 - **Child SKUs:** Enable selection of child SKUs from existing variants using `@SkuSelectionModal.tsx`. Child SKUs cannot be created directly as variants.
 - **Pricing:** Do not display pricing fields, as parent SKUs do not have pricing.
 - **Identification:** Clearly indicate that the SKU is a parent.
 - **Variant Essential Fields:** When copying a child SKU to a parent's variants array, include the following essential fields: `skuId`, `name`, `costPrice`, `sellingPrice`, `mrp`, `type: 'child'`, `variantName`, `isActive: 0`. Other fields like `imageUrl`, `images`, and `description` can fallback to the parent if not specified.

#### Child SKUs:
 - **Parent Selection:** Allow selection of a single parent SKU.
 - **Categories:** Allow selection of categories (non-mandatory).
 - **Variant Name:** Variant name is mandatory.
 - **Name:** Name is mandatory
 - **Attributes:** Allow description, variantName, image, and images to be overridden from the parent.
 - **Status:** Allow setting Active/Inactive status.
 - **Identification:** Clearly indicate that the SKU is a child.
 - **Pricing:** Allow setting CP (Cost Price), SP (Selling Price), and MRP (Maximum Retail Price).
   - **Parent SKU Update:** If a child SKU is created, the parent SKU must be updated to include the new child as a variant.

#### SKU Edition:
 - **Parent Update:** Whenever a child SKU is updated, the parent SKU must also be updated.
 - **Orphan Handling:** If a child SKU becomes orphaned (parent removed), the parent SKU must be updated accordingly.
 - **General Parent Update Rule:** In every case where a parent SKU exists, ensure it reflects changes in its children.
- **SKU Edition and Creation:** SKU creation and edition should use the same form and components, the UI should look mostly the same.
- **Parent/Child Selection:** The SKU creation form must include the option to select either "child" or "parent" SKU type, dynamically displaying relevant fields based on the selection.
- **Parent/Child Updates:** When a child SKU is updated, the parent SKU must also be updated in a single `upsert` call. If a child SKU becomes orphaned (parent removed), the parent SKU must be updated accordingly. In every case where a parent SKU exists, ensure it is updated to reflect changes in its children.
- **SKU View Page:** In the SKU view page, for the variants list, provide an option of edit which will redirect to the edit page for that variant sku.
- **Inactive Status Propagation:** When marking an SKU as inactive, ensure the `isActive: 0` key is also updated in the parent's variants. This applies both in the SKU homepage (`@page.tsx`) and SKU edit page (`@page.tsx`).

### GetSkusOption
- In `GetSkusOption`, add `allowInactive` field.
  - The inactivity is filtered on the client side only as backend api doesn't support this filter.
  - Update the logic of all the methods that uses this function and filter it out
  - For all the customer pages wherever we are calling the functions which require this input, don't allow active.

### SKU Operations Service (Unified SKU Operations)
- All SKU save, update, and bulk operations must go through `SkuOperationsService` to maintain data consistency.
- This service consolidates all SKU save, update, and bulk operations to ensure:
  - Consistent data transformation across all pages
  - Proper parent-child synchronization
  - Complete category relationship management
  - No missing fields like variantName

### SkuCard.tsx
- Two cases can happen either the SKU is 
    - parent (with children) (please don't show parent without children)
    - child (orphan or with parent) - Some child can be added to make their visibility higher. 
- Add fallback place holder image.
- For Parent, show `variant.variantName` instead of `variant.name` for the "Parent Child" display.
- For Orphan show both, `variant.name` and `variant.variantName`, wherever possible when the data is there and not null or empty.
- In `SkuCard.tsx` make sure `isActive` is handled as well.
    - SKUs with `isActive === 0` are completely hidden from customer view
    - If the SKU is a child with a valid parent, then redirect to the parent.
        - If the SKU loaded is either parent or orphan, don't change.
        - If the SKU is child with valid parent, then redirect to the parent.

### SKU Import/Export
- For the SKU Import/Export add the `variantName` column english and tamil both can be imported and exported.

## TECH STACK

-   React Context
-   Zustand (When Context is not performant)
-   TanStack Query
-   Next.js useRouter, useSearchParams, usePathname
-   react-datepicker (For custom date picker components, always display dd/MM/YYYY)

## PROJECT DOCUMENTATION & CONTEXT SYSTEM

## CODING STANDARDS

### REACT CONTEXT AND STATE MANAGEMENT

-   **AVOID CONTEXT FOR**: Frequently changing data (e.g., cart items, form inputs, animations), large objects, cross-cutting concerns, performance-critical scenarios.
-   **USE CONTEXT FOR**: Infrequent changes (theme, auth status, language), simple values, app-wide state, configuration.
-   Zustand selectors need to return stable references to prevent unnecessary re-renders. When selectors create new objects/arrays on every call, React thinks the state has changed and triggers re-renders, potentially causing infinite loops.

### LOGGING
-   Always use the centralized logger from `@/lib/logger` instead of `console.log`.

### ERROR HANDLING
- Implement proper error handling with user-friendly fallbacks. Displaying "0" or "—" or an error indicator on API failure is acceptable, but the specific choice may depend on the situation and user experience considerations (further user confirmation needed). When an API fails, show an error message.
- When an API fails, show an error message.

### TRANSLATIONS & I18N
- When updating text, always update the related translation keys in both `en.json` and `ta.json`.

### URL QUERY PARAMETERS
- When implementing filters, use URL query parameters to enable shareable/bookmarkable links and maintain state across navigation.
- URL parameters should always take priority over default values.
- Ensure only non-empty parameters are added to the URL to keep it clean.
- Dates in URL parameters should be in `dd-MM-yyyy` format.

### DATATABLE SORTING
- When implementing sorting in data tables, extend the Column interface to include a `getSortValue` function to handle sorting based on computed values or derived data. The `getSortValue` function should return a primitive value suitable for sorting.
- Default sorting state is 2-state only (asc <-> desc).

### DATE PICKER
- Always use the `react-datepicker` library for consistent date picker components.
- The date format displayed in the UI should always be `dd/MM/YYYY`.
- The date format used for API communication should always be `YYYY-MM-DD`.

## DEBUGGING

## WORKFLOW & RELEASE RULES

-   **Plan Approval:** Always wait for approval of the implementation plan before coding.
-   **Translation Keys:** When updating text, always update the related translation keys in both `en.json` and `ta.json`.
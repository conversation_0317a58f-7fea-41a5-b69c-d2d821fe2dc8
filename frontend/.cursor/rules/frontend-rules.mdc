---
description: 
globs: 
alwaysApply: false
---
# Frontend Codebase Documentation

## Project Overview
The Infinity Portal is a web application for **users** (e.g., <PERSON><PERSON><PERSON><PERSON>, the portal operator) to manage pre-orders for various products on behalf of multiple **customers** (e.g., <PERSON><PERSON><PERSON>, the end-customer). The frontend is built with Next.js, React, TypeScript, and Tailwind CSS.

**Key Naming Conventions:**
-   **User:** Refers to the portal operator (e.g., <PERSON><PERSON><PERSON><PERSON>) who logs into the system to manage operations.
-   **Customer:** Refers to the end-customer (e.g., <PERSON><PERSON><PERSON>) for whom orders are placed and sessions are managed.

Use React Hooks as much as possible wherever necessary.

## Environment Configuration / Application Modes

The application supports two primary operational modes, determined by the `NEXT_PUBLIC_APP_MODE` environment variable:

*   **`admin`** (default, or when `NEXT_PUBLIC_APP_MODE=admin`):
    *   Intended for `infinity-admin.wheelocity.com`.
    *   Shows all features, including user (operator) login, multi-customer session management, etc.
*   **`customer`** (when `NEXT_PUBLIC_APP_MODE=customer`):
    *   Intended for `infinity.wheelocity.com`.
    *   Provides a simplified UI for the end-customer ('Bhoomi') by:
        *   Hiding operator-specific features like the main user login button in the header.
        *   Hiding UI elements related to multi-customer session management (session tabs on desktop, "Sessions" icon in mobile bottom navigation).
        *   Hiding the "Account" icon (for user login) in the mobile bottom navigation.
        *   Automatically managing a single, default session for the customer.
*   The `src/lib/utils.ts` file (see below) contains helper functions like `isCustomerFacingMode()` to query the current mode throughout the application.

## Key Files and Directories

### App Structure
-   `src/app/page.tsx` - Main page component that renders the application's home page with product categories.
-   `src/app/layout.tsx` - Root layout component that wraps the entire application, likely including the main `Header`.
-   `src/app/globals.css` - Global styles for the application.
-   `src/app/ClientProviders.tsx` - Client-side providers wrapper component, including `AuthProvider` and `SessionProvider`.
-   `src/app/types/session.ts` - Centralized TypeScript interfaces for **customer** session-related data structures (`CartItem`, `Session`, `SessionTabDisplay`, `DetailedCartItem`, `CartDetails`).
-   `src/app/types/auth.ts` - Centralized TypeScript interfaces for **user** authentication data structures (`UserDetails`).
-   `src/lib/utils.ts` - Utility functions.
    -   Includes `AppMode` enum (`CUSTOMER`, `ADMIN`).
    -   `getAppMode()`: Determines the current application mode based on the `NEXT_PUBLIC_APP_MODE` environment variable (defaults to `ADMIN`).
    -   `isCustomerFacingMode()`: Returns `true` if `NEXT_PUBLIC_APP_MODE` is set to `customer`, used to conditionally render UI and behavior.

### Components
-   `src/app/components/Header/Header.tsx` - Responsive header.
    -   Manages and displays the login state of the **user** (operator) via an "Account" button/dropdown, which triggers the `LoginModal`. In **customer-facing mode**, the main 'Account' button/dropdown for user (operator) login is hidden.
    -   Displays **customer** session information (e.g., active customer session name on mobile). The display of this active customer session name is hidden in **customer-facing mode** to simplify the UI, as there's only one implicit session for 'Bhoomi'.
    -   Provides access to **customer** cart (`CartDrawer`) and **customer** session management (`SessionManager` for desktop, `MobileSessionsSheet` for mobile). The `SessionManager` (desktop tab system for managing multiple customer sessions) is hidden in **customer-facing mode**.
    -   Includes `LanguageSwitcher`, `SearchBar`.
    -   Manages visibility of mobile-specific UI elements and drawers/sheets (`MobileMoreMenu`, `MobileSearchSheet`, `MobileSessionsSheet`, `MobileAccountSheet`).
-   `src/app/components/Auth/LoginModal.tsx` (or `src/app/components/LoginModal.tsx`) - Modal component for **user** (operator) login via mobile number and OTP. Uses `AuthContext` for OTP logic and managing user authentication state.
-   `src/app/components/Header/SessionManager.tsx` - Chrome-style tab system for managing multiple **customer** sessions (primarily for desktop/tablet). Exports `SessionModal` and `ConfirmationDialog` for reuse in mobile components. *This component is not rendered in customer-facing mode.*
-   `src/app/components/Header/LanguageSwitcher.tsx` - Component for switching languages (desktop/tablet). For mobile, language selection is integrated into `MobileMoreMenu.tsx`.
-   `src/app/components/SearchBar.tsx` - Search functionality with dropdown results, searches across all products. Accepts a `ref` for autofocus capabilities.
-   `src/app/components/ProductCard.tsx` - Revamped card component for displaying individual product information, including discounts, MRP, variant selection, and conditional "ADD" vs. quantity controls.
-   `src/app/components/CategorySection.tsx` - Horizontal scrollable section for displaying products by category, featuring a "View More" link and scroll chevrons.
-   `src/app/components/CartDrawer.tsx` - Side drawer displaying detailed **customer** cart contents, bill summary, and checkout options.
-   `src/app/components/Header/MobileBottomNav.tsx` - Mobile-specific sticky bottom navigation bar. Always shows 'Search' and 'Cart' icons. The 'Sessions' and 'Account' icons are conditionally rendered based on `isCustomerFacingMode()` (i.e., they are **hidden in customer-facing mode**). The 'Account' icon, when visible, pertains to the **user's** (operator's) account. Accepts `showSessionsIcon: boolean` and `showAccountIcon: boolean` props to control visibility, typically driven by `!isCustomerFacingMode()` in `Header.tsx`.
-   `src/app/components/Header/MobileMoreMenu.tsx` - Mobile "More" options bottom sheet.
    -   Includes integrated language selection.
    -   Provides access to **user** account actions (Login/Logout, link to Account Sheet) based on user's login state from `AuthContext`.
-   `src/app/components/Header/MobileSearchSheet.tsx` - Mobile search interface presented as a top-sliding sheet with autofocus on the search input.
-   `src/app/components/Header/MobileSessionsSheet.tsx` - Mobile **customer** session management bottom sheet, reusing `SessionModal` and `ConfirmationDialog`. *This component is not accessible in customer-facing mode as the trigger icon is hidden.*
-   `src/app/components/Header/MobileAccountSheet.tsx` - Mobile **user** account management bottom sheet (displays user details, provides Logout, or Login option). Triggered from `MobileBottomNav` or `MobileMoreMenu`. *This component is not accessible via `MobileBottomNav` in customer-facing mode as the trigger icon is hidden.*

### Context
-   `src/app/context/AuthContext.tsx` - Context provider for managing **user** (operator) authentication.
    -   Handles user login state (`isUserLoggedIn`), user details (`userDetails`), JWT token.
    -   Provides functions for OTP-based login (`sendOtp`, `verifyOtp`) and logout (`userLogout`).
    -   Persists user auth data to `localStorage`.
-   `src/app/context/SessionContext.tsx` - Context provider for managing multiple **customer** sessions and their associated carts.
    -   Manages `sessions` list, `activeSessionId`, and functions to add, update, remove customer sessions.
    -   Integrates `isCustomerFacingMode()` from `lib/utils`. In **customer-facing mode** (when `NEXT_PUBLIC_APP_MODE` is 'customer'), if no sessions exist upon initialization (e.g., on first visit or after clearing storage), a default session (e.g., 'My Session', from the i18n key `t('session.defaultCustomerName')`) is automatically created and activated. This simplifies the experience for the end-customer ('Bhoomi') by ensuring a session is always present without manual creation.
    -   The `addSession` function is mode-aware; in customer-facing mode, if called without specific session data, it defaults to creating/using the standard customer session name.
    -   The `startOrActivateCustomerSession` function is also mode-aware. In customer-facing mode, it ensures the default session is active and avoids creating multiple distinct sessions for the end-customer. In admin mode, it allows creating named guest sessions or activating existing ones.
    -   Provides cart-related mutations (`addItemToCartMutation`, etc.) for the active customer session.
    -   Exposes `hasActiveCustomerSession` (boolean) and `currentCustomerName`.
    -   Persists customer sessions and active ID to `localStorage`. Includes `isClientMounted` state to guard `localStorage` access and provides `getCartDetails` for comprehensive cart data.

### Data
-   `src/app/mockData.ts` - Mock product data, now including `mrp` (Maximum Retail Price) for products and variants.

### Configuration
-   `next.config.ts` - Next.js configuration file.
-   `tsconfig.json` - TypeScript configuration.
-   `postcss.config.mjs` - PostCSS configuration for Tailwind CSS.

## Key Features

### User Authentication (Operator Login)
-   The **user** (e.g., Bhuvanesh, the portal operator) logs in using a dedicated "Account" button in the `Header`.
-   Clicking "Login" opens the `LoginModal`.
-   The login process involves:
    1.  Entering a mobile number.
    2.  Receiving and verifying an OTP.
-   Successful login stores user details and a JWT token via `AuthContext`.
-   The Header's "Account" button updates to show the user's name and provides a dropdown/sheet with profile/logout options.
-   This user authentication is distinct from customer sessions.
-   This entire user (operator) login mechanism, including the 'Account' button in the `Header` and associated modals/sheets, is not available or visible in the **customer-facing mode** (`infinity.wheelocity.com`).

### Header
-   Sticky and responsive, adapting its layout for desktop/tablet and mobile views.
-   **Desktop/Tablet:** Displays Infinity Portal logo, centered `SearchBar`, `LanguageSwitcher`.
    -   The "Account" button/dropdown reflects the **user's** (operator's) login status (hidden in customer-facing mode).
    -   Cart button provides access to the active **customer's** cart.
-   **Mobile:**
    -   **Top Bar:** Infinity Portal logo (home link), active **customer** session name (truncated if long, hidden in customer-facing mode), and a "More" (ellipsis) icon triggering `MobileMoreMenu.tsx`.
    -   **Bottom Navigation Bar (`MobileBottomNav.tsx`):** Appears when a customer session is active. Shows 'Search' and 'Cart'. 'Sessions' and 'Account' icons are hidden in customer-facing mode.
-   **Cart Functionality:**
    -   Header cart button (desktop/tablet) or mobile bottom nav cart icon displays item count for the active **customer**.
    -   Toggles the `CartDrawer.tsx`, showing detailed **customer** cart items, totals (price, MRP, savings), and a "Proceed to Checkout" / "Login to Proceed" button. The "Login to Proceed" behavior depends on whether the **user** (operator) is logged in and if a **customer** session is active. (Note: User login is not possible in customer-facing mode).

### Session Management (Customer Sessions)
-   Session management behavior differs significantly based on the application mode (controlled by the `NEXT_PUBLIC_APP_MODE` environment variable defined in `lib/utils.ts` and `.env` files).
-   Managed by `SessionContext.tsx`.

    **In Admin Mode (`infinity-admin.wheelocity.com`):**
    *   Allows the logged-in **user** (operator) to manage multiple **customer** (e.g., Bhoomi) sessions.
    *   Uses Chrome-style tabs (`SessionManager.tsx`) for managing these customer sessions on desktop/tablet.
    *   Each customer session maintains its own cart state.
    *   Users can switch between customer sessions, add new sessions (e.g., "Guest Customer" or named), or close existing ones.
    *   Customer sessions and `activeSessionId` are persisted in `localStorage` to maintain state across browser refreshes, with `Date` objects handled for `lastActive`.
    *   Mobile view uses `MobileSessionsSheet.tsx` (bottom sheet) for vertical customer session management (list, select, add, edit, delete).

    **In Customer Mode (`infinity.wheelocity.com`):**
    *   The UI for managing multiple sessions (tabs, mobile sheets for sessions) is hidden.
    *   A single, default session (e.g., "My Session" - from i18n key `t('session.defaultCustomerName')`) is automatically created by `SessionContext` if one doesn't exist (e.g., on first visit) and is used for all cart interactions for the **customer** ('Bhoomi').
    *   The end-customer is not exposed to session creation or switching UIs.
    *   The default session and its `activeSessionId` are persisted in `localStorage`.

### Product Catalog & Display
-   Products displayed in horizontally scrollable `CategorySection.tsx` components, each with a "View More &rarr;" link and navigation chevrons.
-   `ProductCard.tsx` revamped UI:
    -   Displays discount ribbon, variant name (e.g., "5 kg"), current price, and slashed MRP.
    -   Conditional "ADD" button (adds to cart) vs. `-/+` quantity controls (updates cart).
    -   Entire card (image, name) links to product details page (`/products/{category}/{productId}?variant={variantId}`).
    -   Clicking variant name/quantity area (if multiple variants) opens a variant selection modal.

### Search Functionality
-   Global search across all product categories via `SearchBar.tsx`.
-   Real-time filtering as user types.
-   Dropdown results showing product name, category, image, price, MRP, and variant name.
-   Mobile view utilizes `MobileSearchSheet.tsx`, a top-sliding sheet with autofocus on the search input.

### Localization
-   Support for multiple languages (English, Hindi, Kannada, Tamil) via `react-i18next`.
-   Desktop/tablet uses `LanguageSwitcher.tsx` component.
-   Mobile language selection is integrated directly within the `MobileMoreMenu.tsx` bottom sheet.

### General UI/UX Enhancements
-   **Full-width Content Layout:** Application content generally spans the full width of the viewport, with appropriate padding.
-   **Color Scheme:** Consistent use of a green accent color (`var(--color-green-600)`) for primary calls-to-action (e.g., Add to Cart, Proceed buttons, active item counters) and neutral grays for other UI elements to ensure visual consistency.
-   **Responsiveness:** Significant effort in creating distinct and user-friendly experiences for mobile devices, including dedicated sheet components for major interactions.

## Component Relationships
-   `layout.tsx` likely renders `Header.tsx`.
-   `Header.tsx` uses `AuthContext` (for **user** login state) and `SessionContext` (for **customer** session state) and `isCustomerFacingMode()` from `lib/utils` to adapt its rendering.
-   `Header.tsx` conditionally renders `LoginModal` for **user** login (not in customer-facing mode).
-   `Header.tsx` passes **user**-specific props (from `AuthContext`) to `MobileAccountSheet` and `MobileMoreMenu`.
-   `Header.tsx` passes **customer** session-specific props (from `SessionContext`) and mode-related props to `SessionManager`, `MobileSessionsSheet`, `MobileBottomNav`, and `CartDrawer`.
-   `LoginModal.tsx` uses `AuthContext` to perform **user** login.
-   `page.tsx` renders multiple `CategorySection` components.
-   `CategorySection.tsx` renders multiple `ProductCard.tsx` components.
-   Components like `CategorySection`, `ProductCard` might use `SessionContext` for cart interactions related to the active **customer**.

## State Management
-   **User Authentication State:** Managed by `AuthContext.tsx`. Includes `isUserLoggedIn`, `userDetails` (for the **user**, e.g., Bhuvanesh), JWT token, and related auth functions. Persisted to `localStorage`. This is primarily relevant for the admin mode.
-   **Customer Session State:** Managed by `SessionContext.tsx`. Includes list of **customer** `sessions`, `activeSessionId`, cart data per session, and functions to manage these sessions (e.g., `hasActiveCustomerSession`, `currentCustomerName`, `startOrActivateCustomerSession`). Persisted to `localStorage`. The initialization and management of sessions by `SessionContext.tsx` are mode-dependent. In **customer-facing mode**, it ensures a default session is automatically created and made active if no session exists, simplifying the experience.
-   **Application Mode State:** Determined by the `NEXT_PUBLIC_APP_MODE` environment variable and accessed via helper functions in `src/lib/utils.ts`. This influences rendering and behavior globally.
-   UI state (dropdowns, active tabs, sheet visibility) is managed through local React state (`useState`) within components.
-   Product data is currently from `mockData.ts` but structured for easy API integration.
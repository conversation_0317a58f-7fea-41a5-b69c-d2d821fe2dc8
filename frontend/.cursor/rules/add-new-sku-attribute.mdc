---
description: This rule outlines on how to add new field in sku cataloging
globs: 
alwaysApply: false
---
# Add New SKU Attribute - Automated Cursor Rule

## Rule Trigger
When user requests to add new fields/attributes to SKU cataloging system.

## Trigger Patterns
- "I want to add [field] to sku"
- "Add [field] field to sku cataloging"
- "New sku attribute [field]"
- "Add [field] and [field] to sku"

## Automated Implementation Process

### Step 1: Parse User Request
Extract field information from user request:
- Field names (e.g., "size", "color") 
- Field types (string by default, or infer from context)
- Optional validation rules
- Whether field applies to parent, child, or both SKU types

### Step 2: Update Type Definitions

**File: `src/app/types/sku.ts`**

Add to SKU interface:
```typescript
export interface SKU {
    // ... existing fields ...
    {FIELD_NAME}?: {FIELD_TYPE}; // {FIELD_DESCRIPTION}
}
```

Add to FlattenedSKU interface:
```typescript
export interface FlattenedSKU {
    // ... existing fields ...
    {FIELD_NAME}?: {FIELD_TYPE}; // {FIELD_DESCRIPTION} (flat for CSV)
}
```

Add to SKUVariant interface (if applicable):
```typescript
export interface SKUVariant {
    // ... existing fields ...
    {FIELD_NAME}?: {FIELD_TYPE}; // {FIELD_DESCRIPTION} (variant override)
}
```

Add to SKUJsonPayload interface:
```typescript
export interface SKUJsonPayload {
    // ... existing fields ...
    {FIELD_NAME}?: {FIELD_TYPE}; // {FIELD_DESCRIPTION} (API payload)
}
```

### Step 3: Update Export Function

**File: `src/app/services/skuService.ts`**

In `flattenSkusForExport()` function, add to the flattened.push() object:
```typescript
flattened.push({
    // ... existing fields ...
    {FIELD_NAME}: {EXPORT_LOGIC},
});
```

Export logic patterns:
- String: `sanitizeForCSV(sku.{FIELD_NAME} || '')`
- Number: `sku.{FIELD_NAME} || 0`
- Boolean: `sku.{FIELD_NAME} ? 'true' : 'false'`
- Array: `(sku.{FIELD_NAME} || []).join(',')`

### Step 4: Update Import Function

**File: `src/app/services/skuService.ts`**

In `nestSkusFromImport()` function:

For Parent SKUs:
```typescript
const parentSku: SKU = {
    // ... existing fields ...
    {FIELD_NAME}: {IMPORT_LOGIC},
};
```

For Child SKUs:
```typescript
const childSku: SKU = {
    // ... existing fields ...
    {FIELD_NAME}: {IMPORT_LOGIC},
};
```

For Variants (if applicable):
```typescript
parent.variants?.push({
    // ... existing fields ...
    {FIELD_NAME}: childSku.{FIELD_NAME},
});
```

Import logic patterns:
- String: `(childFlat.{FIELD_NAME} || '').trim()`
- Number: `childFlat.{FIELD_NAME} || 0`
- Boolean: `childFlat.{FIELD_NAME} === 'true'`
- Array: `childFlat.{FIELD_NAME} ? childFlat.{FIELD_NAME}.split(',').filter(item => item.trim()) : []`

### Step 5: Update Template Service

**File: `src/app/services/templateService.ts`**

Add column definition in sku-complete template:
```typescript
{
    key: '{FIELD_NAME}',
    header: '{FIELD_DISPLAY_NAME}',
    type: '{FIELD_TYPE}',
    width: {FIELD_WIDTH},
    required: false,
    {VALIDATION_RULES}
},
```

Update sample data:
```typescript
sampleData: [
    {
        // ... existing parent sample ...
        {FIELD_NAME}: '{PARENT_SAMPLE_VALUE}',
    },
    {
        // ... existing child sample ...
        {FIELD_NAME}: '{CHILD_SAMPLE_VALUE}',
    }
],
```

Update instructions:
```typescript
instructions: [
    // ... existing instructions ...
    '{FIELD_DISPLAY_NAME}: {FIELD_DESCRIPTION} {VALIDATION_NOTES}',
]
```

### Step 6: Update SKU Operations Service

**File: `src/app/services/skuOperationsService.ts`**

In `transformForApi()` method:
```typescript
private static transformForApi(skus: SKU[]): SKU[] {
    return skus.map(sku => {
        const transformed = { ...sku };
        
        // Ensure {FIELD_NAME} has proper defaults
        if (!transformed.{FIELD_NAME}) {
            transformed.{FIELD_NAME} = {DEFAULT_VALUE};
        }
        
        return transformed;
    });
}
```

### Step 7: Add Validation

**File: `src/app/services/skuService.ts`**

In `validateFlattenedSkus()` function:
```typescript
// Validate {FIELD_NAME}
{VALIDATION_LOGIC}
```

Validation patterns:
- String length: `if (sku.{FIELD_NAME} && sku.{FIELD_NAME}.length > {MAX_LENGTH}) { errors.push(\`SKU \${sku.skuId}: {FIELD_NAME} cannot exceed {MAX_LENGTH} characters\`); }`
- Number range: `if (sku.{FIELD_NAME} !== undefined && (sku.{FIELD_NAME} < {MIN} || sku.{FIELD_NAME} > {MAX})) { errors.push(\`SKU \${sku.skuId}: {FIELD_NAME} must be between {MIN} and {MAX}\`); }`
- Required field: `if (!sku.{FIELD_NAME}) { errors.push(\`SKU \${sku.skuId}: {FIELD_NAME} is required\`); }`
- Enum validation: `if (sku.{FIELD_NAME} && !['value1', 'value2'].includes(sku.{FIELD_NAME})) { errors.push(\`SKU \${sku.skuId}: {FIELD_NAME} must be one of: value1, value2\`); }`

### Step 8: Create Test Cases

**File: `src/app/services/__tests__/{FIELD_NAME}.test.ts`**

```typescript
import { flattenSkusForExport, nestSkusFromImport } from '../skuService';
import { SKU, FlattenedSKU } from '../../types/sku';

describe('{FIELD_NAME} Field Integration', () => {
    const mockSku: SKU = {
        skuId: 1,
        name: { en: 'Test Product', ta: 'சோதனை தயாரிப்பு' },
        imageUrl: 'test.jpg',
        images: [],
        description: { en: 'Test Description', ta: 'சோதனை விளக்கம்' },
        type: 'child',
        isActive: 1,
        costPrice: 80,
        sellingPrice: 100,
        mrp: 120,
        {FIELD_NAME}: {TEST_VALUE},
    };

    const mockFlatSku: FlattenedSKU = {
        skuId: 1,
        nameEn: 'Test Product',
        nameTa: 'சோதனை தயாரிப்பு',
        imageUrl: 'test.jpg',
        images: '',
        descriptionEn: 'Test Description',
        descriptionTa: 'சோதனை விளக்கம்',
        type: 'child',
        status: 'active',
        costPrice: 80,
        sellingPrice: 100,
        mrp: 120,
        {FIELD_NAME}: {TEST_VALUE},
    };

    test('should export {FIELD_NAME} field correctly', () => {
        const flattened = flattenSkusForExport([mockSku]);
        expect(flattened[0].{FIELD_NAME}).toBe({EXPECTED_EXPORT_VALUE});
    });

    test('should import {FIELD_NAME} field correctly', () => {
        const nested = nestSkusFromImport([mockFlatSku]);
        expect(nested[0].{FIELD_NAME}).toBe({EXPECTED_IMPORT_VALUE});
    });

    test('should handle empty {FIELD_NAME} gracefully', () => {
        const skuWithoutField = { ...mockSku };
        delete skuWithoutField.{FIELD_NAME};
        
        const flattened = flattenSkusForExport([skuWithoutField]);
        expect(flattened[0].{FIELD_NAME}).toBe({DEFAULT_EXPORT_VALUE});
        
        const nested = nestSkusFromImport(flattened);
        expect(nested[0].{FIELD_NAME}).toBe({DEFAULT_IMPORT_VALUE});
    });

    {ADDITIONAL_VALIDATION_TESTS}
});
```

### Step 9: Run Tests

Execute the following commands:
```bash
npm test -- {FIELD_NAME}.test.ts
npm run build
```

Verify all tests pass and build succeeds.

## Field Type Mappings

### String Fields
- **Type**: `string`
- **Default**: `''`
- **Export**: `sanitizeForCSV(sku.{FIELD_NAME} || '')`
- **Import**: `(flatSku.{FIELD_NAME} || '').trim()`
- **Width**: `20`
- **Validation**: Length checks, pattern matching

### Numeric Fields  
- **Type**: `number`
- **Default**: `0` or `undefined`
- **Export**: `sku.{FIELD_NAME} || 0`
- **Import**: `flatSku.{FIELD_NAME} || 0`
- **Width**: `15`
- **Validation**: Range checks, decimal precision

### Boolean Fields
- **Type**: `boolean`
- **Default**: `false`
- **Export**: `sku.{FIELD_NAME} ? 'true' : 'false'`
- **Import**: `flatSku.{FIELD_NAME} === 'true'`
- **Width**: `10`
- **Validation**: True/false string validation

### Array Fields
- **Type**: `string[]`
- **Default**: `[]`
- **Export**: `(sku.{FIELD_NAME} || []).join(',')`
- **Import**: `flatSku.{FIELD_NAME} ? flatSku.{FIELD_NAME}.split(',').filter(item => item.trim()) : []`
- **Width**: `30`
- **Validation**: Array length, element validation

## Example Implementation

### User Input:
"I want to add two fields in sku cataloging - size and color"

### Automated Actions:

#### Fields Detected:
- `size` (string field for product dimensions)
- `color` (string field for product color)

#### Files Modified:
1. `src/app/types/sku.ts` - Add size? and color? to all interfaces
2. `src/app/services/skuService.ts` - Update export/import functions  
3. `src/app/services/templateService.ts` - Add columns and sample data
4. `src/app/services/skuOperationsService.ts` - Add transform logic
5. `src/app/services/__tests__/size.test.ts` - Create size tests
6. `src/app/services/__tests__/color.test.ts` - Create color tests

#### Implementation Details:

**Type Definitions:**
```typescript
// SKU Interface
size?: string; // Product size (e.g., 'Small', 'Medium', 'Large')
color?: string; // Product color (e.g., 'Red', 'Blue', 'Green')

// FlattenedSKU Interface  
size?: string; // Product size (flat for CSV)
color?: string; // Product color (flat for CSV)
```

**Export Logic:**
```typescript
size: sanitizeForCSV(sku.size || ''),
color: sanitizeForCSV(sku.color || ''),
```

**Import Logic:**
```typescript
size: (childFlat.size || '').trim(),
color: (childFlat.color || '').trim(),
```

**Template Columns:**
```typescript
{
    key: 'size',
    header: 'Size',
    type: 'string',
    width: 15,
    required: false
},
{
    key: 'color', 
    header: 'Color',
    type: 'string',
    width: 15,
    required: false
},
```

**Sample Data:**
```typescript
// Parent SKU
size: '',
color: '',

// Child SKU
size: 'Medium',
color: 'Blue',
```

**Validation:**
```typescript
// Validate size
if (sku.size && sku.size.length > 50) {
    errors.push(`SKU ${sku.skuId}: Size cannot exceed 50 characters`);
}

// Validate color
if (sku.color && sku.color.length > 30) {
    errors.push(`SKU ${sku.skuId}: Color cannot exceed 30 characters`);
}
```

## Execution Steps

1. **Parse Request**: Extract field names and infer types
2. **Update Types**: Add fields to all relevant interfaces
3. **Update Services**: Modify export, import, and transform functions
4. **Update Templates**: Add columns, sample data, and instructions
5. **Add Validation**: Include field validation rules
6. **Create Tests**: Generate comprehensive test cases
7. **Verify Build**: Ensure all changes compile successfully
8. **Run Tests**: Validate functionality works correctly

## Success Criteria

- [ ] All TypeScript interfaces updated
- [ ] Export function includes new fields
- [ ] Import function reconstructs new fields
- [ ] Template service defines new columns
- [ ] Validation includes new field checks
- [ ] Test cases cover new fields
- [ ] Build completes successfully
- [ ] All tests pass
- [ ] Round-trip export/import maintains data integrity

## Error Handling

If any step fails:
1. **Type Errors**: Check interface consistency across all files
2. **Export Issues**: Verify sanitization and default value logic
3. **Import Issues**: Check data type conversion and trimming
4. **Template Issues**: Validate column definitions and sample data
5. **Test Failures**: Review test data and expected values
6. **Build Failures**: Check for syntax errors and missing imports

## Notes

- Always make new fields optional to maintain backwards compatibility
- Use consistent naming conventions (camelCase)
- Provide sensible defaults for all field types
- Include comprehensive validation for data integrity
- Test with realistic data samples
- Consider performance impact for large datasets
- Update documentation after implementation


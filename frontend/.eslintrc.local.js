// .eslintrc.local.js
// This file can be used for local development with more relaxed rules
// To use this, rename it to .eslintrc.js (but be careful not to commit it)

module.exports = {
    extends: ['./eslint.config.mjs'],
    rules: {
        // Even more relaxed rules for local development
        'no-console': 'off', // Allow console.log during development
        '@typescript-eslint/no-unused-vars': 'off', // Turn off unused vars completely
        '@typescript-eslint/no-explicit-any': 'off', // Allow any types during development
        'react-hooks/exhaustive-deps': 'off', // Turn off exhaustive deps warnings
        'prefer-const': 'off', // Allow let instead of const
    }
}; 
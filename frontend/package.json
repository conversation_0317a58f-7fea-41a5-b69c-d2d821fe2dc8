{"name": "frontend", "version": "0.3.3", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "build:customer": "npm run build && npm run remove-admin", "remove-admin": "node scripts/remove-admin.js", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "lint:quiet": "next lint --quiet", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@heroicons/react": "^2.2.0", "@microsoft/clarity": "^1.0.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.76.1", "@tanstack/react-query-devtools": "^5.79.0", "@types/react-datepicker": "^6.2.0", "@types/uuid": "^10.0.0", "@uidotdev/usehooks": "^2.4.1", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dompurify": "^3.2.6", "i18next": "^25.2.0", "localforage": "^1.10.0", "lodash": "^4.17.21", "loglevel": "^1.9.2", "lucide-react": "^0.523.0", "next": "15.3.2", "next-i18next": "^15.4.2", "next-themes": "^0.4.6", "point-in-polygon-hao": "^1.2.4", "posthog-js": "^1.249.5", "react": "^19.0.0", "react-confetti": "^6.4.0", "react-datepicker": "^8.4.0", "react-day-picker": "^9.7.0", "react-dom": "^19.0.0", "react-i18next": "^15.5.1", "react-modal": "^3.16.3", "react-toastify": "^11.0.5", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "uuid": "^11.1.0", "xlsx": "^0.18.5", "zod": "^3.25.64", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@playwright/test": "^1.53.1", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/dompurify": "^3.0.5", "@types/jest": "^29.5.14", "@types/localforage": "^0.0.33", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-modal": "^3.16.3", "@types/xlsx": "^0.0.35", "eslint": "^9", "eslint-config-next": "15.3.2", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.0-beta.3", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5"}, "packageManager": "pnpm@10.10.0+sha512.d615db246fe70f25dcfea6d8d73dee782ce23e2245e3c4f6f888249fb568149318637dca73c2c5c8ef2a4ca0d5657fb9567188bfab47f566d1ee6ce987815c39"}
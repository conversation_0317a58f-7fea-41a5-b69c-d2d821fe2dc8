# SKU Field Addition Guide

## Overview

This guide provides a comprehensive checklist for adding new fields to the SKU system. The SKU system supports parent-child relationships and import/export functionality, so any new field must be properly integrated across multiple layers.

## When to Add New SKU Fields

- **Product Attributes**: Physical properties like size, color, weight, dimensions
- **Business Data**: Supplier information, procurement details, shelf life
- **Display Properties**: Additional images, videos, marketing copy
- **Operational Data**: Warehouse location, handling instructions

## Required Changes Checklist

### 1. Type Definitions (`src/app/types/sku.ts`)

#### ✅ Main SKU Interface
```typescript
export interface SKU {
    // ... existing fields ...
    newField?: string; // Add your new field here
    anotherField?: number; // Example of different type
}
```

#### ✅ FlattenedSKU Interface (for Import/Export)
```typescript
export interface FlattenedSKU {
    // ... existing fields ...
    newField?: string; // Flat version for CSV
    anotherField?: number; // Numbers remain the same
}
```

#### ✅ SKUVariant Interface (if field applies to variants)
```typescript
export interface SKUVariant {
    // ... existing fields ...
    newField?: string; // Add if variants can override this field
}
```

#### ✅ SKUJsonPayload Interface (for API)
```typescript
export interface SKUJsonPayload {
    // ... existing fields ...
    newField?: string; // For API communication
}
```

### 2. Export Function (`src/app/services/skuService.ts`)

#### ✅ Update `flattenSkusForExport()`
```typescript
export function flattenSkusForExport(skus: SKU[]): FlattenedSKU[] {
    // ... existing code ...
    
    flattened.push({
        // ... existing fields ...
        newField: sanitizeForCSV(sku.newField || ''),
        anotherField: sku.anotherField || 0,
    });
}
```

**Considerations:**
- Use `sanitizeForCSV()` for string fields
- Provide sensible defaults for missing values
- Consider if field should be type-specific (parent vs child)

### 3. Import Function (`src/app/services/skuService.ts`)

#### ✅ Update `nestSkusFromImport()`

**For Child SKUs:**
```typescript
const childSku: SKU = {
    // ... existing fields ...
    newField: (childFlat.newField || '').trim(),
    anotherField: childFlat.anotherField || 0,
};
```

**For Parent SKUs:**
```typescript
const parentSku: SKU = {
    // ... existing fields ...
    newField: (parentFlat.newField || '').trim(),
    anotherField: parentFlat.anotherField || 0,
};
```

**For Variants (if applicable):**
```typescript
parent.variants?.push({
    // ... existing fields ...
    newField: childSku.newField, // Include in variant if needed
});
```

### 4. Template Service (`src/app/services/templateService.ts`)

#### ✅ Add Column Definition
```typescript
{
    key: 'newField',
    header: 'New Field Label',
    type: 'string', // or 'number', 'boolean'
    width: 20,
    required: false, // or true if mandatory
    validation: {
        options: ['option1', 'option2'] // if dropdown values
    }
},
```

#### ✅ Update Sample Data
```typescript
sampleData: [
    {
        // ... existing sample data ...
        newField: 'Sample Value',
        anotherField: 100,
    }
],
```

#### ✅ Update Instructions
```typescript
instructions: [
    // ... existing instructions ...
    'New Field: Description of what this field is for and valid values',
    'Another Field: Numeric field with range 0-1000',
]
```

### 5. SKU Operations Service (`src/app/services/skuOperationsService.ts`)

#### ✅ Update `transformForApi()`
```typescript
private static transformForApi(skus: SKU[]): SKU[] {
    return skus.map(sku => {
        const transformed = { ...sku };
        
        // Ensure new field has proper defaults
        if (sku.type === 'child' && !transformed.newField) {
            transformed.newField = 'default_value';
        }
        
        return transformed;
    });
}
```

### 6. Validation (`src/app/services/skuService.ts`)

#### ✅ Update `validateFlattenedSkus()`
```typescript
export function validateFlattenedSkus(flattenedSkus: FlattenedSKU[]): ValidationResult {
    // ... existing validation ...
    
    for (const sku of normalizedSkus) {
        // Validate new field
        if (sku.newField && sku.newField.length > 100) {
            errors.push(`SKU ${sku.skuId}: New field cannot exceed 100 characters`);
        }
        
        // Validate another field
        if (sku.anotherField !== undefined && (sku.anotherField < 0 || sku.anotherField > 1000)) {
            errors.push(`SKU ${sku.skuId}: Another field must be between 0 and 1000`);
        }
    }
}
```

### 7. Form Components (Optional UI Updates)

#### ✅ Update `src/app/components/SkuForm.tsx`
```typescript
// Add form field for new attribute
<div className="form-group">
    <label htmlFor="newField">New Field</label>
    <input
        id="newField"
        type="text"
        value={formData.newField || ''}
        onChange={(e) => setFormData({...formData, newField: e.target.value})}
    />
</div>
```

#### ✅ Update SKU View Page `src/app/admin/skus/[id]/page.tsx`
```typescript
// Display new field in view page
{sku.newField && (
    <div className="field-display">
        <span className="label">New Field:</span>
        <span className="value">{sku.newField}</span>
    </div>
)}
```

#### ✅ Update SKU Table `src/app/admin/skus/page.tsx`
```typescript
// Add column to table if needed
<td className="px-6 py-4">
    <div className="text-sm text-gray-900">{sku.newField}</div>
</td>
```

### 8. Test Cases

#### ✅ Create Test File `src/app/services/__tests__/newField.test.ts`
```typescript
import { flattenSkusForExport, nestSkusFromImport } from '../skuService';
import { SKU, FlattenedSKU } from '../../types/sku';

describe('New Field Integration', () => {
    test('should export new field correctly', () => {
        const sku: SKU = {
            skuId: 1,
            name: { en: 'Test', ta: '' },
            newField: 'test_value',
            // ... other required fields
        };
        
        const flattened = flattenSkusForExport([sku]);
        expect(flattened[0].newField).toBe('test_value');
    });
    
    test('should import new field correctly', () => {
        const flatSku: FlattenedSKU = {
            skuId: 1,
            nameEn: 'Test',
            nameTa: '',
            newField: 'imported_value',
            // ... other required fields
        };
        
        const nested = nestSkusFromImport([flatSku]);
        expect(nested[0].newField).toBe('imported_value');
    });
});
```

## Field Type Considerations

### String Fields
- Use `sanitizeForCSV()` in export
- Trim whitespace in import
- Consider length validation
- Handle localization if needed

### Numeric Fields
- Provide sensible defaults (0, undefined)
- Add range validation
- Consider decimal precision

### Boolean Fields
- Use string representation in CSV ('true'/'false')
- Convert properly in import/export
- Provide clear default behavior

### Array Fields
- Use comma-separated values in CSV
- Split/join properly in import/export
- Validate array contents

### Complex Objects
- Consider flattening approach
- May need multiple CSV columns
- JSON string as last resort

## Best Practices

### 1. Backwards Compatibility
- Always make new fields optional
- Provide sensible defaults
- Test with existing data

### 2. Data Validation
- Add client-side validation
- Include server-side validation expectations
- Provide clear error messages

### 3. Performance
- Consider impact on large datasets
- Test import/export with thousands of SKUs
- Optimize database queries if needed

### 4. User Experience
- Update form validation messages
- Add help text for new fields
- Consider field ordering in forms

### 5. Documentation
- Update API documentation
- Add examples to templates
- Update user guides

## Common Pitfalls

### ❌ Missing Validation
```typescript
// Wrong - no validation
newField: flatSku.newField,

// Right - with validation
newField: (flatSku.newField || '').trim(),
```

### ❌ Inconsistent Defaults
```typescript
// Wrong - different defaults in different places
newField: sku.newField || '',     // in export
newField: sku.newField || 'N/A',  // in import

// Right - consistent defaults
newField: sku.newField || '',     // everywhere
```

### ❌ Missing CSV Sanitization
```typescript
// Wrong - no sanitization
newField: sku.newField,

// Right - with sanitization
newField: sanitizeForCSV(sku.newField || ''),
```

### ❌ Forgetting Variants
```typescript
// Wrong - not including in variants
parent.variants?.push({
    skuId: childSku.skuId,
    name: childSku.name,
    // missing newField
});

// Right - including all relevant fields
parent.variants?.push({
    skuId: childSku.skuId,
    name: childSku.name,
    newField: childSku.newField,
});
```

## Testing Checklist

- [ ] Export includes new field
- [ ] Import reconstructs new field correctly
- [ ] Validation catches invalid values
- [ ] Form saves new field properly
- [ ] View page displays new field
- [ ] Table shows new field (if applicable)
- [ ] Template includes new field
- [ ] Round-trip export → import → export maintains data
- [ ] Empty/null values handled gracefully
- [ ] Large datasets perform acceptably

## Example: Adding 'color' and 'size' Fields

See the automated cursor rule `add-new-sku-attribute.mdc` for a complete example of adding these fields automatically.

## Support

If you encounter issues:
1. Check this guide first
2. Test with sample data
3. Verify all checklist items
4. Check browser console for errors
5. Review similar existing fields for patterns 
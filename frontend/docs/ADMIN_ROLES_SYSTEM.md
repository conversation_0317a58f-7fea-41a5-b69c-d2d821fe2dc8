# Admin Roles and Permissions System

This document describes the admin roles and permissions system implemented for the application.

## Overview

The admin roles system provides fine-grained access control for administrative features. It includes:

- **Permission-based access control** - Check specific permissions like `manageUsers`, `editSales`, etc.
- **Role-based access control** - Check user roles like `ADMIN`, `MANAGER`, etc.
- **Repository-based storage** - Persistent storage using IndexedDB/localStorage
- **React hooks and components** - Easy integration with React components

## Architecture

### Core Components

1. **Types** (`src/app/types/auth.ts`)
   - `AdminPermissions` - Interface defining all available permissions
   - `AdminRolesData` - Contains permissions and role names
   - `AdminRolesResponse` - API response structure

2. **Repository** (`src/app/repository/AuthRepository.ts`)
   - `saveAdminRoles()` - Save admin roles to storage
   - `getAdminRoles()` - Retrieve admin roles from storage
   - `clearAdminRoles()` - Clear admin roles data

3. **Service** (`src/app/services/authService.ts`)
   - `getAdminRoles()` - Fetch admin roles from API endpoint

4. **Context** (`src/app/context/AuthContext.tsx`)
   - Manages admin roles state
   - Provides helper functions for permission checking

5. **Custom Hook** (`src/app/hooks/useAdminPermissions.ts`)
   - Convenient access to admin functionality
   - Helper methods for common permission checks

6. **Guard Component** (`src/app/components/common/AdminGuard.tsx`)
   - Conditional rendering based on permissions/roles

## API Integration

### Endpoint
- **URL**: `/userApp-user-getAdminRolesForUser`
- **Method**: POST
- **Headers**: Authorization Bearer token (handled automatically)

### Response Format
```json
{
  "status": true,
  "data": {
    "permissions": {
      "byPass": true,
      "manageUsers": true,
      "editSales": false,
      // ... other permissions
    },
    "roleNames": ["ADMIN"]
  }
}
```

## Usage Examples

### 1. Using the Hook

```tsx
import { useAdminPermissions } from '../hooks/useAdminPermissions';

const MyComponent = () => {
    const { 
        isAdmin, 
        checkPermission, 
        loadAdminRoles,
        isLoadingAdminRoles 
    } = useAdminPermissions();

    // Load admin roles when component mounts
    useEffect(() => {
        if (isAdmin()) {
            loadAdminRoles();
        }
    }, []);

    // Check specific permission
    if (checkPermission('manageUsers')) {
        return <UserManagementPanel />;
    }

    return <div>Access denied</div>;
};
```

### 2. Using the AdminGuard Component

```tsx
import { AdminGuard } from '../components/common/AdminGuard';

// Require specific permission
<AdminGuard requiredPermission="manageUsers">
    <UserManagementPanel />
</AdminGuard>

// Require any admin role
<AdminGuard requireAdmin>
    <AdminDashboard />
</AdminGuard>

// Require multiple permissions (ALL)
<AdminGuard requiredPermissions={["manageUsers", "editSales"]}>
    <AdvancedAdminPanel />
</AdminGuard>

// Require any of the permissions
<AdminGuard anyPermissions={["viewAndEditPjp", "pjpTracking"]}>
    <PjpManagement />
</AdminGuard>

// With fallback content
<AdminGuard 
    requiredPermission="manageUsers"
    fallback={<div>You don't have permission to view this.</div>}
>
    <UserManagementPanel />
</AdminGuard>
```

### 3. Using the Context Directly

```tsx
import { useAuth } from '../context/AuthContext';

const MyComponent = () => {
    const { 
        adminRoles, 
        hasPermission, 
        hasRole, 
        loadAdminRoles 
    } = useAuth();

    // Check permission
    const canManageUsers = hasPermission('manageUsers');
    
    // Check role
    const isAdmin = hasRole('ADMIN');

    // Load admin roles
    const handleLoadRoles = async () => {
        const success = await loadAdminRoles();
        if (success) {
            console.log('Admin roles loaded successfully');
        }
    };

    return (
        <div>
            {canManageUsers && <button>Manage Users</button>}
            {isAdmin && <button>Admin Panel</button>}
        </div>
    );
};
```

## Available Permissions

Based on the API response, the following permissions are available:

- `byPass` - Bypass certain restrictions
- `idencity` - Identity management
- `editSales` - Edit sales data
- `otpByPass` - Bypass OTP verification
- `pjpRoster` - PJP roster management
- `incentives` - Incentives management
- `userRoster` - User roster management
- `manageUsers` - User management
- `pjpTracking` - PJP tracking
- `updatePrice` - Price updates
- `blockPreOrder` - Block pre-orders
- `configManager` - Configuration management
- `posConfigView` - POS configuration view
- `manageSpeakers` - Speaker management
- `viewAndEditPjp` - View and edit PJP
- `bulkSupplyOrder` - Bulk supply orders
- `firebaseConfigs` - Firebase configurations
- `manageTerminals` - Terminal management
- `habitationSurvey` - Habitation surveys
- `incentivePayment` - Incentive payments
- `salesPreparation` - Sales preparation
- `habitatSurveySlot` - Habitat survey slots
- `projectClearSlots` - Project clear slots
- `commonImportExport` - Import/export functionality
- `cardDistributionSlot` - Card distribution slots
- `supplyOrderConfigView` - Supply order configuration view
- `editWastageAndStockRejection` - Edit wastage and stock rejection

## Implementation Notes

1. **Lazy Loading**: Admin roles are only fetched when needed (when user visits admin pages)
2. **Persistence**: Admin roles are stored in IndexedDB/localStorage for offline access
3. **Security**: All permission checks should be backed by server-side validation
4. **Performance**: Permission checks are optimized with memoization
5. **Error Handling**: Graceful fallbacks when admin roles fail to load

## Security Considerations

- **Client-side only**: This system is for UI control only
- **Server validation required**: Always validate permissions on the server
- **Token expiry**: Admin roles are cleared when user logs out
- **Fallback behavior**: Components should gracefully handle missing permissions

## Future Enhancements

- **Permission groups**: Group related permissions for easier management
- **Dynamic permissions**: Support for runtime permission updates
- **Audit logging**: Track permission usage for security auditing
- **Permission inheritance**: Support for hierarchical permission structures 
# SKU Service Migration Guide

## Overview

The SKU management system has been unified under a new `SkuOperationsService` to ensure consistent behavior across all pages (homepage, edit, create). This migration guide helps you understand the changes and how to update existing code.

## Key Changes

### 1. Service Consolidation

**Before (Inconsistent):**
- Homepage: Direct `upsertSkus()` calls
- Edit/Create pages: `SkuFormService.saveSku()`

**After (Unified):**
- All pages: `SkuOperationsService` methods

### 2. Missing Field Fixes

**Critical Fix:** The `variantName` field was missing from homepage API calls, causing data loss.

**Before (Homepage):**
```typescript
// ❌ Missing variantName field in API calls
await upsertSkus(modifiedSkus);
```

**After (Homepage):**
```typescript
// ✅ All fields preserved, proper parent-child sync
await SkuOperationsService.saveBulkSkus(skusToSave, operation);
```

### 3. Display Enhancements

**Before:**
```typescript
// ❌ Child SKUs showed only name
<div>{sku.name.en}</div>
```

**After:**
```typescript
// ✅ Child SKUs show variant names
<div>{sku.name.en}</div>
{sku.type === 'child' && sku.variantName && (
    <div className="text-xs text-blue-600 mt-1">
        Variant: {sku.variantName.en}
    </div>
)}
```

## Migration Steps

### For Form Components

**Before:**
```typescript
import { SkuFormService } from '@/app/services/skuFormService';

// In component
await SkuFormService.saveSku(skuData, categoryIds, originalSku);
```

**After:**
```typescript
import { SkuOperationsService } from '@/app/services/skuOperationsService';

// In component
await SkuOperationsService.saveSingleSku(skuData, categoryIds, originalSku);
```

### For Bulk Operations

**Before:**
```typescript
// Direct API calls without parent sync
await upsertSkus(modifiedSkus);
```

**After:**
```typescript
import { SkuOperationsService, BulkOperation } from '@/app/services/skuOperationsService';

const operation: BulkOperation = {
    type: 'bulkEdit',
    fields: {} // or specific fields to update
};
await SkuOperationsService.saveBulkSkus(skusToSave, operation);
```

### For Status Changes

**Before:**
```typescript
// Manual status update without parent sync
const updatedSkus = skus.map(sku => ({ ...sku, isActive: 0 }));
await upsertSkus(updatedSkus);
```

**After:**
```typescript
// Automatic parent-child synchronization
await SkuOperationsService.updateSkuStatus(skuIds, 'inactive');
```

## API Changes

### SKUJsonPayload Interface

**Added:**
```typescript
interface SKUJsonPayload {
    // ... existing fields
    variantName?: LocalizedName; // ✅ Added missing field
}
```

### SKUVariant Interface

**Added:**
```typescript
interface SKUVariant {
    // ... existing fields
    isActive: number; // ✅ Reflects child SKU's status
}
```

## Behavioral Changes

### 1. Parent-Child Synchronization

**Before:** Inconsistent synchronization between pages
**After:** All operations automatically sync parent variants with child changes

### 2. Category Relationships

**Before:** Homepage didn't update category relationships
**After:** All operations maintain bidirectional category-SKU relationships

### 3. Field Preservation

**Before:** `variantName` lost in homepage operations
**After:** All fields preserved consistently across all operations

### 4. Error Handling

**Before:** Basic error handling
**After:** Comprehensive error handling with graceful degradation

## Validation Changes

### Required Fields

**Child SKUs:**
- ✅ `name.en` is required
- ✅ `variantName.en` required for children with parents
- ✅ Pricing fields required (costPrice, sellingPrice, mrp)

**Parent SKUs:**
- ✅ `name.en` is required
- ❌ Pricing fields should not be set

### Type-Specific Validation

```typescript
// Child SKU validation
if (sku.type === 'child') {
    if (sku.parentId && !sku.variantName?.en?.trim()) {
        throw new Error('Variant name required for child SKUs with parents');
    }
}

// Parent SKU validation
if (sku.type === 'parent') {
    if (sku.costPrice || sku.sellingPrice || sku.mrp) {
        throw new Error('Parent SKUs should not have pricing');
    }
}
```

## Testing Migration

### Unit Tests

Use the comprehensive test suite in `src/app/services/__tests__/skuOperationsService.test.ts`:

```bash
npm test skuOperationsService.test.ts
```

### Integration Testing

1. **Homepage Bulk Operations:**
   - Test status changes with parent-child sync
   - Test save operations with variantName preservation

2. **Edit/Create Pages:**
   - Test single SKU save operations
   - Test parent-child synchronization
   - Test category relationship updates

3. **Cross-Page Consistency:**
   - Verify same SKU behaves identically across all pages
   - Test variant name display consistency

## Rollback Plan

If issues arise, the old `SkuFormService` is still available (marked as deprecated):

```typescript
// Temporary rollback (not recommended)
import { SkuFormService } from '@/app/services/skuFormService';
await SkuFormService.saveSku(skuData, categoryIds, originalSku);
```

## Benefits of Migration

### For Developers
- ✅ Single source of truth for SKU operations
- ✅ Consistent API across all pages
- ✅ Better error handling and logging
- ✅ Comprehensive validation

### For Users
- ✅ Consistent behavior across all pages
- ✅ No data loss (variantName preserved)
- ✅ Better variant name display
- ✅ Reliable parent-child synchronization

### For Rural Markets
- ✅ Maintained cache-first strategies
- ✅ Batched operations for better connectivity
- ✅ Progressive loading patterns preserved

## Timeline

- **Phase 1:** ✅ Service creation and homepage migration
- **Phase 2:** ✅ Edit/create page migration
- **Phase 3:** 🔄 Remove `SkuFormService` (planned)
- **Phase 4:** 🔄 Additional optimizations (future)

## Support

For questions or issues during migration:
1. Check the comprehensive documentation in `docs/SKU_OPERATIONS_SERVICE.md`
2. Review test cases for examples
3. Contact the development team

## Breaking Changes

⚠️ **Minimal Breaking Changes:**
- API signatures remain compatible
- Only internal implementation changed
- Deprecated services still available for transition period

## Success Metrics

After migration, you should see:
- ✅ Zero missing `variantName` fields in API calls
- ✅ 100% parent-child synchronization accuracy
- ✅ Consistent behavior across all pages
- ✅ Proper variant name display everywhere 
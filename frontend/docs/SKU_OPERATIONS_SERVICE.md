# SKU Operations Service Documentation

## Overview

The **SKU Operations Service** is a unified service that consolidates all SKU save, update, and bulk operations across the application. It replaces the inconsistent patterns between the homepage (`page.tsx`) and edit/create pages (`SkuFormService`) to ensure data consistency and proper parent-child synchronization.

## Problem Statement

### Current Issues
1. **Data Inconsistency**: Homepage and edit/create pages use different save mechanisms leading to missing fields like `variantName`
2. **Missing Parent-Child Sync**: Homepage doesn't update parent SKUs when child SKUs change
3. **Incomplete Category Updates**: Homepage doesn't maintain bidirectional category-SKU relationships
4. **Display Issues**: Child SKUs don't show variant names in homepage table
5. **Code Duplication**: Similar logic scattered across multiple files

### Root Cause
- **Homepage**: Directly calls `upsertSkus(modifiedSkus)` without proper data transformation
- **Edit/Create Pages**: Use `SkuFormService.saveSku()` with complete synchronization
- **API Transform**: `transformSkusToUpsertFormat()` missing critical fields like `variantName`

## Architecture Design

### Unified Service Structure
```
SkuOperationsService
├── saveSingleSku()           // Individual SKU operations (edit/create)
├── saveBulkSkus()            // Bulk operations (homepage)
├── updateSkuStatus()         // Status change operations
└── Internal Methods:
    ├── syncParentChildRelations()
    ├── syncCategoryRelations()
    ├── validateSkuData()
    └── transformForApi()
```

### Data Flow
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Homepage      │    │   Edit Page     │    │  Create Page    │
│   Operations    │    │   Operations    │    │   Operations    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 ▼
                  ┌─────────────────────────────┐
                  │  SkuOperationsService       │
                  │                             │
                  │  ┌─────────────────────────┐│
                  │  │ Data Validation         ││
                  │  └─────────────────────────┘│
                  │  ┌─────────────────────────┐│
                  │  │ Parent-Child Sync       ││
                  │  └─────────────────────────┘│
                  │  ┌─────────────────────────┐│
                  │  │ Category Relations      ││
                  │  └─────────────────────────┘│
                  │  ┌─────────────────────────┐│
                  │  │ API Transformation      ││
                  │  └─────────────────────────┘│
                  └─────────────────────────────┘
                                 │
                    ┌────────────┼────────────┐
                    ▼            ▼            ▼
            ┌─────────────┐ ┌─────────────┐ ┌─────────────┐
            │ SkuService  │ │CategoryServ │ │ Repository  │
            │ (API)       │ │ (Relations) │ │ (Cache)     │
            └─────────────┘ └─────────────┘ └─────────────┘
```

## Service Methods

### 1. `saveSingleSku(skuData, selectedCategoryIds, originalSku?)`
**Purpose**: Handle individual SKU save operations from edit/create pages

**Parameters**:
- `skuData: SKU` - The SKU being saved
- `selectedCategoryIds: number[]` - Categories to associate with the SKU
- `originalSku?: SKU` - Original SKU data for comparison (edit mode)

**Process**:
1. Validate SKU data completeness
2. Handle parent-child synchronization if needed
3. Update category relationships bidirectionally  
4. Transform data for API compatibility
5. Execute upsert operation
6. Clear relevant caches

### 2. `saveBulkSkus(skus, operation)`
**Purpose**: Handle bulk operations from homepage (status changes, bulk edits)

**Parameters**:
- `skus: SKU[]` - Array of SKUs to update
- `operation: BulkOperation` - Type of bulk operation being performed

**Process**:
1. Validate all SKU data in bulk
2. Identify parent SKUs that need variant updates
3. Synchronize parent-child relationships for all affected SKUs
4. Maintain category relationships
5. Execute bulk upsert with proper field mapping
6. Clear affected caches

### 3. `updateSkuStatus(skuIds, newStatus)`
**Purpose**: Handle status changes with proper parent-child synchronization

**Parameters**:
- `skuIds: number[]` - SKUs to update
- `newStatus: 'active' | 'inactive'` - New status

**Process**:
1. Update child SKU status
2. Update corresponding variants in parent SKUs
3. Maintain `isActive` field consistency
4. Execute synchronized updates

## Field Mapping & Consistency

### Child SKU Required Fields
```typescript
interface ChildSkuApiPayload {
  skuId: number;
  type: 'child';
  parentId?: number;
  name: LocalizedName;
  variantName: LocalizedName;        // ← CRITICAL: Previously missing
  costPrice: number;
  sellingPrice: number;
  mrp: number;
  isActive: number;
  imageUrl?: string;                 // Optional override
  images?: string[];                 // Optional override
  description?: LocalizedName;       // Optional override
}
```

### Parent SKU Variant Synchronization
```typescript
interface SKUVariant {
  skuId: number;
  name: LocalizedName;
  variantName: LocalizedName;        // ← CRITICAL: Must reflect child's variantName
  costPrice: number;
  sellingPrice: number;
  mrp: number;
  type: 'child';
  isActive: number;                  // ← CRITICAL: Must reflect child's status
  imageUrl?: string;
  images?: string[];
  description?: LocalizedName;
}
```

## Display Enhancements

### Homepage Table Updates
For child SKUs in the table, display will show:
```typescript
// Current (WRONG):
<div>{sku.name.en}</div>

// New (CORRECT):
<div>{sku.name.en}</div>
{sku.variantName && (
  <div className="text-xs text-gray-500">
    Variant: {sku.variantName.en}
  </div>
)}
```

## Migration Strategy

### Phase 1: Service Creation
1. Create `SkuOperationsService` with unified methods
2. Maintain backward compatibility with existing interfaces
3. Add comprehensive validation and logging

### Phase 2: Homepage Integration  
1. Update homepage to use `SkuOperationsService.saveBulkSkus()`
2. Enhance table display for variant names
3. Fix `prepareHierarchicalSkus()` data preservation

### Phase 3: Edit/Create Page Migration
1. Update pages to use `SkuOperationsService.saveSingleSku()`
2. Remove obsolete `SkuFormService` methods
3. Ensure consistent behavior across all pages

### Phase 4: API Layer Updates
1. Fix `transformSkusToUpsertFormat()` to include all fields
2. Add validation to prevent field omissions
3. Enhance error handling and debugging

## Validation Rules

### Pre-Save Validation
- All required fields present (name, variantName for children)
- Parent-child relationship integrity
- Pricing validation for child SKUs
- Category ID validation

### Post-Save Validation
- Parent variants updated correctly
- Category relationships maintained
- Cache consistency verified

## Error Handling

### Graceful Degradation
- If parent update fails, child is still saved but logged as warning
- If category update fails, SKU is saved but relationship logged as error
- Detailed error messages for debugging

### Logging Strategy
```typescript
// Success logging
logger.info('SKU operation completed', {
  operation: 'saveSingleSku',
  skuId: sku.skuId,
  parentUpdated: boolean,
  categoriesUpdated: number[]
});

// Error logging  
logger.error('SKU operation failed', {
  operation: 'saveBulkSkus',
  failedSkuIds: number[],
  error: errorMessage,
  partialSuccess: boolean
});
```

## Performance Considerations

### Batching Strategy
- Single API call for bulk operations
- Parent updates batched with child updates
- Category updates executed in parallel when possible

### Cache Management
- Selective cache invalidation based on affected SKUs
- Progressive cache rebuilding for better performance
- Rural connectivity optimizations maintained

## Testing Strategy

### Unit Tests
- Individual method validation
- Parent-child synchronization scenarios
- Category relationship management
- Error handling edge cases

### Integration Tests
- End-to-end homepage bulk operations
- Edit/create page workflows
- Cross-page consistency verification

## Success Metrics

### Data Consistency
- Zero missing `variantName` fields in API calls
- 100% parent-child synchronization accuracy
- Complete category relationship maintenance

### User Experience
- Consistent behavior across all pages
- Proper variant name display in all tables
- Reliable bulk operations

### Developer Experience
- Single source of truth for SKU operations
- Simplified debugging and maintenance
- Reduced code duplication

This unified service architecture ensures that all SKU operations maintain data integrity, proper relationships, and consistent behavior across the entire application. 
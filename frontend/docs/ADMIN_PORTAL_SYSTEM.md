# Admin Portal System

This document describes the comprehensive admin portal system for managing categories, SKUs, carts, and orders in the application.

## Implementation Status

### ✅ Completed Features
- **Category Management** - Fully implemented with create, edit, manage SKUs, drag-and-drop reordering
- **SKU Management** - Fully implemented with advanced filtering, variants management, bulk operations
- **Admin Authentication** - Complete with permission-based access control
- **Core Infrastructure** - AdminLayout, AdminSidebar, AdminHeader, AdminGuard, DataTable components

### 🚧 In Progress
- **Import/Export Functionality** - Excel-based import/export for business operations (planned)

### 📋 Planned Features
- **Cart Management** - View and edit customer carts (no deletion)
- **Order Management** - View, update status, and cancel orders (no deletion)

## Overview

The admin portal provides a web-optimized interface for administrative operations including:

- **Category Management** ✅ - Create, edit, and manage product categories with SKU assignments
- **SKU Management** ✅ - Manage products and their variants with multi-category support
- **Import/Export Operations** 🚧 - Excel-based bulk operations for business teams
- **Cart Management** 📋 - View and edit customer carts (no deletion)
- **Order Management** 📋 - View, update status, and cancel orders (no deletion)

## Architecture

### Core Principles

1. **Web-Only Optimization** - Designed specifically for desktop/web use
2. **Permission-Based Access** - Leverages existing admin roles system
3. **No Pagination** - Simple list management without pagination complexity
4. **Soft Operations** - No hard deletions, only status changes and cancellations

### SKU & Variant System Understanding

- **Variants are separate SKUs** that share a parent-child relationship
- **Frontend shows only parent SKUs** to customers
- **Admin interface provides intuitive variant management** despite technical complexity
- **SKUs can belong to multiple categories** (many-to-many relationship)
- **Categories define SKU membership** via `skuIds` array with display ordering

### Import/Export System for Business Operations

Business operations teams require Excel-based workflows for bulk data management. The system provides comprehensive import/export capabilities for:

#### **Category Import/Export Operations**
1. **Category List Export** - Export all categories with metadata
2. **Category Bulk Import** - Create/update multiple categories
3. **Category-SKU Mapping Export** - Export SKU assignments per category
4. **Category-SKU Mapping Import** - Bulk update SKU assignments and ordering

#### **SKU Import/Export Operations**
1. **SKU Master Data Export** - Export all SKU information
2. **SKU Bulk Import** - Create/update multiple SKUs
3. **SKU Pricing Export** - Export pricing information for bulk updates
4. **SKU Pricing Import** - Bulk update pricing (cost, selling, MRP)
5. **SKU Variants Export** - Export parent-variant relationships
6. **SKU Variants Import** - Bulk create/update variants
7. **SKU Category Assignments Export** - Export multi-category assignments
8. **SKU Category Assignments Import** - Bulk update category memberships

#### **Excel Template Standards**
- **Standardized Templates** - Pre-defined Excel templates for each operation
- **Data Validation** - Built-in Excel validation rules
- **Error Reporting** - Detailed error reports for failed imports
- **Preview Mode** - Preview changes before applying imports
- **Rollback Capability** - Ability to rollback failed imports

## Routing Structure

```
/admin (Simple overview dashboard)
├── /admin/categories ✅ (Category Management with Import/Export buttons)
│   ├── /admin/categories/create ✅
│   ├── /admin/categories/[id]/edit ✅ (with individual import/export options)
│   └── /admin/categories/[id]/manage-skus ✅ (Add/remove/reorder SKUs)
├── /admin/skus ✅ (SKU Management with Import/Export buttons)
│   ├── /admin/skus/create ✅
│   ├── /admin/skus/[id]/edit ✅ (with individual import/export options)
│   ├── /admin/skus/[id]/variants ✅ (Manage variants with import/export)
│   └── /admin/skus/[id]/preview ✅
├── /admin/cart 📋 (Cart Management - view/edit only)
│   └── /admin/cart/[cartId]/details 📋
└── /admin/orders 📋 (Order Management - view/cancel/update status)
    └── /admin/orders/[orderId]/details 📋
```

## Permission System

### Required Permissions

```typescript
export interface AdminPermissions {
    // Existing
    byPass?: boolean;
    
    // Category Management
    manageCategories?: boolean;
    
    // SKU Management
    manageSku?: boolean;
    manageSkuVariants?: boolean;
    
    // Cart Management
    viewAllCarts?: boolean;
    editCustomerCart?: boolean;
    
    // Order Management
    viewAllOrders?: boolean;
    manageOrders?: boolean; // View, cancel, update status only
}
```

### Permission Usage

```tsx
// Category management access
<AdminGuard requiredPermission="manageCategories">
    <CategoryManagement />
</AdminGuard>

// SKU variant management
<AdminGuard requiredPermissions={["manageSku", "manageSkuVariants"]}>
    <VariantManager />
</AdminGuard>

// Order management
<AdminGuard requiredPermission="manageOrders">
    <OrderStatusUpdate />
</AdminGuard>
```

## Features

### 1. Category Management (`/admin/categories`) ✅

#### List View ✅
- **Table Columns**: Name, Status, SKU Count, Background Color Preview, Actions
- **Actions**: Create, Edit, Manage SKUs, Toggle Status
- **Bulk Operations**: Activate/Deactivate selected categories
- **Import/Export Panel** 🚧: 
  - **Export Dropdown**: Export All Categories, Export Category-SKU Mappings
  - **Import Button**: Opens import modal for bulk category operations
  - **Template Downloads**: Pre-configured Excel templates

#### Category Form (Create/Edit) ✅
- **Name**: Localized name input (English/Tamil)
- **Icon**: Icon selection dropdown/picker
- **Background**: Color picker for category background
- **Status**: Active/Inactive toggle
- **SKU Management**: (Edit mode only) Manage category SKUs
- **Individual Import/Export** 🚧: (Edit mode only)
  - **Export Category Data**: Export this specific category's information
  - **Import Category Updates**: Import updates for this category
  - **Export SKU Mappings**: Export SKUs assigned to this category

#### SKU Management (`/admin/categories/[id]/manage-skus`) ✅
- **Current SKUs**: Drag-and-drop reordering within category
- **Add SKUs**: Search and select from available SKUs
- **Remove SKUs**: Remove from category (doesn't delete SKU)
- **Parent SKU Display**: Show only parent SKUs with variant count indicator

#### Import/Export Operations 🚧

##### **Category List Export**
- **Format**: Excel (.xlsx) with standardized template
- **Columns**: ID, Name (EN), Name (TA), Icon, Background Color, Status, SKU Count, Created Date, Modified Date
- **Filters**: Export all or filtered categories
- **Use Cases**: Backup, reporting, bulk editing preparation

##### **Category Bulk Import**
- **Template**: Pre-defined Excel template with validation
- **Operations**: Create new categories, update existing categories
- **Validation**: Name uniqueness, valid colors, icon validation
- **Preview**: Show changes before applying
- **Error Handling**: Detailed error report for failed rows

##### **Category-SKU Mapping Export**
- **Format**: Excel with category-SKU relationships
- **Columns**: Category ID, Category Name, SKU ID, SKU Name, Display Order, Status
- **Use Cases**: Reorganizing category structures, bulk SKU assignments

##### **Category-SKU Mapping Import**
- **Operations**: Add/remove SKUs from categories, update display order
- **Validation**: Valid category IDs, valid SKU IDs, order conflicts
- **Bulk Operations**: Assign multiple SKUs to multiple categories

### 2. SKU Management (`/admin/skus`) ✅

#### List View with Filters ✅
- **Filters**:
  - Category (multi-select - SKUs can be in multiple categories)
  - Status (active/inactive)
  - Type (parent/variant)
  - Search by name
  - Price range filters
- **Table Columns**: Image, Name, Type, Categories, Status, Variant Count, Actions
- **Import/Export Panel** 🚧:
  - **Export Dropdown**: All SKUs, SKU Pricing, SKU Variants, Category Assignments
  - **Import Dropdown**: Bulk SKU Import, Pricing Updates, Variants, Category Assignments
  - **Template Downloads**: Pre-configured Excel templates for each operation
  - **Filter-Based Export**: Export only filtered/selected SKUs

#### SKU Form (Create/Edit) ✅
- **Basic Info**: Name (localized), Description (localized)
- **Pricing**: Cost Price, Selling Price, MRP
- **Images**: Primary image + gallery images
- **Categories**: Multi-select categories this SKU belongs to
- **Status**: Active/Inactive toggle
- **Parent SKU**: (For variants only) Select parent SKU
- **Variant Details**: (For variants) Specific variant attributes
- **Individual Import/Export** 🚧: (Edit mode only)
  - **Export SKU Data**: Export this specific SKU's complete information
  - **Import SKU Updates**: Import updates for this SKU
  - **Export Variants**: (Parent SKUs only) Export all variants of this SKU
  - **Import Variants**: (Parent SKUs only) Bulk import variants for this SKU

#### Variant Management (`/admin/skus/[id]/variants`) ✅
- **Available for Parent SKUs only**
- **Current Variants**: List with quick edit capabilities
- **Add Variant**: Simplified form inheriting parent data
- **Variant Actions**: Activate/Deactivate, Edit pricing
- **Display Order**: Reorder variants for category display
- **Variant Import/Export** 🚧:
  - **Export All Variants**: Export all variants of this parent SKU
  - **Import Variants**: Bulk create/update variants for this parent SKU
  - **Export Variant Template**: Download template for variant creation
  - **Bulk Variant Operations**: Update pricing, status for multiple variants

#### SKU Preview (`/admin/skus/[id]/preview`) ✅
- **Product Detail Page Preview**: Customer view simulation
- **Category Card Preview**: How it appears in category listings
- **Desktop Preview**: Web-optimized preview

#### Import/Export Operations 🚧

##### **SKU Master Data Export**
- **Format**: Excel (.xlsx) with comprehensive SKU information
- **Columns**: SKU ID, Name (EN), Name (TA), Description (EN), Description (TA), Cost Price, Selling Price, MRP, Status, Type (Parent/Variant), Parent SKU ID, Categories, Image URLs, Created Date, Modified Date
- **Filters**: Export all, by category, by status, by type
- **Use Cases**: Complete SKU backup, bulk editing, reporting

##### **SKU Bulk Import**
- **Template**: Pre-defined Excel template with validation rules
- **Operations**: Create new SKUs, update existing SKUs
- **Validation**: Name uniqueness, valid pricing, parent-child relationships
- **Image Handling**: Support for image URL imports
- **Preview Mode**: Show all changes before applying
- **Error Handling**: Detailed validation errors with row references

##### **SKU Pricing Export**
- **Format**: Focused Excel template for pricing operations
- **Columns**: SKU ID, SKU Name, Current Cost Price, Current Selling Price, Current MRP, Status
- **Use Cases**: Bulk pricing updates, pricing analysis, cost management

##### **SKU Pricing Import**
- **Operations**: Update cost price, selling price, MRP for multiple SKUs
- **Validation**: Positive pricing values, logical price relationships (cost < selling < MRP)
- **Audit Trail**: Track pricing changes with timestamps
- **Bulk Operations**: Apply percentage increases/decreases

##### **SKU Variants Export**
- **Format**: Parent-variant relationship export
- **Columns**: Parent SKU ID, Parent Name, Variant SKU ID, Variant Name, Variant Attributes, Pricing Differences, Status
- **Use Cases**: Variant management, pricing analysis

##### **SKU Variants Import**
- **Operations**: Create variants for parent SKUs, update variant relationships
- **Validation**: Valid parent SKU IDs, unique variant names
- **Bulk Creation**: Create multiple variants for multiple parents

##### **SKU Category Assignments Export**
- **Format**: Many-to-many relationship export
- **Columns**: SKU ID, SKU Name, Category ID, Category Name, Display Order, Assignment Status
- **Use Cases**: Category reorganization, bulk assignments

##### **SKU Category Assignments Import**
- **Operations**: Add/remove SKUs from categories, update display orders
- **Validation**: Valid SKU and category IDs
- **Bulk Operations**: Assign multiple SKUs to multiple categories simultaneously

### 3. Cart Management (`/admin/cart`)

#### List View with Filters
- **Filters**:
  - Status: Active, Abandoned, Converted to Order
  - Date Range: Created date, last modified date
  - Customer: Search by name/phone
  - Value: Cart value ranges
- **Table Columns**: Customer, Items Count, Total Value, Status, Last Modified, Actions

#### Cart Operations (No Deletion)
- **View Details**: Complete cart information
- **Edit Items**: Add/remove/modify quantities
- **Customer Contact**: For abandoned cart recovery

#### Cart Details (`/admin/cart/[cartId]/details`)
- **Customer Information**: Name, phone, location
- **Cart Items**: Parent SKU names with variant details
- **Cart Timeline**: Creation, modifications, activity log
- **Edit Capabilities**: Modify individual items

### 4. Order Management (`/admin/orders`)

#### List View with Filters
- **Filters**:
  - Status: Pending, Confirmed, In-Progress, Completed, Cancelled
  - Date Range: Order date, delivery date
  - Customer: Search by name/phone
  - Value: Order value ranges
  - Payment Status: Paid, Pending, Failed
- **Table Columns**: Order ID, Customer, Items Count, Total Value, Status, Order Date, Actions

#### Order Operations (No Deletion)
- **View Details**: Complete order information
- **Update Status**: Change order status
- **Cancel Order**: With cancellation reason
- **Modify Items**: Before confirmation only

#### Order Details (`/admin/orders/[orderId]/details`)
- **Customer Information**: Complete customer details
- **Order Items**: Parent SKU + variant details
- **Order Timeline**: Status changes and activities
- **Payment Information**: Payment status and details
- **Status Management**: Update order status
- **Cancellation**: Cancel with reason (if applicable)

## Data Models

### Enhanced SKU Interface for Admin

```typescript
export interface AdminSKU extends SKU {
    parentSkuId?: number; // For variants
    isParent: boolean;
    variantCount?: number; // For parent SKUs
    categories: number[]; // Array of category IDs this SKU belongs to
    displayOrder?: { [categoryId: number]: number }; // Display order per category
}
```

### Enhanced Category Interface for Admin

```typescript
export interface AdminCategory extends Category {
    skuDisplayOrder: number[]; // Ordered array of SKU IDs for display
}
```

## API Endpoints

### Category Management
```typescript
GET /admin/categories                    // List all categories
POST /admin/categories                   // Create category
PUT /admin/categories/:id                // Update category
DELETE /admin/categories/:id             // Soft delete category
PUT /admin/categories/:id/toggle-status  // Toggle active/inactive
PUT /admin/categories/:id/skus           // Add/remove/reorder SKUs

// Import/Export Endpoints
GET /admin/categories/export             // Export categories to Excel
GET /admin/categories/export/mappings    // Export category-SKU mappings
POST /admin/categories/import            // Import categories from Excel
POST /admin/categories/import/mappings   // Import category-SKU mappings
GET /admin/categories/template/:type     // Download Excel templates
POST /admin/categories/validate-import   // Validate import file before processing
```

### SKU Management
```typescript
GET /admin/skus?category=&status=&type=&search=  // List SKUs with filters
POST /admin/skus                                 // Create SKU
PUT /admin/skus/:id                             // Update SKU
DELETE /admin/skus/:id                          // Soft delete SKU
PUT /admin/skus/:id/toggle-status               // Toggle active/inactive
GET /admin/skus/:id/variants                    // Get SKU variants
POST /admin/skus/:id/variants                   // Create variant
PUT /admin/skus/:id/variants/reorder            // Reorder variants

// Import/Export Endpoints
GET /admin/skus/export                          // Export all SKUs to Excel
GET /admin/skus/export/pricing                  // Export SKU pricing data
GET /admin/skus/export/variants                 // Export variant relationships
GET /admin/skus/export/categories               // Export SKU-category assignments
POST /admin/skus/import                         // Import SKUs from Excel
POST /admin/skus/import/pricing                 // Import pricing updates
POST /admin/skus/import/variants                // Import variant data
POST /admin/skus/import/categories              // Import category assignments
GET /admin/skus/template/:type                  // Download Excel templates
POST /admin/skus/validate-import                // Validate import file before processing
```

### Cart Management (No Delete)
```typescript
GET /admin/carts?status=&dateFrom=&dateTo=&customer=&minValue=&maxValue=  // List carts
GET /admin/carts/:id                                                     // Get cart details
PUT /admin/carts/:id                                                     // Edit cart items
```

### Order Management (No Delete)
```typescript
GET /admin/orders?status=&dateFrom=&dateTo=&customer=&minValue=&maxValue=&paymentStatus=  // List orders
GET /admin/orders/:id                    // Get order details
PUT /admin/orders/:id/status             // Update order status
PUT /admin/orders/:id/cancel             // Cancel order
PUT /admin/orders/:id/items              // Modify items (before confirmation)
```

## Component Architecture

```
src/app/admin/
├── layout.tsx                          // Fixed sidebar layout for web
├── page.tsx                           // Simple dashboard
├── components/
│   ├── AdminSidebar.tsx               // Fixed sidebar navigation
│   ├── AdminHeader.tsx                // Admin header with user info
│   ├── DataTable.tsx                  // Reusable table (no pagination)
│   ├── FilterPanel.tsx                // Reusable filter component
│   ├── StatusBadge.tsx                // Status indicator component
│   ├── SkuTypeIndicator.tsx           // Parent/Variant indicator
│   ├── CategoryMultiSelect.tsx        // Multi-category selection
│   ├── VariantManager.tsx             // Variant management component
│   ├── ImportExportPanel.tsx 🚧       // Import/Export operations panel
│   ├── ExcelPreviewModal.tsx 🚧       // Preview import changes
│   ├── ImportProgressModal.tsx 🚧     // Import progress tracking
│   ├── ExportOptionsModal.tsx 🚧      // Export configuration options
│   └── forms/
│       ├── CategoryForm.tsx           // Category create/edit form
│       ├── SkuForm.tsx                // SKU create/edit form
│       ├── VariantForm.tsx            // Variant create/edit form
│       ├── OrderStatusForm.tsx        // Order status update form
│       ├── ImportValidationForm.tsx 🚧 // Import validation and preview
│       └── ExportConfigForm.tsx 🚧    // Export configuration form
├── categories/
│   ├── page.tsx                       // Category list with integrated import/export
│   ├── create/page.tsx                // Create category
│   ├── [id]/
│   │   ├── edit/page.tsx              // Edit category with individual import/export
│   │   └── manage-skus/page.tsx       // Manage category SKUs
├── skus/
│   ├── page.tsx                       // SKU list with integrated import/export panel
│   ├── create/page.tsx                // Create SKU
│   ├── [id]/
│   │   ├── edit/page.tsx              // Edit SKU with individual import/export
│   │   ├── variants/page.tsx          // Manage SKU variants with import/export
│   │   └── preview/page.tsx           // Preview SKU
├── cart/ 📋
│   ├── page.tsx                       // Cart list with filters
│   └── [cartId]/details/page.tsx      // Cart details and editing
└── orders/ 📋
    ├── page.tsx                       // Order list with filters
    └── [orderId]/details/page.tsx     // Order details and management
```

## UI/UX Features

### Import/Export Integration UX
- **Contextual Placement**: Import/Export buttons integrated into existing page headers
- **Modal-Based Workflow**: All import/export operations use modals to maintain context
- **Progressive Disclosure**: 
  - Export dropdown with multiple options
  - Import button opens step-by-step modal workflow
- **Visual Feedback**: Clear progress indicators and validation results
- **Template-First Approach**: Always provide Excel templates before import
- **Preview Before Apply**: Show all changes before committing imports

### SKU & Variant Management UX
- **Parent SKU Creation**: Standard SKU form workflow
- **Variant Creation**: 
  - "Add Variant" button on parent SKU pages
  - Pre-filled form inheriting parent data
  - Focus on variant-specific fields (size, color, etc.)
  - Bulk variant creation for common patterns
- **Visual Hierarchy**: Clear parent-child relationship indicators
- **Category Assignment**: Multi-select with visual feedback

### Category-SKU Relationship Management
- **Drag-and-Drop**: Reorder SKUs within categories
- **Multi-Category Display**: Show all categories a SKU belongs to
- **Quick Actions**: Add/remove SKU from categories without full edit

### Web-Optimized Interface
- **Fixed Sidebar**: Always visible navigation
- **Wide Tables**: Utilize full screen width effectively
- **Keyboard Shortcuts**: Power user features for efficiency
- **No Mobile Considerations**: Desktop-focused design
- **Integrated Operations**: Import/Export seamlessly integrated into existing workflows

## Internationalization (i18n)

**Supported Languages**: English (en) and Tamil (ta)

### Key Translation Areas

```json
{
  "admin": {
    "nav": {
      "dashboard": "Dashboard",
      "categories": "Categories", 
      "sku": "Products",
      "cart": "Carts",
      "orders": "Orders"
    },
    "actions": {
      "create": "Create",
      "edit": "Edit", 
      "delete": "Delete",
      "activate": "Activate",
      "deactivate": "Deactivate",
      "cancel": "Cancel",
      "updateStatus": "Update Status"
    },
    "filters": {
      "status": "Status",
      "dateRange": "Date Range",
      "search": "Search",
      "category": "Category",
      "customer": "Customer",
      "value": "Value"
    },
    "status": {
      "active": "Active",
      "inactive": "Inactive",
      "pending": "Pending",
      "confirmed": "Confirmed",
      "cancelled": "Cancelled",
      "completed": "Completed"
    },
    "sku": {
      "type": {
        "parent": "Parent SKU",
        "variant": "Variant"
      },
      "variants": {
        "count": "{{count}} variants",
        "addVariant": "Add Variant",
        "manageVariants": "Manage Variants",
        "noVariants": "No variants"
      },
      "categories": {
        "multiSelect": "Select Categories",
        "displayOrder": "Display Order",
        "addToCategory": "Add to Category",
        "removeFromCategory": "Remove from Category"
      }
    },
    "cart": {
      "noDelete": "Carts cannot be deleted",
      "editItems": "Edit Items",
      "abandoned": "Abandoned",
      "active": "Active",
      "converted": "Converted to Order"
    },
    "orders": {
      "noDelete": "Orders cannot be deleted",
      "cancelOrder": "Cancel Order",
      "cancellationReason": "Cancellation Reason",
      "updateStatus": "Update Status",
      "modifyItems": "Modify Items"
    },
    "importExport": {
      "export": "Export",
      "import": "Import",
      "downloadTemplate": "Download Template",
      "uploadFile": "Upload File",
      "validateData": "Validate Data",
      "previewChanges": "Preview Changes",
      "applyChanges": "Apply Changes",
      "exportOptions": "Export Options",
      "importProgress": "Import Progress",
      "validationErrors": "Validation Errors",
      "successfulImport": "Import Successful",
      "failedImport": "Import Failed",
      "exportComplete": "Export Complete",
      "selectFile": "Select Excel File",
      "fileFormat": "File Format: Excel (.xlsx)",
      "maxFileSize": "Maximum file size: 10MB",
      "operations": {
        "categories": "Categories",
        "categoryMappings": "Category-SKU Mappings",
        "skus": "SKUs",
        "skuPricing": "SKU Pricing",
        "skuVariants": "SKU Variants",
        "skuCategories": "SKU Category Assignments"
      },
      "templates": {
        "categoryList": "Category List Template",
        "categoryMappings": "Category-SKU Mappings Template",
        "skuMaster": "SKU Master Data Template",
        "skuPricing": "SKU Pricing Template",
        "skuVariants": "SKU Variants Template",
        "skuCategories": "SKU Category Assignments Template"
      },
      "validation": {
        "requiredField": "Required field missing",
        "invalidFormat": "Invalid format",
        "duplicateEntry": "Duplicate entry found",
        "invalidReference": "Invalid reference",
        "pricingError": "Pricing validation error",
        "categoryNotFound": "Category not found",
        "skuNotFound": "SKU not found",
        "parentSkuRequired": "Parent SKU required for variants"
      }
    }
  }
}
```

## Security Considerations

1. **Permission Validation**: All admin operations must validate permissions server-side
2. **Audit Logging**: Track all admin actions for accountability
3. **Data Validation**: Validate all form inputs and API requests
4. **Session Management**: Secure admin session handling
5. **Role-Based Access**: Granular permission checking for each operation

## Performance Considerations

1. **No Pagination**: Simple list management without pagination complexity
2. **Efficient Filtering**: Client-side filtering for better UX
3. **Optimistic Updates**: Immediate UI feedback for admin actions
4. **Image Optimization**: Automated image processing for SKU images
5. **Caching Strategy**: Strategic caching for frequently accessed data

## Implementation Notes

1. **Web-Only**: Optimized specifically for desktop/web browsers
2. **Fixed Layout**: Sidebar navigation always visible
3. **Bulk Operations**: Efficient multi-item management capabilities
4. **Search & Filtering**: Advanced search and filtering on all list pages
5. **Visual Feedback**: Clear status indicators and action confirmations

## Future Enhancements

1. **Advanced Analytics**: Integration with Microsoft Clarity for insights
2. **Bulk Import/Export**: CSV/Excel import/export capabilities
3. **Advanced Search**: Full-text search across all entities
4. **Workflow Management**: Approval workflows for certain operations
5. **API Rate Limiting**: Implement rate limiting for admin operations 
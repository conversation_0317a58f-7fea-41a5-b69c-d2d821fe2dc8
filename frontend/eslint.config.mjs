import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  ...compat.extends("next/core-web-vitals", "next/typescript"),
  {
    rules: {
      // TypeScript specific rules
      "@typescript-eslint/no-unused-vars": ["warn", {
        "argsIgnorePattern": "^_",
        "varsIgnorePattern": "^_",
        "destructuredArrayIgnorePattern": "^_"
      }],
      "@typescript-eslint/no-explicit-any": "warn",
      "@typescript-eslint/no-empty-object-type": "warn",
      "@typescript-eslint/no-unsafe-function-type": "warn",

      // React specific rules
      "react/no-unescaped-entities": "off", // Allow unescaped entities in JSX
      "react-hooks/exhaustive-deps": "warn", // Warn instead of error for missing dependencies

      // General JavaScript rules
      "prefer-const": "warn",
      "no-console": "warn", // Warn about console.log statements

      // Next.js specific
      "@next/next/no-img-element": "warn", // Prefer Next.js Image component

      // TypeScript import rules
      "@typescript-eslint/no-require-imports": "warn", // Allow require() in platform detection

      // Disable some overly strict rules for development
      "react/display-name": "off",
      "react/prop-types": "off",

      // Allow unused parameters that start with underscore
      "no-unused-vars": "off", // Turn off base rule as it can conflict with TypeScript rule
    },
    settings: {
      react: {
        version: "detect"
      }
    }
  }
];

export default eslintConfig;

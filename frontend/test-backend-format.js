const fs = require('fs');
const path = require('path');

// Mock browser APIs for Node.js environment
global.Blob = class Blob {
    constructor(parts, options) {
        this.parts = parts;
        this.type = options?.type || '';
    }
};

global.URL = {
    createObjectURL: () => 'mock-url',
    revokeObjectURL: () => { }
};

global.document = {
    createElement: () => ({
        href: '',
        download: '',
        click: () => { },
        style: { display: '' }
    }),
    body: {
        appendChild: () => { },
        removeChild: () => { }
    }
};

/**
 * Sanitize text for CSV export by removing line breaks and extra spaces
 */
function sanitizeForCSV(text) {
    if (!text || typeof text !== 'string') return '';
    return text
        .replace(/\r\n/g, ' ')
        .replace(/\n/g, ' ')
        .replace(/\r/g, ' ')
        .replace(/\s+/g, ' ')
        .trim();
}

/**
 * Backend SKU format (from API response)
 */
function convertBackendToFrontend(backendSku) {
    const frontendSku = {
        skuId: backendSku.skuId,
        name: backendSku.name?.en || '',
        nameEn: backendSku.name?.en || '',
        nameTa: backendSku.name?.ta || '',
        description: backendSku.description?.en || '',
        descriptionEn: backendSku.description?.en || '',
        descriptionTa: backendSku.description?.ta || '',
        type: backendSku.type || 'child',
        parentId: backendSku.parentId || null,
        categoryId: backendSku.categoryId || 1, // Default category
        isActive: 1, // Default to active
        price: backendSku.sellingPrice || undefined,
        compareAtPrice: backendSku.mrp || undefined,
        costPrice: backendSku.costPrice || undefined,
        sellingPrice: backendSku.sellingPrice || undefined,
        mrp: backendSku.mrp || undefined,
        imageUrl: backendSku.imageUrl || '',
        images: backendSku.images || [],
        variants: []
    };

    // Convert variants if they exist
    if (backendSku.variants && Array.isArray(backendSku.variants)) {
        frontendSku.variants = backendSku.variants.map(variant => ({
            skuId: variant.skuId,
            name: variant.name?.en || '',
            nameEn: variant.name?.en || '',
            nameTa: variant.name?.ta || '',
            type: 'child',
            parentId: backendSku.skuId,
            price: variant.sellingPrice,
            compareAtPrice: variant.mrp,
            costPrice: variant.costPrice,
            sellingPrice: variant.sellingPrice,
            mrp: variant.mrp,
            isActive: 1
        }));
    }

    return frontendSku;
}

/**
 * Convert backend API response to frontend format
 */
function convertBackendApiToFrontend(backendSkus) {
    const frontendSkus = {};
    const processedChildIds = new Set();

    // First pass: Process parent SKUs and their variants
    Object.values(backendSkus).forEach(backendSku => {
        if (backendSku.type === 'parent') {
            const frontendSku = convertBackendToFrontend(backendSku);
            frontendSkus[backendSku.skuId] = frontendSku;

            // Mark variant IDs as processed
            if (frontendSku.variants) {
                frontendSku.variants.forEach(variant => {
                    processedChildIds.add(variant.skuId);
                });
            }
        }
    });

    // Second pass: Process standalone child SKUs (not already processed as variants)
    Object.values(backendSkus).forEach(backendSku => {
        if (backendSku.type === 'child' && !processedChildIds.has(backendSku.skuId)) {
            const frontendSku = convertBackendToFrontend(backendSku);
            frontendSkus[backendSku.skuId] = frontendSku;
        }
    });

    return frontendSkus;
}

/**
 * Flatten SKUs for CSV export (FIXED VERSION - no variants processing)
 */
function flattenSkusForExport(skus) {
    const flattenedSkus = [];

    Object.values(skus).forEach(sku => {
        // Use the SKU's own type field directly (no need to check variants)
        const skuType = sku.type || 'child'; // Default to 'child' if not specified

        // Ensure we have a valid name for all SKUs
        let nameEn = sku.nameEn || sku.name || '';
        let nameTa = sku.nameTa || '';

        if (!nameEn || !nameEn.trim()) {
            nameEn = `Product ${sku.skuId}`;
        }

        // Add the SKU as a flattened entry
        const flatSku = {
            skuId: sku.skuId,
            nameEn: sanitizeForCSV(nameEn),
            nameTa: sanitizeForCSV(nameTa),
            descriptionEn: sanitizeForCSV(sku.descriptionEn || sku.description || ''),
            descriptionTa: sanitizeForCSV(sku.descriptionTa || ''),
            type: skuType,
            parentId: sku.parentId || '',
            categoryId: sku.categoryId || 1,
            status: sku.isActive === 1 ? 'active' : 'inactive',
            costPrice: skuType === 'parent' ? '' : (sku.costPrice || ''),
            sellingPrice: skuType === 'parent' ? '' : (sku.sellingPrice || sku.price || ''),
            mrp: skuType === 'parent' ? '' : (sku.mrp || sku.compareAtPrice || ''),
            imageUrl: sku.imageUrl || '',
            images: Array.isArray(sku.images) ? sku.images.join('|') : ''
        };

        flattenedSkus.push(flatSku);

        // ✅ NO VARIANTS PROCESSING - variants are already independent SKUs in the admin interface
        // This eliminates the duplication issue where child SKUs were being exported twice:
        // 1. Once as standalone SKUs (correct)
        // 2. Again from parent's variants array (incorrect - causing duplicates)
    });

    return flattenedSkus;
}

/**
 * Generate CSV content from flattened SKUs
 */
function generateCSV(flattenedSkus) {
    const headers = [
        'skuId', 'nameEn', 'nameTa', 'descriptionEn', 'descriptionTa',
        'type', 'parentId', 'categoryId', 'status',
        'costPrice', 'sellingPrice', 'mrp', 'imageUrl', 'images'
    ];

    const csvRows = [headers.join(',')];

    flattenedSkus.forEach(sku => {
        const row = headers.map(header => {
            const value = sku[header] || '';
            // Escape quotes and wrap in quotes if contains comma or quote
            if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
                return `"${value.replace(/"/g, '""')}"`;
            }
            return value;
        });
        csvRows.push(row.join(','));
    });

    return csvRows.join('\n');
}

/**
 * Parse CSV content to flattened SKUs
 */
function parseCSV(csvContent) {
    const lines = csvContent.split('\n').filter(line => line.trim());
    if (lines.length === 0) return [];

    const headers = lines[0].split(',').map(h => h.trim());
    const flattenedSkus = [];

    for (let i = 1; i < lines.length; i++) {
        const values = [];
        let currentValue = '';
        let inQuotes = false;

        for (let j = 0; j < lines[i].length; j++) {
            const char = lines[i][j];

            if (char === '"') {
                if (inQuotes && lines[i][j + 1] === '"') {
                    currentValue += '"';
                    j++; // Skip next quote
                } else {
                    inQuotes = !inQuotes;
                }
            } else if (char === ',' && !inQuotes) {
                values.push(currentValue.trim());
                currentValue = '';
            } else {
                currentValue += char;
            }
        }
        values.push(currentValue.trim());

        const sku = {};
        headers.forEach((header, index) => {
            const value = values[index] || '';
            if (header === 'skuId' || header === 'parentId' || header === 'categoryId') {
                sku[header] = value ? parseInt(value) : (header === 'categoryId' ? 1 : null);
            } else if (header === 'costPrice' || header === 'sellingPrice' || header === 'mrp') {
                sku[header] = value ? parseFloat(value) : null;
            } else if (header === 'images') {
                sku[header] = value ? value.split('|') : [];
            } else {
                sku[header] = value;
            }
        });

        flattenedSkus.push(sku);
    }

    return flattenedSkus;
}

/**
 * Convert flattened SKUs back to nested structure
 */
function nestSkusFromImport(flattenedSkus) {
    const skuMap = {};
    const parentSkus = {};

    // First pass: Create all SKUs and identify parents
    flattenedSkus.forEach(flatSku => {
        const sku = {
            skuId: flatSku.skuId,
            nameEn: flatSku.nameEn?.trim() || '',
            nameTa: flatSku.nameTa?.trim() || '',
            descriptionEn: flatSku.descriptionEn?.trim() || '',
            descriptionTa: flatSku.descriptionTa?.trim() || '',
            name: flatSku.nameEn?.trim() || '',
            description: flatSku.descriptionEn?.trim() || '',
            type: flatSku.type,
            parentId: flatSku.parentId,
            categoryId: flatSku.categoryId || 1,
            isActive: flatSku.status === 'active' ? 1 : 0,
            costPrice: flatSku.costPrice || 0,
            sellingPrice: flatSku.sellingPrice || 0,
            mrp: flatSku.mrp || 0,
            price: flatSku.sellingPrice || 0,
            compareAtPrice: flatSku.mrp || 0,
            imageUrl: flatSku.imageUrl || '',
            images: Array.isArray(flatSku.images) ? flatSku.images : [],
            variants: []
        };

        skuMap[sku.skuId] = sku;

        if (flatSku.type === 'parent') {
            parentSkus[sku.skuId] = sku;
        }
    });

    // Second pass: Nest child SKUs as variants under their parents
    Object.values(skuMap).forEach(sku => {
        if (sku.type === 'child' && sku.parentId && parentSkus[sku.parentId]) {
            parentSkus[sku.parentId].variants.push(sku);
        }
    });

    // Return only parent SKUs and standalone child SKUs
    const result = {};
    Object.values(skuMap).forEach(sku => {
        if (sku.type === 'parent' || !sku.parentId) {
            result[sku.skuId] = sku;
        }
    });

    return result;
}

/**
 * Main test function
 */
function runTest(jsonFile) {
    console.log('🧪 Testing Backend Format Export-Import Cycle');
    console.log('='.repeat(50));

    try {
        // Load backend format data
        const backendData = JSON.parse(fs.readFileSync(jsonFile, 'utf8'));
        console.log('✅ Loaded backend data:', Object.keys(backendData.skus).length, 'SKUs');

        // Convert to frontend format
        const frontendSkus = convertBackendApiToFrontend(backendData.skus);
        console.log('✅ Converted to frontend format:', Object.keys(frontendSkus).length, 'SKUs');

        // First export
        const flattened1 = flattenSkusForExport(frontendSkus);
        const csv1 = generateCSV(flattened1);
        fs.writeFileSync('backend-export1.csv', csv1);
        console.log('✅ First export completed:', flattened1.length, 'rows');

        // Import back
        const imported = parseCSV(csv1);
        const nested = nestSkusFromImport(imported);
        console.log('✅ Import completed:', Object.keys(nested).length, 'SKUs');

        // Second export
        const flattened2 = flattenSkusForExport(nested);
        const csv2 = generateCSV(flattened2);
        fs.writeFileSync('backend-export2.csv', csv2);
        console.log('✅ Second export completed:', flattened2.length, 'rows');

        // Compare exports
        const export1Lines = csv1.split('\n');
        const export2Lines = csv2.split('\n');

        let differences = 0;
        const maxLines = Math.max(export1Lines.length, export2Lines.length);

        for (let i = 0; i < maxLines; i++) {
            if (export1Lines[i] !== export2Lines[i]) {
                differences++;
                if (differences <= 5) { // Show first 5 differences
                    console.log(`❌ Line ${i + 1} differs:`);
                    console.log(`   Export 1: ${export1Lines[i] || '(missing)'}`);
                    console.log(`   Export 2: ${export2Lines[i] || '(missing)'}`);
                }
            }
        }

        // Generate test report
        const report = {
            timestamp: new Date().toISOString(),
            testFile: jsonFile,
            results: {
                backendSkuCount: Object.keys(backendData.skus).length,
                frontendSkuCount: Object.keys(frontendSkus).length,
                firstExportRows: flattened1.length,
                importedSkuCount: Object.keys(nested).length,
                secondExportRows: flattened2.length,
                exportDifferences: differences,
                testPassed: differences === 0
            },
            skuDetails: {
                backend: Object.values(backendData.skus).map(sku => ({
                    skuId: sku.skuId,
                    type: sku.type,
                    name: sku.name?.en,
                    hasVariants: sku.variants?.length > 0
                })),
                frontend: Object.values(frontendSkus).map(sku => ({
                    skuId: sku.skuId,
                    type: sku.type,
                    name: sku.name || sku.nameEn,
                    variantCount: sku.variants?.length || 0
                }))
            }
        };

        fs.writeFileSync('backend-test-report.json', JSON.stringify(report, null, 2));

        console.log('\n📊 Test Results:');
        console.log(`   Backend SKUs: ${report.results.backendSkuCount}`);
        console.log(`   Frontend SKUs: ${report.results.frontendSkuCount}`);
        console.log(`   Export 1 rows: ${report.results.firstExportRows}`);
        console.log(`   Import SKUs: ${report.results.importedSkuCount}`);
        console.log(`   Export 2 rows: ${report.results.secondExportRows}`);
        console.log(`   Differences: ${differences}`);
        console.log(`   Test Status: ${differences === 0 ? '✅ PASSED' : '❌ FAILED'}`);

        if (differences === 0) {
            console.log('\n🎉 Perfect! Export-Import cycle maintains data integrity.');
        } else {
            console.log(`\n⚠️  Found ${differences} differences between exports.`);
            if (differences > 5) {
                console.log('   (Only first 5 differences shown above)');
            }
        }

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        console.error(error.stack);
    }
}

// Run the test
if (process.argv.length < 3) {
    console.log('Usage: node test-backend-format.js <json-file>');
    console.log('Example: node test-backend-format.js backend-sample.json');
    process.exit(1);
}

runTest(process.argv[2]); 
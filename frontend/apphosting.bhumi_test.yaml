# Settings for Backend (on Cloud Run).
# See https://firebase.google.com/docs/app-hosting/configure#cloud-run
runConfig:
  minInstances: 1
  # maxInstances: 100
  # concurrency: 80
  # cpu: 1
  # memoryMiB: 512

# Build configuration - exclude admin for customer deployment
buildConfig:
  commands:
    - name: "Install dependencies"
      command: "npm ci"
    - name: "Build customer version (no admin)"
      command: "npm run build:customer"

# Environment variables and secrets.
env:
  - variable: NEXT_PUBLIC_DEFAULT_LANGUAGE
    value: en
  - variable: NEXT_PUBLIC_APP_MODE
    value: customer
  - variable: NEXT_PUBLIC_API_URL
    value: https://test.apis.wow-firebase-apps.wheelocity.com/

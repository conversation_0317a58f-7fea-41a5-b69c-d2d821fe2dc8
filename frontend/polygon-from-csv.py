import csv
import json
import sys

if len(sys.argv) != 3:
    print("Usage: python polygon-from-csv.py <input-file.csv> <output-file.json>")
    sys.exit(1)

input_csv = sys.argv[1]
output_json = sys.argv[2]

terminals = []

with open(input_csv, newline='', encoding='utf-8') as csvfile:
    reader = csv.DictReader(csvfile)
    for row in reader:
        terminal = {
            "id": row["id"].strip(),
            "name": row["name"].strip(),
            "polygon": row["polygon"].strip()
        }
        terminals.append(terminal)

result = {"terminals": terminals}

with open(output_json, 'w', encoding='utf-8') as jsonfile:
    json.dump(result, jsonfile, ensure_ascii=False, indent=4)

print(f"Converted {len(terminals)} records from {input_csv} to {output_json}") 
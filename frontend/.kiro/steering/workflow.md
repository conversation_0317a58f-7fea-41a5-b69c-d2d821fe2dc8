---
inclusion: always
---

# Development Workflow & Code Standards

## Code Style & Conventions

### TypeScript Standards
- Use strict TypeScript mode with explicit return types for functions
- Prefer `interface` over `type` for object definitions
- Use `const assertions` for immutable data structures
- Always handle async operations with proper error boundaries

### Component Patterns
- **Server Components by default** - Use client components only when necessary
- **Composition over inheritance** - Prefer component composition patterns
- **Props interface naming** - Use `ComponentNameProps` convention
- **Event handlers** - Prefix with `handle` (e.g., `handleSubmit`, `handleClick`)

### Import Organization
```typescript
// 1. External libraries
import React from 'react';
import { NextPage } from 'next';

// 2. Internal utilities and types
import { formatIndianMobile } from '@/lib/utils';
import type { SKU } from '@/app/types/sku';

// 3. Components (UI first, then feature components)
import { Button } from '@/components/ui/button';
import { SkuCard } from '@/app/components/SkuCard';

// 4. Relative imports last
import { useLocalState } from './hooks/useLocalState';
```

## Architecture Patterns

### Repository Pattern Usage
- Always use singleton pattern: `Repository.getInstance()`
- Repositories handle data persistence, NOT business logic
- Use TTL-based caching for performance optimization
- Implement proper error handling and fallback strategies

### Service Layer Guidelines
- Services contain business logic, repositories handle data
- Use dependency injection pattern for testability
- Implement proper error boundaries and user feedback
- Services should be stateless and pure when possible

### State Management Rules
- Use Zustand for state management
- **Server state** - Use TanStack Query for API data management
- **Persistent state** - Use Repository pattern for offline storage

## Testing Guidelines

### Component Testing
- Test user interactions, not implementation details
- Use Testing Library queries in order of preference
- Mock external dependencies and API calls
- Test accessibility compliance

### Service Testing
- Unit test business logic thoroughly
- Mock repository dependencies
- Test error scenarios and edge cases
- Verify proper data transformations

## Multi-language Support

### i18n Best Practices
- Use translation keys that describe content, not location
- Provide fallback text for missing translations
- Test UI layout with longer translated text
- Consider RTL languages in component design

### Content Guidelines
- Keep translation keys descriptive: `cart.checkout.button` not `button1`
- Use interpolation for dynamic content
- Provide context comments for translators
- Test number and date formatting for different locales
- Use `@/lib/logger` instead of `console.log`

## Admin Portal Conventions

### Role-Based Access
- Check permissions at component level, not just route level
- Provide clear feedback for insufficient permissions
- Use consistent permission naming conventions
- Implement graceful degradation for limited access

### Data Management
- Use optimistic updates for better UX
- Implement proper validation on both client and server
- Provide bulk operations for efficiency
- Include audit trails for sensitive operations

## Mobile & Responsive Design

### Touch-First Design
- Ensure minimum 44px touch targets
- Implement proper gesture handling
- Test on actual devices, not just browser dev tools
- Consider one-handed usage patterns

### Performance on Low-End Devices
- Minimize JavaScript bundle size
- Use CSS transforms for animations
- Implement proper image lazy loading
- Test on slower network connections

## Development Process

### Task Implementation
- Complete one task at a time, including all subtasks
- Ensure functionality works end-to-end before moving to next task
- Write and verify unit tests for new functionality
- Fix all linting errors and type issues
- Update documentation and code comments
- Provide clear manual testing guidance when automated tests aren't sufficient

### Code Review Standards
- Self-review code before submission
- Validate i18n implementation with both languages
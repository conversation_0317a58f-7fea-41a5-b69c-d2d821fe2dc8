# Technology Stack

## Core Framework & Language
- **Next.js 15.3.2** - React framework with App Router
- **TypeScript 5** - Type-safe JavaScript with strict mode enabled
- **React 19** - UI library with latest features
- **Node.js** - Runtime environment

## Build System & Package Management
- **pnpm** - Fast, disk space efficient package manager (v10.10.0)
- **Turbopack** - Next.js bundler for development (faster than Webpack)
- **SWC** - Fast TypeScript/JavaScript compiler
- **PostCSS** - CSS processing with Tailwind integration

## Styling & UI
- **Tailwind CSS v4** - Utility-first CSS framework
- **Radix UI** - Headless UI components for accessibility
- **Lucide React** - Icon library
- **Heroicons** - Additional icon set
- **CSS Variables** - Custom properties for theming

## State Management & Data Fetching
- **React Context API** - Global state management (NOT Redux)
- **TanStack Query v5** - Server state management and caching
- **Zustand** - Lightweight state management for client state
- **Repository Pattern** - Data persistence layer with singleton instances

## API & HTTP
- **Axios** - HTTP client with interceptors for token management
- **Custom axios instance** - Centralized API configuration

## Storage & Caching
- **LocalForage** - Offline storage with IndexedDB/WebSQL fallback
- **Custom Repository Pattern** - Singleton-based data persistence
- **TTL-based caching** - Time-to-live cache invalidation

## Internationalization
- **react-i18next** - i18n framework
- **next-i18next** - Next.js integration for i18n
- **Localized content** - English/Tamil language support

## Testing
- **Jest** - JavaScript testing framework
- **Testing Library** - React component testing utilities
- **Playwright** - End-to-end testing framework
- **jsdom** - DOM testing environment

## Development Tools
- **ESLint** - Code linting with Next.js config
- **TypeScript strict mode** - Enhanced type checking
- **Path aliases** - `@/*` for clean imports

## Analytics & Monitoring
- **Microsoft Clarity** - User behavior analytics
- **PostHog** - Product analytics
- **Google Analytics** - Web analytics
- **Custom logging** - Centralized logger with masking

## Common Commands

### Development
```bash
# Start development server with Turbopack
pnpm dev

# Type checking
pnpm type-check

# Linting
pnpm lint
pnpm lint:fix
```

### Building & Deployment
```bash
# Production build
pnpm build

# Customer-only build (excludes admin)
pnpm build:customer

# Start production server
pnpm start
```

### Testing
```bash
# Unit tests
pnpm test
pnpm test:watch

# E2E tests
pnpm test:e2e
pnpm test:e2e:ui
```

## Environment Configuration
- **Multiple environments** - Development, test, production
- **Environment variables** - `.env`, `.env.local`, `.env.example`
- **Firebase integration** - App hosting configuration
- **Google Maps API** - Location services integration

## Performance Optimizations
- **Image optimization** - Next.js Image component with remote patterns
- **Console removal** - Production builds strip console.log (keeps error/warn)
- **React Native module handling** - Webpack/Turbopack aliases for web compatibility
- **Bundle optimization** - Tree shaking and code splitting
# Project Structure & Organization

## Root Directory Structure

```
frontend/
├── .kiro/                    # Kiro AI assistant configuration
├── src/                      # Source code
├── public/                   # Static assets
├── docs/                     # Documentation
├── tests/                    # E2E tests
├── .storage/                 # Local storage databases
└── packages/                 # Monorepo packages
```

## Source Code Organization (`src/`)

### App Router Structure (`src/app/`)
```
src/app/
├── layout.tsx               # Root layout (server component)
├── page.tsx                 # Homepage (client component)
├── globals.css              # Global styles
├── ClientProviders.tsx      # Client-side providers wrapper
├── middleware.ts            # Next.js middleware
│
├── account/                 # Customer account pages
├── admin/                   # Admin portal (excluded in customer builds)
├── categories/              # Category pages
├── product/                 # Product detail pages
│
├── components/              # Reusable UI components
│   ├── Header/             # Header components
│   ├── CartDrawer/         # Cart-related components
│   ├── Layout/             # Layout components
│   ├── ProductDetail/      # Product detail components
│   ├── CategoriesDrawer/   # Category navigation
│   └── common/             # Shared components
│
├── context/                # React Context providers
│   ├── AuthContext.tsx     # Authentication state
│   ├── SessionContext.tsx  # Multi-session cart management
│   └── OrderSuccessContext.tsx
│
├── hooks/                  # Custom React hooks
├── services/               # Business logic layer
├── repository/             # Data persistence layer
└── types/                  # TypeScript type definitions
```

## Key Architectural Patterns

### Component Organization
- **Page components** - Route handlers in app directory
- **Layout components** - Shared layouts and shells
- **Feature components** - Domain-specific UI (CartDrawer, Header)
- **Common components** - Reusable UI elements (SkeletonLoader, DatePicker)
- **UI components** - Base design system components (`src/components/ui/`)

### Service Layer Pattern
```
src/app/services/
├── authService.ts          # Authentication logic
├── skuService.ts           # Product/SKU operations
├── categoryService.ts      # Category management
├── cartService.ts          # Cart operations
├── orderService.ts         # Order processing
└── serviceSetup.ts         # Service initialization
```

### Repository Pattern (Singleton)
```
src/app/repository/
├── RepositoryBase.ts       # Base repository class
├── AuthRepository.ts       # Auth data persistence
├── SkuRepository.ts        # SKU caching
└── CategoryRepository.ts   # Category caching
```

### Type Definitions
```
src/app/types/
├── common.ts              # Shared types (LocalizedName, etc.)
├── sku.ts                 # SKU/Product types
├── category.ts            # Category types
├── auth.ts                # Authentication types
├── order.ts               # Order types
└── api.ts                 # API response types
```

## Admin Portal Structure
```
src/app/admin/
├── layout.tsx             # Admin layout with sidebar
├── page.tsx               # Admin dashboard
├── components/            # Admin-specific components
├── categories/            # Category management
├── skus/                  # SKU management
├── orders/                # Order management
├── leads/                 # Lead management
└── system-admin/          # System administration
```

## Shared Components (`src/components/ui/`)
- **Radix UI based** - Accessible headless components
- **Tailwind styled** - Utility-first styling approach
- **Consistent API** - Standardized prop interfaces

## File Naming Conventions

### Components
- **PascalCase** for component files: `SkuCard.tsx`, `CategorySection.tsx`
- **camelCase** for hooks: `useSessionCheck.ts`, `useCartFilters.ts`
- **camelCase** for services: `skuService.ts`, `authService.ts`

### Directories
- **camelCase** for feature directories: `cartDrawer/`, `productDetail/`
- **kebab-case** for route segments: `system-admin/`, `change-delivery-date/`

## Import Path Conventions
```typescript
// Absolute imports using @ alias
import { SkuCard } from '@/app/components/SkuCard';
import { formatIndianMobile } from '@/lib/utils';
import type { SKU } from '@/app/types/sku';

// Relative imports for closely related files
import { useSkuCart } from './hooks/useSkuCart';
```

## State Management Architecture

### Context Providers
- **AuthContext** - User authentication state
- **SessionContext** - Multi-session cart management
- **OrderSuccessContext** - Order completion state

### Repository Singletons
```typescript
// Always use getInstance() pattern
const skuRepo = SkuRepository.getInstance();
const authRepo = AuthRepository.getInstance();
```

### Service Dependencies
- Services use repositories for data persistence
- Services handle business logic, NOT HTTP concerns
- Axios interceptors handle HTTP-level concerns

## Storage Strategy
```
.storage/
├── infinityAppDB/         # Main application data
├── capabilities_db/       # Feature capabilities
├── node_storage_db/       # Node-specific storage
└── test_db/              # Test data storage
```

## Documentation Structure
```
docs/
├── ADMIN_PORTAL_SYSTEM.md    # Admin system overview
├── ADMIN_ROLES_SYSTEM.md     # Role-based access control
├── SKU_FIELD_ADDITION_GUIDE.md # SKU schema changes
├── SKU_MIGRATION_GUIDE.md    # Data migration procedures
├── SKU_OPERATIONS_SERVICE.md # SKU service documentation
└── SKU_SERVICE_MIGRATION.md  # Service migration guide
```

## Build Artifacts
- `.next/` - Next.js build output
- `node_modules/` - Dependencies
- `.storage/` - Runtime storage databases
- `tsconfig.tsbuildinfo` - TypeScript incremental build cache

## Configuration Files Location
- **Root level** - Framework configs (next.config.ts, tsconfig.json)
- **Package.json** - Dependencies and scripts
- **Environment files** - `.env*` files for different environments
- **Linting** - ESLint configuration files
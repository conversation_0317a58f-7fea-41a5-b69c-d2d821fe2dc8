# Product Overview

## Infinity Portal Frontend

A rural market e-commerce application designed for pre-ordering SKUs (Stock Keeping Units) in rural areas with limited connectivity. The system serves three primary user types:

- **<PERSON><PERSON><PERSON><PERSON><PERSON> (Cart Operator)**: Uses tablet/mobile interface to assist rural customers (Bhumis) with pre-orders
- **<PERSON><PERSON> (Admin/Customer Care)**: Desktop/mobile interface for product management and customer support  
- **<PERSON><PERSON><PERSON> (Customer)**: Views products via shared links

## Key Features

- **Multi-language support** (English/Tamil) with i18n
- **Session-based cart management** for handling multiple customer sessions
- **Parent-child SKU system** optimized for rural markets with poor connectivity
- **Progressive loading** and caching strategies for performance
- **Admin portal** with comprehensive product, order, and user management
- **Location-based delivery** with Google Maps integration
- **Offline-capable** design with local storage fallbacks

## Rural Market Optimizations

The application is specifically designed for rural markets with:
- Limited internet connectivity
- Older devices with performance constraints
- Need for batch operations and offline functionality
- Simplified UI patterns for ease of use
- Aggressive caching strategies to minimize API calls
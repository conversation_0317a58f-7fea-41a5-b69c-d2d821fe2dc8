import { RepositoryBase } from './RepositoryBase';
import { logger } from '@/lib/logger';

/**
 * Interface for customer details that need to be persisted
 */
export interface CustomerDetails {
    customerName: string;
    customerPhone: string;
    landmark?: string;
    location?: {
        lat: number;
        lng: number;
    };
}

/**
 * Repository for managing customer details persistence in customer-facing mode.
 * Uses localforage with IndexedDB/localStorage fallback for reliable storage.
 */
export class CustomerDetailsRepository extends RepositoryBase {
    private static readonly CUSTOMER_DETAILS_KEY = 'customer_details';
    private static instance: CustomerDetailsRepository | null = null;

    /**
     * Private constructor to enforce singleton pattern
     */
    private constructor() {
        // Initialize with store name 'customer_store' and no TTL (persistent data)
        super('customer_store');
    }

    /**
     * Gets the singleton instance of CustomerDetailsRepository
     */
    public static getInstance(): CustomerDetailsRepository {
        if (!CustomerDetailsRepository.instance) {
            CustomerDetailsRepository.instance = new CustomerDetailsRepository();
        }
        return CustomerDetailsRepository.instance;
    }

    /**
     * Saves customer details to persistent storage
     * @param details Customer details to save
     * @returns Promise that resolves when details are saved
     */
    public async saveCustomerDetails(details: CustomerDetails): Promise<void> {
        try {
            // Log without sensitive data for privacy
            logger.info('CustomerDetailsRepository: Saving customer details', {
                hasName: !!details.customerName,
                hasPhone: !!details.customerPhone,
                hasLocation: !!details.location
            });

            await this.setItem(CustomerDetailsRepository.CUSTOMER_DETAILS_KEY, details);
            logger.info('CustomerDetailsRepository: Customer details saved successfully');
        } catch (error) {
            logger.error('CustomerDetailsRepository: Failed to save customer details:', error);
            throw new Error('Failed to save customer details');
        }
    }

    /**
     * Retrieves saved customer details from persistent storage
     * @returns Promise that resolves with customer details or null if not found
     */
    public async getCustomerDetails(): Promise<CustomerDetails | null> {
        try {
            const details = await this.getItem<CustomerDetails>(CustomerDetailsRepository.CUSTOMER_DETAILS_KEY);

            if (details) {
                // Log without sensitive data for privacy
                logger.debug('CustomerDetailsRepository: Retrieved customer details', {
                    hasName: !!details.customerName,
                    hasPhone: !!details.customerPhone,
                    hasLocation: !!details.location
                });
            } else {
                logger.debug('CustomerDetailsRepository: No saved customer details found');
            }

            return details;
        } catch (error) {
            logger.error('CustomerDetailsRepository: Failed to retrieve customer details:', error);
            return null;
        }
    }

    /**
     * Clears saved customer details from persistent storage
     * @returns Promise that resolves when details are cleared
     */
    public async clearCustomerDetails(): Promise<void> {
        try {
            logger.info('CustomerDetailsRepository: Clearing customer details');
            await this.removeItem(CustomerDetailsRepository.CUSTOMER_DETAILS_KEY);
            logger.info('CustomerDetailsRepository: Customer details cleared successfully');
        } catch (error) {
            logger.error('CustomerDetailsRepository: Failed to clear customer details:', error);
            throw new Error('Failed to clear customer details');
        }
    }

    /**
     * Checks if customer details exist in storage (lightweight check using keys)
     * @returns Promise that resolves with boolean indicating if details exist
     */
    public async hasCustomerDetails(): Promise<boolean> {
        try {
            // Use the keys method from RepositoryBase for lightweight check
            const keys = await this.keys();
            const exists = keys.includes(CustomerDetailsRepository.CUSTOMER_DETAILS_KEY);

            logger.debug(`CustomerDetailsRepository: Customer details existence check: ${exists}`);
            return exists;
        } catch (error) {
            logger.error('CustomerDetailsRepository: Failed to check customer details existence:', error);
            return false;
        }
    }

    /**
     * Updates specific fields of saved customer details
     * @param updates Partial customer details to update
     * @returns Promise that resolves when details are updated
     */
    public async updateCustomerDetails(updates: Partial<CustomerDetails>): Promise<void> {
        try {
            const existingDetails = await this.getCustomerDetails();

            if (!existingDetails) {
                throw new Error('No existing customer details to update');
            }

            const updatedDetails: CustomerDetails = {
                ...existingDetails,
                ...updates
            };

            await this.saveCustomerDetails(updatedDetails);
            logger.info('CustomerDetailsRepository: Customer details updated successfully');
        } catch (error) {
            logger.error('CustomerDetailsRepository: Failed to update customer details:', error);
            throw new Error('Failed to update customer details');
        }
    }

    /**
     * Gets customer name only (lightweight operation)
     * @returns Promise that resolves with customer name or null
     */
    public async getCustomerName(): Promise<string | null> {
        try {
            const details = await this.getCustomerDetails();
            return details?.customerName || null;
        } catch (error) {
            logger.error('CustomerDetailsRepository: Failed to get customer name:', error);
            return null;
        }
    }

    /**
     * Gets customer phone only (lightweight operation)
     * @returns Promise that resolves with customer phone or null
     */
    public async getCustomerPhone(): Promise<string | null> {
        try {
            const details = await this.getCustomerDetails();
            return details?.customerPhone || null;
        } catch (error) {
            logger.error('CustomerDetailsRepository: Failed to get customer phone:', error);
            return null;
        }
    }

    /**
     * Gets customer location only (lightweight operation)
     * @returns Promise that resolves with customer location or null
     */
    public async getCustomerLocation(): Promise<{ lat: number; lng: number } | null> {
        try {
            const details = await this.getCustomerDetails();
            return details?.location || null;
        } catch (error) {
            logger.error('CustomerDetailsRepository: Failed to get customer location:', error);
            return null;
        }
    }

    /**
     * Updates only the customer location
     * @param location New location coordinates
     * @returns Promise that resolves when location is updated
     */
    public async updateCustomerLocation(location: { lat: number; lng: number }): Promise<void> {
        try {
            await this.updateCustomerDetails({ location });
            logger.info('CustomerDetailsRepository: Customer location updated successfully');
        } catch (error) {
            logger.error('CustomerDetailsRepository: Failed to update customer location:', error);
            throw new Error('Failed to update customer location');
        }
    }

    /**
     * Gets basic customer info for quick access
     * @returns Promise that resolves with basic customer info
     */
    public async getBasicCustomerInfo(): Promise<{
        hasDetails: boolean;
        hasName: boolean;
        hasPhone: boolean;
        hasLocation: boolean;
    }> {
        try {
            const details = await this.getCustomerDetails();

            if (!details) {
                return {
                    hasDetails: false,
                    hasName: false,
                    hasPhone: false,
                    hasLocation: false
                };
            }

            return {
                hasDetails: true,
                hasName: !!details.customerName,
                hasPhone: !!details.customerPhone,
                hasLocation: !!details.location
            };
        } catch (error) {
            logger.error('CustomerDetailsRepository: Failed to get basic customer info:', error);
            return {
                hasDetails: false,
                hasName: false,
                hasPhone: false,
                hasLocation: false
            };
        }
    }

    /**
     * Clears all data in the customer store (for debugging/reset purposes)
     * @returns Promise that resolves when store is cleared
     */
    public async clearAllData(): Promise<void> {
        try {
            logger.info('CustomerDetailsRepository: Clearing all customer store data');
            await this.clearStore();
            logger.info('CustomerDetailsRepository: All customer store data cleared');
        } catch (error) {
            logger.error('CustomerDetailsRepository: Failed to clear all data:', error);
            throw new Error('Failed to clear all customer data');
        }
    }
}

// Export singleton instance for easy access
export const customerDetailsRepository = CustomerDetailsRepository.getInstance();
export default customerDetailsRepository; 
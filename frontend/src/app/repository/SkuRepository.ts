import { RepositoryBase } from './RepositoryBase';
import { logger } from '@/lib/logger';
import type { SKU, SKUMap } from '@/app/types/sku';

const SKU_STORE_KEY = 'all_skus';
const SKU_LOADING_PROGRESS_KEY = 'sku_loaded_categories';
const DEFAULT_SKU_TTL_MS = 12 * 60 * 60 * 1000; // 12 hours

/**
 * Repository for SKU data persistence and caching.
 * 
 * Responsibilities:
 * - SKU data storage/retrieval with TTL
 * - SKU loading progress tracking (optimization concern)
 * - Batch SKU operations
 * 
 * Design Principles:
 * - Single responsibility: SKU data + loading optimization
 * - Performance optimized for rural markets
 * - Consistent error handling
 * - Type-safe operations
 */
export class SkuRepository extends RepositoryBase {
    private static instance: SkuRepository | null = null;

    /**
     * Get the singleton instance of SkuRepository
     */
    public static getInstance(ttlInMilliseconds?: number): SkuRepository {
        if (!SkuRepository.instance) {
            SkuRepository.instance = new SkuRepository(ttlInMilliseconds);
        }
        return SkuRepository.instance;
    }

    private constructor(ttlInMilliseconds: number = DEFAULT_SKU_TTL_MS) {
        super('sku_store', ttlInMilliseconds);
    }

    // ========================================================================
    // SKU LOADING PROGRESS TRACKING (Optimization Concern)
    // ========================================================================

    /**
     * Gets which categories have had their SKUs loaded (for progressive loading optimization)
     */
    public async getSkuLoadedCategories(): Promise<Set<string>> {
        try {
            const loadedIds = await this.getItem<string[]>(SKU_LOADING_PROGRESS_KEY);
            return new Set(loadedIds || []);
        } catch (error) {
            logger.error('SkuRepository: Error getting loaded categories:', error);
            return new Set();
        }
    }

    /**
     * Marks a category as having its SKUs loaded
     */
    public async markCategorySkusLoaded(categoryId: string): Promise<void> {
        try {
            const loadedIds = await this.getSkuLoadedCategories();
            loadedIds.add(categoryId);
            await this.setItem(SKU_LOADING_PROGRESS_KEY, Array.from(loadedIds));
        } catch (error) {
            logger.error(`SkuRepository: Error marking category ${categoryId} as loaded:`, error);
            // Don't throw - this is optimization, not critical
        }
    }

    /**
     * Marks multiple categories as having their SKUs loaded
     */
    public async markCategoriesSkusLoaded(categoryIds: string[]): Promise<void> {
        try {
            const loadedIds = await this.getSkuLoadedCategories();
            categoryIds.forEach(id => loadedIds.add(id));
            await this.setItem(SKU_LOADING_PROGRESS_KEY, Array.from(loadedIds));
        } catch (error) {
            logger.error('SkuRepository: Error marking multiple categories as loaded:', error);
            // Don't throw - this is optimization, not critical
        }
    }

    /**
     * Clears the loading progress tracking (when cache is invalidated)
     */
    public async clearLoadingProgress(): Promise<void> {
        try {
            await this.removeItem(SKU_LOADING_PROGRESS_KEY);
        } catch (error) {
            logger.error('SkuRepository: Error clearing loading progress:', error);
            // Don't throw - this is cleanup, not critical
        }
    }

    // ========================================================================
    // CORE SKU DATA OPERATIONS
    // ========================================================================

    /**
     * Saves or updates SKUs in the local store.
     * @param skusToSave The map of SKUs to save or merge
     * @param replaceAll If true, replaces all SKUs; otherwise, merges
     */
    public async saveOrUpdateSkus(skusToSave: SKUMap, replaceAll: boolean = false): Promise<void> {
        try {
            const skuCount = Object.keys(skusToSave).length;

            if (replaceAll) {
                await this.setItem<SKUMap>(SKU_STORE_KEY, skusToSave);
                logger.info(`SkuRepository: Replaced all SKUs (${skuCount} total)`);
            } else {
                const existingSkus = await this.getItem<SKUMap>(SKU_STORE_KEY);
                const mergedSkus = existingSkus ? { ...existingSkus, ...skusToSave } : skusToSave;
                await this.setItem<SKUMap>(SKU_STORE_KEY, mergedSkus);
                logger.info(`SkuRepository: Merged ${skuCount} SKUs (total: ${Object.keys(mergedSkus).length})`);
            }
        } catch (error) {
            logger.error('SkuRepository: Error saving/updating SKUs:', error);
            throw error;
        }
    }

    /**
     * Retrieves all SKUs as a map from local store
     * @returns SKUMap or null if not found/expired
     */
    public async getAllSkus(): Promise<SKUMap | null> {
        try {
            const skus = await this.getItem<SKUMap>(SKU_STORE_KEY);
            if (skus) {
                logger.debug(`SkuRepository: Loaded ${Object.keys(skus).length} SKUs from cache`);
            }
            return skus;
        } catch (error) {
            logger.error('SkuRepository: Error retrieving all SKUs:', error);
            return null;
        }
    }

    /**
     * Retrieves a single SKU by ID from local store
     * Optimized to avoid loading entire SKU map when possible
     * @param skuId The SKU ID (number or string)
     * @returns SKU or null if not found/expired
     */
    public async getSkuById(skuId: number | string): Promise<SKU | null> {
        try {
            const skuIdStr = String(skuId);
            const allSkus = await this.getAllSkus();

            if (allSkus && allSkus[skuIdStr]) {
                return allSkus[skuIdStr];
            }
            return null;
        } catch (error) {
            logger.error(`SkuRepository: Error retrieving SKU ${skuId}:`, error);
            return null;
        }
    }

    /**
     * Retrieves multiple SKUs by their IDs (batch operation)
     * @param skuIds Array of SKU IDs to retrieve
     * @returns SKUMap containing found SKUs, empty map if none found, null on error
     */
    public async getSkusByIds(skuIds: (number | string)[]): Promise<SKUMap | null> {
        if (!skuIds || skuIds.length === 0) {
            return {};
        }

        try {
            const allSkus = await this.getAllSkus();
            if (!allSkus) {
                return null;
            }

            const foundSkus: SKUMap = {};
            for (const id of skuIds) {
                const skuIdStr = String(id);
                if (allSkus[skuIdStr]) {
                    foundSkus[skuIdStr] = allSkus[skuIdStr];
                }
            }

            return foundSkus;
        } catch (error) {
            logger.error('SkuRepository: Error retrieving SKUs by IDs:', error);
            return null;
        }
    }

    /**
     * Searches SKUs by name in specified language
     * @param searchTerm Search term
     * @param language Language to search in (default: 'en')
     * @returns Array of matching SKUs, empty array if none found
     */
    public async searchSkusByName(searchTerm: string, language: string = 'en'): Promise<SKU[]> {
        if (!searchTerm || !searchTerm.trim()) {
            return [];
        }

        try {
            const allSkus = await this.getAllSkus();
            if (!allSkus) {
                return [];
            }

            const searchTermLower = searchTerm.toLowerCase().trim();
            return Object.values(allSkus).filter(sku => {
                const name = sku.name[language as keyof typeof sku.name];
                return name?.toLowerCase().includes(searchTermLower) || false;
            });
        } catch (error) {
            logger.error(`SkuRepository: Error searching SKUs by name '${searchTerm}':`, error);
            return [];
        }
    }

    /**
     * Checks if a SKU exists without fetching full data
     * @param skuId SKU ID to check
     * @returns true if SKU exists, false otherwise
     */
    public async skuExists(skuId: number | string): Promise<boolean> {
        try {
            const skuIdStr = String(skuId);
            const allSkus = await this.getAllSkus();
            return allSkus ? skuIdStr in allSkus : false;
        } catch (error) {
            logger.error(`SkuRepository: Error checking if SKU ${skuId} exists:`, error);
            return false;
        }
    }

    /**
     * Gets the total count of cached SKUs
     * @returns Number of SKUs in cache
     */
    public async getSkuCount(): Promise<number> {
        try {
            const allSkus = await this.getAllSkus();
            return allSkus ? Object.keys(allSkus).length : 0;
        } catch (error) {
            logger.error('SkuRepository: Error getting SKU count:', error);
            return 0;
        }
    }

    /**
     * Clears all SKU data and loading progress from cache
     */
    public async clearAllSkus(): Promise<void> {
        try {
            await this.clearStore();
            await this.clearLoadingProgress();
            logger.info('SkuRepository: All SKU data and loading progress cleared');
        } catch (error) {
            logger.error('SkuRepository: Error clearing SKU data:', error);
            throw error;
        }
    }
}

export default SkuRepository;
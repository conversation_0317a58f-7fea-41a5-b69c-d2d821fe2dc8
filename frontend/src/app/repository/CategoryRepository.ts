import { RepositoryBase } from './RepositoryBase';
import { logger } from '@/lib/logger';
import type { Category, CategoryMap } from '../types/category';

const CATEGORY_STORE_KEY = 'all_categories';
const CATEGORY_STORE_TTL = 4 * 1000 * 60 * 60; // 4 Hours

export class CategoryRepository extends RepositoryBase {
    private static instance: CategoryRepository;

    /**
     * Get the singleton instance of CategoryRepository
     * @returns The singleton CategoryRepository instance
     */
    public static getInstance(): CategoryRepository {
        if (!CategoryRepository.instance) {
            CategoryRepository.instance = new CategoryRepository();
        }
        return CategoryRepository.instance;
    }

    private constructor() {
        super('category_store', CATEGORY_STORE_TTL); // Unique store name for category data
    }

    /**
     * Saves the entire map of categories to the local store.
     * @param categories The map of categories to save.
     * @returns A promise that resolves when the categories have been saved.
     */
    public async saveCategories(categories: CategoryMap): Promise<void> {
        try {
            await this.setItem<CategoryMap>(CATEGORY_STORE_KEY, categories);
            logger.info(`CategoryRepository: ${Object.keys(categories).length} categories saved successfully.`);
        } catch (error) {
            logger.error('CategoryRepository: Error saving categories:', error);
            throw error; // Re-throw to allow calling code to handle
        }
    }

    /**
     * Retrieves the entire map of categories from the local store.
     * @returns A promise that resolves with the map of categories, or null if not found or an error occurs.
     */
    public async getAllCategories(): Promise<CategoryMap | null> {
        try {
            const categories = await this.getItem<CategoryMap>(CATEGORY_STORE_KEY);

            if (categories) {
                logger.debug(`CategoryRepository: Loaded ${Object.keys(categories).length} categories from storage`);
            }

            return categories;
        } catch (error) {
            logger.error('CategoryRepository: Error retrieving all categories:', error);
            return null;
        }
    }

    /**
     * Retrieves a single category by its ID (optimized for single lookups).
     * @param categoryId The ID of the category to retrieve (can be number or string).
     * @returns A promise that resolves with the Category, or null if not found.
     */
    public async getCategoryById(categoryId: number | string): Promise<Category | null> {
        try {
            const categoryIdStr = String(categoryId);
            const allCategories = await this.getAllCategories();

            if (allCategories && allCategories[categoryIdStr]) {
                return allCategories[categoryIdStr];
            }
            return null;
        } catch (error) {
            logger.error(`CategoryRepository: Error retrieving category by ID '${categoryId}':`, error);
            return null;
        }
    }

    /**
     * Retrieves multiple categories by their IDs (optimized for batch lookups).
     * @param categoryIds Array of category IDs to retrieve
     * @returns A promise that resolves with a CategoryMap containing found categories
     */
    public async getCategoriesByIds(categoryIds: (number | string)[]): Promise<CategoryMap> {
        if (!categoryIds || categoryIds.length === 0) {
            return {};
        }

        try {
            const allCategories = await this.getAllCategories();
            if (!allCategories) {
                return {};
            }

            const foundCategories: CategoryMap = {};
            for (const id of categoryIds) {
                const categoryIdStr = String(id);
                if (allCategories[categoryIdStr]) {
                    foundCategories[categoryIdStr] = allCategories[categoryIdStr];
                }
            }

            logger.debug(`CategoryRepository: Retrieved ${Object.keys(foundCategories).length}/${categoryIds.length} categories by IDs`);
            return foundCategories;
        } catch (error) {
            logger.error('CategoryRepository: Error retrieving categories by IDs:', error);
            return {};
        }
    }

    /**
     * Retrieves all categories as an array (optimized).
     * @returns A promise that resolves with an array of Category objects, or an empty array if none are found or an error occurs.
     */
    public async getAllCategoriesAsArray(): Promise<Category[]> {
        try {
            const allCategoriesMap = await this.getAllCategories();
            if (allCategoriesMap) {
                const categoriesArray = Object.values(allCategoriesMap);
                logger.debug(`CategoryRepository: Converted ${categoriesArray.length} categories to array`);
                return categoriesArray;
            }
            return [];
        } catch (error) {
            logger.error('CategoryRepository: Error retrieving categories as array:', error);
            return [];
        }
    }

    /**
     * Checks if a category exists without fetching full data
     * @param categoryId The ID of the category to check
     * @returns Promise that resolves with boolean indicating if category exists
     */
    public async categoryExists(categoryId: number | string): Promise<boolean> {
        try {
            const categoryIdStr = String(categoryId);
            const allCategories = await this.getAllCategories();
            return allCategories ? categoryIdStr in allCategories : false;
        } catch (error) {
            logger.error(`CategoryRepository: Error checking if category '${categoryId}' exists:`, error);
            return false;
        }
    }

    /**
     * Gets the count of categories
     * @returns Promise that resolves with the number of categories
     */
    public async getCategoryCount(): Promise<number> {
        try {
            const allCategories = await this.getAllCategories();
            const count = allCategories ? Object.keys(allCategories).length : 0;
            logger.debug(`CategoryRepository: Category count: ${count}`);
            return count;
        } catch (error) {
            logger.error('CategoryRepository: Error getting category count:', error);
            return 0;
        }
    }

    /**
     * Searches categories by name (case-insensitive)
     * @param searchTerm The term to search for in category names
     * @returns Promise that resolves with matching categories
     */
    public async searchCategoriesByName(searchTerm: string, language: string = 'en'): Promise<Category[]> {
        if (!searchTerm || searchTerm.trim().length === 0) {
            return [];
        }

        try {
            const allCategories = await this.getAllCategories();
            if (!allCategories) {
                return [];
            }

            const searchTermLower = searchTerm.toLowerCase().trim();
            const matchingCategories = Object.values(allCategories).filter(category =>
                category.name[language]?.toLowerCase().includes(searchTermLower)
            );

            logger.debug(`CategoryRepository: Found ${matchingCategories.length} categories matching '${searchTerm}'`);
            return matchingCategories;
        } catch (error) {
            logger.error(`CategoryRepository: Error searching categories by name '${searchTerm}':`, error);
            return [];
        }
    }

    /**
     * Updates a single category in the store
     * @param categoryId The ID of the category to update
     * @param categoryData The updated category data
     * @returns Promise that resolves when category is updated
     */
    public async updateCategory(categoryId: number | string, categoryData: Category): Promise<void> {
        try {
            const categoryIdStr = String(categoryId);
            const allCategories = await this.getAllCategories();

            if (!allCategories) {
                throw new Error('No categories found to update');
            }

            allCategories[categoryIdStr] = categoryData;
            await this.saveCategories(allCategories);

            logger.info(`CategoryRepository: Category '${categoryId}' updated successfully.`);
        } catch (error) {
            logger.error(`CategoryRepository: Error updating category '${categoryId}':`, error);
            throw error;
        }
    }

    /**
     * Clears all category data from this repository's store.
     * @returns A promise that resolves when the category data has been cleared.
     */
    public async clearAllCategories(): Promise<void> {
        try {
            await this.clearStore();
            logger.info('CategoryRepository: All categories cleared successfully.');
        } catch (error) {
            logger.error('CategoryRepository: Error clearing categories:', error);
            throw error;
        }
    }
}

export default CategoryRepository;
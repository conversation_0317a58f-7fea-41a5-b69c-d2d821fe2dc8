import localforage from 'localforage';
import { logger } from '@/lib/logger';

// Configure localforage to use a specific driver order (IndexedDB, WebSQL, localStorage)
localforage.config({
    driver: [localforage.INDEXEDDB, localforage.WEBSQL, localforage.LOCALSTORAGE],
    name: 'infinityAppDB', // Common database name for the application
});

/**
 * Interface for the wrapper object that includes the data and a timestamp.
 */
interface StoredItemWithTimestamp<T> {
    data: T;
    timestamp: number; // Milliseconds since epoch, when the item was stored
}

/**
 * Base class for repositories, providing a standardized way to interact with
 * local storage (IndexedDB with fallback to localStorage via localforage).
 *
 * Each specialized repository that extends this class will operate on its own
 * dedicated store within the common 'infinityAppDB' database, ensuring data isolation.
 */
export class RepositoryBase {
    private storeInstance: LocalForage;
    private readonly ttlInMilliseconds?: number;

    /**
     * Initializes a new instance of the RepositoryBase.
     * @param storeName The unique name for the localforage store this repository instance will manage.
     *                  This ensures that data for different repositories is kept separate.
     *                  Example: 'auth_store', 'session_store'.
     */
    constructor(storeName: string, ttlInMilliseconds?: number) {
        if (!storeName) {
            throw new Error('RepositoryBase: storeName cannot be empty.');
        }
        this.storeInstance = localforage.createInstance({
            name: 'infinityAppDB', // Should match the global config, but can be specified
            storeName: storeName, // Unique store name for this repository instance
        });
        if (ttlInMilliseconds && ttlInMilliseconds > 0) {
            this.ttlInMilliseconds = ttlInMilliseconds;
            logger.debug(`RepositoryBase initialized for store: ${storeName} with TTL: ${ttlInMilliseconds}ms`);
        } else {
            logger.debug(`RepositoryBase initialized for store: ${storeName} (no TTL)`);
        }
    }

    /**
     * Retrieves an item from the local store.
     * @param key The key of the item to retrieve.
     * @returns A promise that resolves with the item's value, or null if the key is not found or an error occurs.
     */
    protected async getItem<T>(key: string): Promise<T | null> {
        try {
            const storedWrapper = await this.storeInstance.getItem<StoredItemWithTimestamp<T>>(key);

            if (storedWrapper === null || storedWrapper === undefined) {
                return null;
            }

            // Check for TTL if it's configured
            if (this.ttlInMilliseconds && this.ttlInMilliseconds > 0) {
                const isExpired = Date.now() > storedWrapper.timestamp + this.ttlInMilliseconds;
                if (isExpired) {
                    logger.debug(`RepositoryBase (${this.storeInstance.config().storeName}): Item '${key}' has expired. Removing.`);
                    await this.removeItem(key); // This removes the stale item
                    return null;
                }
            }
            // If no TTL or item is not expired, return the data
            return storedWrapper.data;
        } catch (error) {
            logger.error(`RepositoryBase (${this.storeInstance.config().storeName}): Error getting item '${key}':`, error);
            return null;
        }
    }

    /**
     * Sets an item in the local store.
     * @param key The key of the item to set.
     * @param value The value to store.
     * @returns A promise that resolves when the item has been set.
     */
    protected async setItem<T>(key: string, value: T): Promise<void> {
        try {
            const itemToStore: StoredItemWithTimestamp<T> = {
                data: value,
                timestamp: Date.now(),
            };
            await this.storeInstance.setItem<StoredItemWithTimestamp<T>>(key, itemToStore);
        } catch (error) {
            logger.error(`RepositoryBase (${this.storeInstance.config().storeName}): Error setting item '${key}':`, error);
            // Optionally re-throw or handle more gracefully depending on requirements
            throw error;
        }
    }

    /**
     * Removes an item from the local store.
     * @param key The key of the item to remove.
     * @returns A promise that resolves when the item has been removed.
     */
    protected async removeItem(key: string): Promise<void> {
        try {
            await this.storeInstance.removeItem(key);
        } catch (error) {
            logger.error(`RepositoryBase (${this.storeInstance.config().storeName}): Error removing item '${key}':`, error);
            throw error;
        }
    }

    /**
     * Clears all items from this repository's specific local store.
     * This does NOT affect other stores within the 'infinityAppDB' database.
     * @returns A promise that resolves when the store has been cleared.
     */
    protected async clearStore(): Promise<void> {
        try {
            await this.storeInstance.clear();
            logger.debug(`RepositoryBase (${this.storeInstance.config().storeName}): Store cleared.`);
        } catch (error) {
            logger.error(`RepositoryBase (${this.storeInstance.config().storeName}): Error clearing store:`, error);
            throw error;
        }
    }

    /**
     * Gets the number of keys in this repository's specific local store.
     * @returns A promise that resolves with the number of keys.
     */
    protected async length(): Promise<number> {
        try {
            // Note: This length will be the count of StoredItemWithTimestamp objects.
            // The expiry check is done on getItem, not here.
            return await this.storeInstance.length();
        } catch (error) {
            logger.error(`RepositoryBase (${this.storeInstance.config().storeName}): Error getting store length:`, error);
            return 0;
        }
    }

    /**
     * Gets the name of the key at the specified index in this repository's specific local store.
     * @param index The index of the key.
     * @returns A promise that resolves with the key name, or null if the index is out of bounds or an error occurs.
     */
    protected async key(index: number): Promise<string | null> {
        try {
            return await this.storeInstance.key(index);
        } catch (error) {
            logger.error(`RepositoryBase (${this.storeInstance.config().storeName}): Error getting key at index ${index}:`, error);
            return null;
        }
    }

    /**
     * Gets all keys in this repository's specific local store.
     * @returns A promise that resolves with an array of all key names.
     */
    protected async keys(): Promise<string[]> {
        try {
            return await this.storeInstance.keys();
        } catch (error) {
            logger.error(`RepositoryBase (${this.storeInstance.config().storeName}): Error getting all keys:`, error);
            return [];
        }
    }
}

export default RepositoryBase; 
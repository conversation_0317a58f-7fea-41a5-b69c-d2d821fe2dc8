'use client';

import React, { useMemo } from 'react';
import { useParams } from 'next/navigation';
import { useQuery } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';
import Link from 'next/link';

// import ProductCard from '@/app/components/ProductCard'; // REMOVED
import SkuCard from '@/app/components/SkuCard'; // ADDED SkuCard
import Breadcrumbs from '@/app/components/Breadcrumbs';
import CategorySectionSkeleton from '@/app/components/common/CategorySectionSkeleton';

// Services
// import { fetchCategoryBySlug, fetchProducts } from '../../services/productService'; // REMOVED
import { getCategoryById } from '@/app/services/categoryService'; // CHANGED - was fetchCategories
import { getSkusByCategory } from '@/app/services/skuService';

// Types
import type { Category } from '@/app/types/category'; // CategoryMap no longer needed here
import type { SKU, SKUMap } from '@/app/types/sku';
import type { LocalizedName } from '@/app/types/common';

export default function CategoryPage() {
    const params = useParams();
    const categoryIdFromSlug = params.slug as string; // slug is categoryId, rename for clarity
    const { t, i18n } = useTranslation();
    const currentLanguage = i18n.language as keyof LocalizedName;

    // Step 1: Fetch the current category directly using categoryIdFromSlug
    const {
        data: currentCategory,
        isLoading: isLoadingCategory,
        isError: isErrorCategory,
        error: categoryError
    } = useQuery<Category | null, Error>({
        queryKey: ['category', categoryIdFromSlug],
        queryFn: () => getCategoryById(categoryIdFromSlug),
        staleTime: 3 * 60 * 60 * 1000, // 3 hours - cache category data
        gcTime: 3 * 60 * 60 * 1000, // 3 hours (was cacheTime in older versions)
    });

    // Step 2: Fetch SKUs for the current category (dependent on currentCategory)
    const {
        data: skusArray,
        isLoading: isLoadingSkus,
        isError: isErrorSkus,
        error: skusError
    } = useQuery<SKU[] | null, Error>({
        queryKey: ['skusForCategory', currentCategory?.id], // Depends on the fetched category's ID
        queryFn: () => currentCategory?.id ? getSkusByCategory(currentCategory.id.toString()) : Promise.resolve(null), // Customer page - excludes inactive SKUs by default
        enabled: !!currentCategory?.id, // Only run if category is successfully fetched and has an ID
        staleTime: 3 * 60 * 60 * 1000, // 3 hour - cache SKUs
        gcTime: 3 * 60 * 60 * 1000, // 3 hour (was cacheTime in older versions)
    });

    const skusForDisplay: SKU[] = useMemo(() => skusArray || [], [skusArray]);

    // Generate breadcrumb items early for immediate display
    const defaultBreadcrumbItems = [
        { name: t('breadcrumbs.home', 'Home'), href: '/' },
        { name: t('categories.page.loading', 'Loading...'), href: `/categories/${categoryIdFromSlug}` }
    ];

    // --- Loading and Error States ---
    if (isLoadingCategory) {
        return (
            <div className="w-full px-4 md:px-6 lg:px-8 py-8">
                <Breadcrumbs items={defaultBreadcrumbItems} />
                <div className="mb-6">
                    <div className="h-8 bg-gray-200 rounded w-48 animate-pulse"></div>
                </div>
                <CategorySectionSkeleton showSkuSkeletons={true} />
            </div>
        );
    }

    if (isErrorCategory) {
        return (
            <div className="w-full px-4 md:px-6 lg:px-8 py-8">
                <Breadcrumbs items={defaultBreadcrumbItems} />
                <div className="text-center text-red-600 bg-red-50 border border-red-200 rounded-lg p-6">
                    <h1 className="text-xl font-semibold mb-2">{t('categories.page.errorCategory', 'Error loading category')}</h1>
                    <p>{categoryError?.message || t('common.unknownError', 'Unknown error')}</p>
                    <button
                        onClick={() => window.location.reload()}
                        className="mt-4 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
                    >
                        {t('common.retry', 'Try Again')}
                    </button>
                </div>
            </div>
        );
    }

    if (!currentCategory) { // Category not found by ID
        return (
            <div className="w-full px-4 md:px-6 lg:px-8 py-8">
                <Breadcrumbs items={defaultBreadcrumbItems} />
                <div className="text-center bg-gray-50 border border-gray-200 rounded-lg p-8">
                    <h1 className="text-2xl font-bold mb-4">{t('categories.page.notFound.title', 'Category Not Found')}</h1>
                    <p className="text-gray-600">{t('categories.page.notFound.messageById', 'The category with ID {{categoryId}} was not found.', { categoryId: categoryIdFromSlug })}</p>
                    <Link
                        href="/"
                        className="mt-4 inline-block px-4 py-2 bg-[var(--color-green-600)] text-white rounded-md hover:brightness-90 transition-colors"
                    >
                        {t('common.backToHome', 'Back to Home')}
                    </Link>
                </div>
            </div>
        );
    }

    // At this point, currentCategory is successfully loaded.
    const categoryDisplayName = currentCategory?.name?.[currentLanguage] || currentCategory?.name?.en || t('categories.page.unnamedCategory', 'Unnamed Category');

    const breadcrumbItems = [
        { name: t('breadcrumbs.home', 'Home'), href: '/' },
        // Use categoryIdFromSlug (which is the actual ID and also the slug in URL) for the href
        { name: categoryDisplayName, href: `/categories/${categoryIdFromSlug}` }
    ];

    return (
        <div className="w-full px-4 md:px-6 lg:px-8 py-8">
            <Breadcrumbs items={breadcrumbItems} />
            <h1 className="text-xl font-bold text-gray-800 mb-6">{categoryDisplayName}</h1>

            {/* Loading state for SKUs with skeleton */}
            {isLoadingSkus && (
                <div className="space-y-4">
                    <div className="flex items-center space-x-2">
                        <div className="animate-spin rounded-full h-4 w-4 border-2 border-[var(--color-green-600)] border-t-transparent"></div>
                        <p className="text-gray-600">{t('categories.page.loadingProducts', 'Loading products...')}</p>
                    </div>
                    <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 lg:grid-cols-6 xl:grid-cols-7 gap-3 md:gap-4">
                        {Array.from({ length: 12 }).map((_, index) => (
                            <div key={index} className="animate-pulse">
                                <div className="bg-gray-200 rounded-lg aspect-square mb-2"></div>
                                <div className="h-4 bg-gray-200 rounded mb-1"></div>
                                <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                            </div>
                        ))}
                    </div>
                </div>
            )}

            {/* Error state for SKUs */}
            {isErrorSkus && (
                <div className="text-center text-red-600 bg-red-50 border border-red-200 rounded-lg p-6">
                    <h2 className="text-lg font-semibold mb-2">{t('categories.page.errorProducts', 'Error loading products')}</h2>
                    <p>{skusError?.message || t('common.unknownError', 'Unknown error')}</p>
                    <button
                        onClick={() => window.location.reload()}
                        className="mt-4 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
                    >
                        {t('common.retry', 'Try Again')}
                    </button>
                </div>
            )}

            {/* Success state - Products loaded */}
            {!isLoadingSkus && !isErrorSkus && (
                skusForDisplay && skusForDisplay.length > 0 ? (
                    <div className="grid grid-cols-2 sm:grid-cols-4 md:grid-cols-5 lg:grid-cols-6 xl:grid-cols-7 gap-3 md:gap-4 mb-20">
                        {skusForDisplay.map((skuItem: SKU) => (
                            <SkuCard key={skuItem.skuId} sku={skuItem} />
                        ))}
                    </div>
                ) : (
                    <div className="text-center bg-gray-50 border border-gray-200 rounded-lg p-8">
                        <div className="text-4xl mb-4">📦</div>
                        <h2 className="text-lg font-semibold mb-2">{t('categories.page.noProducts', 'No products found in this category.')}</h2>
                        <p className="text-gray-600">{t('categories.page.noProductsDescription', 'This category doesn\'t have any products yet.')}</p>
                    </div>
                )
            )}
        </div>
    );
}
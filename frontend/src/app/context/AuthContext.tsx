"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode, useCallback } from 'react';
import { UserDetails, AuthData, AdminRolesData, AdminPermissions } from '../types/auth';
import { requestOtpAPI, verifyOtpAPI, getAdminRoles } from '../services/authService';
import { AuthRepository } from '../repository/AuthRepository';
import { formatIndianMobile, validateIndianMobile } from '@/lib/utils';
import { EventBus } from '@/lib/eventBus';
import { useTranslation } from 'react-i18next';

export const OTP_LENGTH = 4; // Configurable OTP length

interface AuthContextType {
    isUserLoggedIn: boolean;
    userDetails: UserDetails | null;
    isLoading: boolean; // General loading for auth operations
    error: string | null; // General error for auth operations

    // Specific states for multi-step login
    isSendingOtp: boolean;
    isVerifyingOtp: boolean;
    otpSent: boolean; // To indicate if OTP has been successfully sent and UI should change

    sendOtp: (mobileNumber: string) => Promise<boolean>; // Returns true on success
    verifyOtp: (mobileNumber: string, otp: string) => Promise<boolean>; // Returns true on success
    resetOtpState: () => void; // Reset OTP sent state
    userLogout: () => void;

    // Admin roles functionality
    adminRoles: AdminRolesData | null;
    isLoadingAdminRoles: boolean;
    adminRolesChecked: boolean; // Whether we've attempted to load admin roles (prevents infinite retries)
    loadAdminRoles: () => Promise<boolean>; // Load admin roles from API
    hasPermission: (permission: keyof AdminPermissions) => boolean; // Check if user has specific permission
    hasRole: (roleName: string) => boolean; // Check if user has specific role
    clearAdminRoles: () => Promise<void>; // Clear admin roles data
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
    const { i18n } = useTranslation();
    const [isUserLoggedIn, setIsUserLoggedIn] = useState<boolean>(false);
    const [userDetails, setUserDetails] = useState<UserDetails | null>(null);
    const [isLoading, setIsLoading] = useState<boolean>(true); // Start with loading true to check repository
    const [error, setError] = useState<string | null>(null);

    const [isSendingOtp, setIsSendingOtp] = useState<boolean>(false);
    const [isVerifyingOtp, setIsVerifyingOtp] = useState<boolean>(false);
    const [otpSent, setOtpSent] = useState<boolean>(false);
    const [isClientMounted, setIsClientMounted] = useState(false);

    // Admin roles state
    const [adminRoles, setAdminRoles] = useState<AdminRolesData | null>(null);
    const [isLoadingAdminRoles, setIsLoadingAdminRoles] = useState<boolean>(false);
    const [adminRolesChecked, setAdminRolesChecked] = useState<boolean>(false); // Track if we've checked admin roles

    // Initialize auth state from repository
    useEffect(() => {
        const initializeAuth = async () => {
            setIsClientMounted(true);

            try {
                const authRepository = AuthRepository.getInstance();
                const authData = await authRepository.getAuthData();

                if (authData) {
                    setUserDetails(authData.userDetails);
                    setIsUserLoggedIn(true);

                    // Load admin roles if available
                    if (authData.adminRoles) {
                        setAdminRoles(authData.adminRoles);
                        setAdminRolesChecked(true);
                        console.log('[AuthContext] Admin roles loaded from repository');
                    }

                    console.log('[AuthContext] User authenticated from repository');
                } else {
                    console.log('[AuthContext] No valid auth data found');
                }
            } catch (error) {
                console.error('[AuthContext] Failed to initialize auth from repository:', error);
                const authRepository = AuthRepository.getInstance();
                await authRepository.clearAuthData();
                setIsUserLoggedIn(false);
                setUserDetails(null);
            } finally {
                setIsLoading(false);
            }
        };

        initializeAuth();
    }, []);

    // Listen for logout events from EventBus
    useEffect(() => {
        const eventBus = EventBus.getInstance();

        const handleLogout = () => {
            console.log('[AuthContext] Logout triggered by auth failure');
            userLogout();
        };

        eventBus.on('auth:logout', handleLogout);

        return () => {
            eventBus.off('auth:logout', handleLogout);
        };
    }, []);

    const sendOtp = async (mobileNumber: string): Promise<boolean> => {
        console.log(`[AuthContext] Requesting OTP for ${mobileNumber}`);
        setIsSendingOtp(true);
        setError(null);
        setOtpSent(false);

        try {
            // Validate mobile number
            if (!validateIndianMobile(formatIndianMobile(mobileNumber))) {
                throw new Error('Please enter a valid 10-digit mobile number');
            }

            const formattedMobile = formatIndianMobile(mobileNumber);
            const langCode = i18n.language || 'en';

            await requestOtpAPI(formattedMobile, langCode);

            setOtpSent(true);
            console.log(`[AuthContext] OTP sent successfully to ${formattedMobile}`);
            return true;
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Failed to send OTP. Please try again.';
            setError(errorMessage);
            console.error(`[AuthContext] Failed to send OTP:`, error);
            return false;
        } finally {
            setIsSendingOtp(false);
        }
    };

    const verifyOtp = async (mobileNumber: string, otp: string): Promise<boolean> => {
        console.log(`[AuthContext] Verifying OTP ${otp} for ${mobileNumber}`);
        setIsVerifyingOtp(true);
        setError(null);

        try {
            // Validate inputs
            if (!validateIndianMobile(formatIndianMobile(mobileNumber))) {
                throw new Error('Invalid mobile number');
            }

            if (otp.length !== OTP_LENGTH) {
                throw new Error(`OTP must be ${OTP_LENGTH} digits`);
            }

            const formattedMobile = formatIndianMobile(mobileNumber);
            const loginResponse = await verifyOtpAPI(formattedMobile, otp);

            // Create auth data for repository
            const authData: AuthData = {
                userDetails: loginResponse.user,
                tokenMetadata: {
                    token: loginResponse.token,
                    expiresAt: loginResponse.expiresAt,
                    refreshAfter: loginResponse.refreshAfter
                }
            };

            // Save to repository
            const authRepository = AuthRepository.getInstance();
            await authRepository.saveAuthData(authData);

            // Update state
            setUserDetails(loginResponse.user);
            setIsUserLoggedIn(true);
            setOtpSent(false); // Reset otpSent status

            console.log('[AuthContext] OTP verified successfully. User logged in.');

            // Automatically load admin roles after successful login
            try {
                console.log('[AuthContext] Attempting to load admin roles after login...');
                await loadAdminRoles();
            } catch (error) {
                console.warn('[AuthContext] Failed to load admin roles after login:', error);
                // Don't fail the login process if admin roles loading fails
            }

            return true;
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Invalid OTP. Please try again.';
            setError(errorMessage);
            console.error('[AuthContext] OTP verification failed:', error);
            return false;
        } finally {
            setIsVerifyingOtp(false);
        }
    };

    const resetOtpState = useCallback(() => {
        console.log('[AuthContext] Resetting OTP state.');
        setOtpSent(false);
        setError(null);
    }, []);

    const userLogout = useCallback(async () => {
        console.log('[AuthContext] User logging out.');

        // Clear repository
        try {
            const authRepository = AuthRepository.getInstance();
            await authRepository.clearAuthData();
        } catch (error) {
            console.error('[AuthContext] Error clearing auth data:', error);
        }

        // Reset state
        setIsUserLoggedIn(false);
        setUserDetails(null);
        setOtpSent(false);
        setError(null);
        setAdminRoles(null);
        setIsLoadingAdminRoles(false);
        setAdminRolesChecked(false);
    }, []);

    // Admin roles functions
    const loadAdminRoles = useCallback(async (): Promise<boolean> => {
        console.log('[AuthContext] Loading admin roles');
        setIsLoadingAdminRoles(true);
        setError(null);

        try {
            const response = await getAdminRoles();

            if (response) {
                // Save to repository
                const authRepository = AuthRepository.getInstance();
                await authRepository.saveAdminRoles(response);

                // Update state
                setAdminRoles(response);
                setAdminRolesChecked(true);
                console.log('[AuthContext] Admin roles loaded successfully');
                return true;
            } else {
                throw new Error('Invalid admin roles response');
            }
        } catch (error) {
            console.error('[AuthContext] Failed to load admin roles:', error);

            // Mark as checked even if failed - this prevents infinite retries
            setAdminRolesChecked(true);

            // Check if this is a "not admin" error vs a real API error
            const errorMessage = error instanceof Error ? error.message : 'Failed to load admin roles';

            // If the error suggests user is not admin, don't set it as a general error
            // This allows the UI to show "access denied" instead of "error loading"
            if (errorMessage.toLowerCase().includes('access denied') ||
                errorMessage.toLowerCase().includes('unauthorized') ||
                errorMessage.toLowerCase().includes('permission') ||
                errorMessage.toLowerCase().includes('forbidden')) {
                console.log('[AuthContext] User does not have admin permissions');
                // Don't set error state for permission issues
            } else {
                // Only set error for actual API/network issues
                setError(errorMessage);
            }

            return false;
        } finally {
            setIsLoadingAdminRoles(false);
        }
    }, []);

    const hasPermission = useCallback((permission: keyof AdminPermissions): boolean => {
        if (!adminRoles?.permissions) {
            return false;
        }
        return adminRoles.permissions[permission] === true;
    }, [adminRoles]);

    const hasRole = useCallback((roleName: string): boolean => {
        if (!adminRoles?.roleNames) {
            return false;
        }
        return adminRoles.roleNames.includes(roleName);
    }, [adminRoles]);

    const clearAdminRoles = useCallback(async (): Promise<void> => {
        console.log('[AuthContext] Clearing admin roles');

        try {
            const authRepository = AuthRepository.getInstance();
            await authRepository.clearAdminRoles();
            setAdminRoles(null);
            setAdminRolesChecked(false); // Reset checked state
            console.log('[AuthContext] Admin roles cleared successfully');
        } catch (error) {
            console.error('[AuthContext] Error clearing admin roles:', error);
        }
    }, []);

    // Don't render children until client is mounted and initial auth check is complete
    if (!isClientMounted || isLoading) {
        return null; // Or a global loading spinner for the app shell
    }

    return (
        <AuthContext.Provider
            value={{
                isUserLoggedIn,
                userDetails,
                isLoading: isLoading || isSendingOtp || isVerifyingOtp,
                error,
                isSendingOtp,
                isVerifyingOtp,
                otpSent,
                sendOtp,
                verifyOtp,
                resetOtpState,
                userLogout,
                adminRoles,
                isLoadingAdminRoles,
                adminRolesChecked,
                loadAdminRoles,
                hasPermission,
                hasRole,
                clearAdminRoles,
            }}
        >
            {children}
        </AuthContext.Provider>
    );
};

export const useAuth = () => {
    const context = useContext(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};
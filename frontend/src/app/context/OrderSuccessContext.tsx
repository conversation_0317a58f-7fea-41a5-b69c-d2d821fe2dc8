"use client";

import React, { createContext, useContext, useState, ReactNode } from 'react';

interface OrderDetails {
    total: number;
    customerName: string;
}

interface OrderSuccessContextType {
    isOrderSuccessModalOpen: boolean;
    orderDetails: OrderDetails | undefined;
    showOrderSuccess: (details: OrderDetails) => void;
    hideOrderSuccess: () => void;
}

const OrderSuccessContext = createContext<OrderSuccessContextType | undefined>(undefined);

export function OrderSuccessProvider({ children }: { children: ReactNode }) {
    const [isOrderSuccessModalOpen, setIsOrderSuccessModalOpen] = useState(false);
    const [orderDetails, setOrderDetails] = useState<OrderDetails | undefined>(undefined);

    const showOrderSuccess = (details: OrderDetails) => {
        setOrderDetails(details);
        setIsOrderSuccessModalOpen(true);
    };

    const hideOrderSuccess = () => {
        setIsOrderSuccessModalOpen(false);
        setOrderDetails(undefined);
    };

    return (
        <OrderSuccessContext.Provider
            value={{
                isOrderSuccessModalOpen,
                orderDetails,
                showOrderSuccess,
                hideOrderSuccess,
            }}
        >
            {children}
        </OrderSuccessContext.Provider>
    );
}

export function useOrderSuccess() {
    const context = useContext(OrderSuccessContext);
    if (context === undefined) {
        throw new Error('useOrderSuccess must be used within an OrderSuccessProvider');
    }
    return context;
} 
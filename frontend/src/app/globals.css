@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  /* Green/Emerald Theme */
  --primary: oklch(0.494 0.172 166.155);
  /* emerald-600 */
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-700: #334155;
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.141 0.005 285.823);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.141 0.005 285.823);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.141 0.005 285.823);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.967 0.001 286.375);
  --secondary-foreground: oklch(0.21 0.006 285.885);
  --muted: oklch(0.967 0.001 286.375);
  --muted-foreground: oklch(0.552 0.016 285.938);
  --accent: oklch(0.721 0.135 164.989);
  /* emerald-400 for accents */
  --accent-foreground: oklch(0.141 0.005 285.823);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.92 0.004 286.32);
  --input: oklch(0.92 0.004 286.32);
  --ring: oklch(0.494 0.172 166.155);
  /* emerald-600 for focus rings */
  --chart-1: oklch(0.494 0.172 166.155);
  /* emerald-600 */
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.721 0.135 164.989);
  /* emerald-400 */
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.141 0.005 285.823);
  --sidebar-primary: oklch(0.494 0.172 166.155);
  /* emerald-600 */
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.967 0.001 286.375);
  --sidebar-accent-foreground: oklch(0.21 0.006 285.885);
  --sidebar-border: oklch(0.92 0.004 286.32);
  --sidebar-ring: oklch(0.494 0.172 166.155);
  /* emerald-600 */
}

body {
  font-family: var(--font-geist-sans);
  -webkit-font-smoothing: antialiased;
}

/* Custom utility classes */
@layer utilities {

  /* Hide scrollbar but allow scrolling */
  .no-scrollbar {
    -ms-overflow-style: none;
    /* IE and Edge */
    scrollbar-width: none;
    /* Firefox */
  }

  .no-scrollbar::-webkit-scrollbar {
    display: none;
    /* Chrome, Safari and Opera */
  }

  /* Line clamp utilities for text truncation */
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  /* Shimmer animation for skeleton loaders */
  @keyframes shimmer {
    0% {
      background-position: -200px 0;
    }

    100% {
      background-position: calc(200px + 100%) 0;
    }
  }

  .animate-shimmer {
    animation: shimmer 1.5s ease-in-out infinite;
  }
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

/* Dark Theme Variants */

/* Discord Dark Theme */
.dark {
  --background: oklch(0.16 0.008 290);
  --foreground: oklch(0.93 0 0);
  --card: oklch(0.20 0.009 290);
  --card-foreground: oklch(0.93 0 0);
  --popover: oklch(0.20 0.009 290);
  --popover-foreground: oklch(0.93 0 0);
  --primary: oklch(0.721 0.135 164.989);
  --primary-foreground: oklch(0.16 0.008 290);
  --secondary: oklch(0.26 0.010 290);
  --secondary-foreground: oklch(0.93 0 0);
  --muted: oklch(0.26 0.010 290);
  --muted-foreground: oklch(0.62 0.014 290);
  --accent: oklch(0.494 0.172 166.155);
  --accent-foreground: oklch(0.93 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 14%);
  --ring: oklch(0.721 0.135 164.989);
  --chart-1: oklch(0.721 0.135 164.989);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.494 0.172 166.155);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.18 0.009 290);
  --sidebar-foreground: oklch(0.88 0 0);
  --sidebar-primary: oklch(0.721 0.135 164.989);
  --sidebar-primary-foreground: oklch(0.16 0.008 290);
  --sidebar-accent: oklch(0.28 0.010 290);
  --sidebar-accent-foreground: oklch(0.93 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.721 0.135 164.989);
}



@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
  }
}
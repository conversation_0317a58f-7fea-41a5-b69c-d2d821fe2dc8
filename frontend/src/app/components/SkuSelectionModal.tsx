'use client';

import React, { useState, useEffect, useMemo, useRef } from 'react';
import Image from 'next/image';
import Modal from 'react-modal';
import { MagnifyingGlassIcon } from '@heroicons/react/24/outline';
import { searchSkusProgressive } from '@/app/services/skuService';
import type { SKU } from '@/app/types/sku';
import i18n from '@/i18n';

interface SkuSelectionModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSelect: (selectedSkus: SKU[]) => void;
    title: string;
    searchPlaceholder?: string;
    filterType: 'all' | 'parent' | 'child';
    activeOnly?: boolean;
    excludeSkuIds?: number[];
}

const SkuSelectionModal: React.FC<SkuSelectionModalProps> = ({
    isOpen,
    onClose,
    onSelect,
    title,
    searchPlaceholder = 'Search SKUs...',
    filterType,
    activeOnly = false,
    excludeSkuIds = [],
}) => {
    const [allSkus, setAllSkus] = useState<SKU[]>([]);
    const [selectedSkuIds, setSelectedSkuIds] = useState<Set<string>>(new Set());
    const [searchQuery, setSearchQuery] = useState('');
    const [loading, setLoading] = useState(false);
    const [loadingProgress, setLoadingProgress] = useState<string>('');
    const currentLoadRef = useRef<boolean>(false);

    // Load all SKUs when modal opens using progressive search
    useEffect(() => {
        const loadAllSkus = async () => {
            if (!isOpen) return;

            setLoading(true);
            setLoadingProgress('Loading SKUs...');
            currentLoadRef.current = true;
            console.log('SkuSelectionModal: Starting comprehensive SKU loading...');

            try {
                // First, try to trigger progressive loading to ensure all categories are loaded
                // We'll use a callback to track progress and get the final results
                await searchSkusProgressive(
                    'sku', // Use 'sku' as it's likely to appear in many SKU names
                    (progressData) => {
                        // Only update if this modal is still open
                        if (currentLoadRef.current && isOpen) {
                            console.log('SkuSelectionModal: Progress update:', {
                                skuCount: progressData.skus.length,
                                isComplete: progressData.isComplete,
                                loadedCategories: progressData.loadedCategories,
                                totalCategories: progressData.totalCategories,
                                message: progressData.message
                            });

                            // Update loading progress
                            if (progressData.totalCategories > 0) {
                                const percentage = Math.round((progressData.loadedCategories / progressData.totalCategories) * 100);
                                setLoadingProgress(`Loading categories: ${progressData.loadedCategories}/${progressData.totalCategories} (${percentage}%)`);
                            } else {
                                setLoadingProgress(progressData.message || 'Loading SKUs...');
                            }

                            // When loading is complete, get all SKUs using getSkus
                            if (progressData.isComplete) {
                                console.log('SkuSelectionModal: Progressive loading complete, now getting all SKUs...');

                                // Now that all categories are loaded, get all SKUs from cache
                                import('@/app/services/skuService').then(async ({ getSkus }) => {
                                    try {
                                        const allSkus = await getSkus(); // Should now have all SKUs in cache
                                        console.log('SkuSelectionModal: Retrieved all SKUs from cache:', allSkus.length);

                                        // Apply filters to all loaded SKUs
                                        let filteredSkus = allSkus;
                                        console.log('SkuSelectionModal: Initial SKUs before filtering:', filteredSkus.length);

                                        // Filter by type
                                        if (filterType === 'parent') {
                                            filteredSkus = filteredSkus.filter(sku => sku.type === 'parent');
                                            console.log('SkuSelectionModal: After parent filter:', filteredSkus.length);
                                        } else if (filterType === 'child') {
                                            filteredSkus = filteredSkus.filter(sku => sku.type === 'child');
                                            console.log('SkuSelectionModal: After child filter:', filteredSkus.length);
                                        }

                                        // Filter by active status
                                        if (activeOnly) {
                                            const beforeActiveFilter = filteredSkus.length;
                                            filteredSkus = filteredSkus.filter(sku => sku.isActive === 1);
                                            console.log(`SkuSelectionModal: After active filter: ${filteredSkus.length} (was ${beforeActiveFilter})`);
                                        }

                                        // Exclude specified SKUs
                                        const excludeSet = new Set(excludeSkuIds);
                                        const beforeExcludeFilter = filteredSkus.length;
                                        filteredSkus = filteredSkus.filter(sku => !excludeSet.has(sku.skuId));
                                        console.log(`SkuSelectionModal: After exclude filter: ${filteredSkus.length} (was ${beforeExcludeFilter})`);
                                        console.log('SkuSelectionModal: Excluded SKU IDs:', Array.from(excludeSet));

                                        console.log('SkuSelectionModal: Final filtered SKUs:', filteredSkus.length);
                                        console.log('SkuSelectionModal: Sample SKUs:', filteredSkus.slice(0, 3).map(sku => ({ id: sku.skuId, name: sku.name.en, type: sku.type, active: sku.isActive })));

                                        if (currentLoadRef.current && isOpen) {
                                            setAllSkus(filteredSkus);
                                            setLoading(false);
                                            setLoadingProgress('');
                                        }
                                    } catch (error) {
                                        console.error('Error getting SKUs after progressive load:', error);
                                        if (currentLoadRef.current && isOpen) {
                                            setLoading(false);
                                            setLoadingProgress('');
                                            setAllSkus([]);
                                        }
                                    }
                                });
                            }
                        }
                    },
                    { maxResults: 10000, language: i18n.language } // High limit to get all SKUs
                );

            } catch (error) {
                console.error('Failed to load SKUs with progressive search:', error);
                if (currentLoadRef.current && isOpen) {
                    setLoading(false);
                    setLoadingProgress('');
                    setAllSkus([]);
                }
            }
        };

        loadAllSkus();

        // Cleanup function
        return () => {
            currentLoadRef.current = false;
        };
    }, [isOpen, filterType, activeOnly, excludeSkuIds]);

    // Filter SKUs based on search query
    const filteredAvailableSkus = useMemo(() => {
        if (!searchQuery) return allSkus;

        return allSkus.filter(sku =>
            sku.name.en.toLowerCase().includes(searchQuery.toLowerCase()) ||
            (sku.name.ta && sku.name.ta.toLowerCase().includes(searchQuery.toLowerCase()))
        );
    }, [allSkus, searchQuery]);

    // Handle SKU selection
    const handleSkuSelection = (skuId: string, checked: boolean) => {
        setSelectedSkuIds(prev => {
            const newSet = new Set(prev);
            if (checked) {
                newSet.add(skuId);
            } else {
                newSet.delete(skuId);
            }
            return newSet;
        });
    };

    // Handle select all
    const handleSelectAll = (checked: boolean) => {
        if (checked) {
            setSelectedSkuIds(new Set(filteredAvailableSkus.map(sku => sku.skuId.toString())));
        } else {
            setSelectedSkuIds(new Set());
        }
    };

    // Handle add selected SKUs
    const handleAddSelectedSkus = () => {
        const selectedSkus = Array.from(selectedSkuIds)
            .map(id => allSkus.find(sku => sku.skuId.toString() === id))
            .filter((sku): sku is SKU => sku !== undefined);
        onSelect(selectedSkus);
        handleCloseModal();
    };

    // Handle close modal
    const handleCloseModal = () => {
        setSelectedSkuIds(new Set());
        setSearchQuery('');
        onClose();
    };

    // Check if all filtered SKUs are selected
    const isAllSelected = filteredAvailableSkus.length > 0 && selectedSkuIds.size === filteredAvailableSkus.length;

    return (
        <Modal
            isOpen={isOpen}
            onRequestClose={handleCloseModal}
            contentLabel={title}
            className="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white focus:outline-none"
            shouldCloseOnOverlayClick={true}
            shouldCloseOnEsc={true}
        >
            <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">{title}</h3>
                <button
                    onClick={handleCloseModal}
                    className="text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded"
                >
                    <span className="sr-only">Close</span>
                    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>

            {/* Search */}
            <div className="mb-4">
                <div className="relative">
                    <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-3 text-gray-400" />
                    <input
                        type="text"
                        placeholder={searchPlaceholder}
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                </div>
            </div>

            {/* Available SKUs */}
            <div className="max-h-96 overflow-y-auto border border-gray-200 rounded-lg">
                {loading ? (
                    <div className="text-center py-8">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                        <p className="text-gray-500 mt-2">{loadingProgress}</p>
                    </div>
                ) : filteredAvailableSkus.length === 0 ? (
                    <div className="text-center py-8">
                        <p className="text-gray-500">
                            {searchQuery ? 'No SKUs found matching your search.' : 'No available SKUs to add.'}
                        </p>
                    </div>
                ) : (
                    <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                            <tr>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <input
                                        type="checkbox"
                                        checked={isAllSelected}
                                        onChange={(e) => handleSelectAll(e.target.checked)}
                                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                    />
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    SKU
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Name
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Price
                                </th>
                                {filterType === 'all' && (
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Type
                                    </th>
                                )}
                            </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                            {filteredAvailableSkus.map((sku) => (
                                <tr key={sku.skuId} className="hover:bg-gray-50">
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <input
                                            type="checkbox"
                                            checked={selectedSkuIds.has(sku.skuId.toString())}
                                            onChange={(e) => handleSkuSelection(sku.skuId.toString(), e.target.checked)}
                                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                        />
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="flex items-center">
                                            <div className="h-8 w-8 flex-shrink-0">
                                                <Image
                                                    src={sku.imageUrl || '/placeholder-product.png'}
                                                    alt={sku.name.en}
                                                    width={32}
                                                    height={32}
                                                    className="h-8 w-8 rounded object-cover"
                                                />
                                            </div>
                                            <div className="ml-3">
                                                <div className="text-sm font-medium text-gray-900">#{sku.skuId}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td className="px-6 py-4">
                                        <div className="text-sm font-medium text-gray-900">{sku.name.en}</div>
                                        {sku.name.ta && (
                                            <div className="text-sm text-gray-500">{sku.name.ta}</div>
                                        )}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="text-sm text-gray-900">₹{sku.sellingPrice}</div>
                                    </td>
                                    {filterType === 'all' && (
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${sku.type === 'parent'
                                                ? 'bg-purple-100 text-purple-800'
                                                : 'bg-blue-100 text-blue-800'
                                                }`}>
                                                {sku.type}
                                            </span>
                                        </td>
                                    )}
                                </tr>
                            ))}
                        </tbody>
                    </table>
                )}
            </div>

            {/* Modal Actions */}
            <div className="flex items-center justify-between mt-6">
                <p className="text-sm text-gray-500">
                    {selectedSkuIds.size} SKU(s) selected
                </p>
                <div className="flex space-x-3">
                    <button
                        onClick={handleCloseModal}
                        className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                        Cancel
                    </button>
                    <button
                        onClick={handleAddSelectedSkus}
                        disabled={selectedSkuIds.size === 0}
                        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                        Add Selected ({selectedSkuIds.size})
                    </button>
                </div>
            </div>
        </Modal>
    );
};

export default SkuSelectionModal; 
'use client';

import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useQuery } from '@tanstack/react-query';
import { getOrderedCategories } from '@/app/services/categoryService';
import type { Category } from '@/app/types/category';
import type { LocalizedName } from '@/app/types/common';
import DynamicHeroIcon from '@/app/components/common/DynamicHeroIcon';

// Interface for the data structure expected by the rendering logic
interface DisplayCategory {
    id: number;
    title: string;
    icon?: string;
    background?: string;
}

interface CategoriesListProps {
    onSelectCategory: (categoryId: string) => void;
    className?: string;
    activeCategoryId?: string; // Current active category ID for highlighting
    isCollapsed?: boolean; // Whether the sidebar is collapsed (show only icons)
    onPrefetchCategory?: (categoryId: string) => void; // Optional prefetch function
    includeInactive?: boolean; // For admin components
}

export default function CategoriesList({ onSelectCategory, className = "", activeCategoryId, isCollapsed = false, onPrefetchCategory, includeInactive = false }: CategoriesListProps) {
    const { t, i18n } = useTranslation();
    const currentLanguage = i18n.language as keyof LocalizedName;
    const [loadingCategoryId, setLoadingCategoryId] = useState<string | null>(null);

    const {
        data: displayCategories,
        isLoading,
        isError,
        error
    } = useQuery<Category[], Error, DisplayCategory[]>({
        queryKey: ['allCategories', includeInactive],
        queryFn: () => getOrderedCategories({ includeInactive }), // Direct service call
        select: (categories) => {
            if (!categories) return [];
            return categories.map((category: Category) => {
                let title: string | undefined;
                // Prioritize current language directly if it's a known key
                if (currentLanguage in category.name) {
                    title = category.name[currentLanguage];
                }
                // Fallback to English if current language title wasn't found or applicable
                if (!title && category.name.en) {
                    title = category.name.en;
                }
                // Final fallback if no title could be determined
                if (!title) {
                    title = t('categoriesDrawer.missingTitle', 'Unnamed Category');
                }
                return {
                    id: category.id,
                    title: title as string,
                    icon: category.icon,
                    background: category.background,
                };
            });
        },
        staleTime: 1000 * 60 * 5, // 5 minutes
    });

    const handleCategoryClick = (categoryId: string) => {
        // Show loading state immediately
        setLoadingCategoryId(categoryId);

        // If clicking the currently active category, scroll to top
        if (activeCategoryId && categoryId === activeCategoryId) {
            window.scrollTo({ top: 0, behavior: 'smooth' });
            setLoadingCategoryId(null);
        } else {
            // Navigate to the category
            onSelectCategory(categoryId);
            // Note: Loading state will be cleared when component unmounts or parent re-renders

            // Clear loading state after a reasonable timeout as backup
            setTimeout(() => {
                setLoadingCategoryId(null);
            }, 3000);
        }
    };

    if (isLoading) {
        return <p className="p-4 text-gray-500">{t('categoriesDrawer.loading', 'Loading categories...')}</p>;
    }

    if (isError) {
        return <p className="p-4 text-red-600">{t('categoriesDrawer.error', 'Error loading categories:')} {error?.message}</p>;
    }

    if (!displayCategories || displayCategories.length === 0) {
        return <p className="p-4 text-gray-500">{t('categoriesDrawer.noCategories', 'No categories found.')}</p>;
    }

    return (
        <ul className={`space-y-1 ${className}`}>
            {displayCategories.map((category) => {
                const itemStyle: React.CSSProperties = {};
                // if (category.background) {
                //     itemStyle.backgroundColor = category.background;
                // }

                const isActive = activeCategoryId === category.id.toString();
                const isLoading = loadingCategoryId === category.id.toString();
                const fontWeight = isActive ? 'font-bold' : 'font-medium';

                return (
                    <li key={category.id} className="rounded-md" style={itemStyle}>
                        <button
                            onClick={() => handleCategoryClick(category.id.toString())}
                            onMouseEnter={() => onPrefetchCategory?.(category.id.toString())}
                            disabled={isLoading}
                            className={`w-full text-left rounded-md hover:bg-gray-100/50 hover:text-[var(--color-green-600)] transition-all duration-200 text-sm ${fontWeight} flex items-center ${isCollapsed
                                ? 'px-2 py-3 justify-center hover:scale-105'
                                : 'px-3 py-2.5 space-x-3'
                                } ${isActive ? 'bg-[var(--color-green-50)] text-[var(--color-green-600)] border border-[var(--color-green-200)]' : ''} ${isLoading ? 'opacity-60 cursor-not-allowed' : ''}`}
                            title={isCollapsed ? category.title : undefined}
                        >

                            {category.icon && (
                                <DynamicHeroIcon
                                    iconName={category.icon}
                                    className={`${isCollapsed ? 'h-6 w-6' : 'h-5 w-5'} transition-colors ${isActive
                                        ? 'text-[var(--color-green-600)]'
                                        : 'text-gray-500 group-hover:text-[var(--color-green-600)]'
                                        }`}
                                />
                            )}

                            {!isCollapsed && (
                                <span className="flex items-center space-x-2">
                                    <span>{category.title}</span>
                                    {isLoading && (
                                        <div className="animate-spin rounded-full h-3 w-3 border-2 border-[var(--color-green-600)] border-t-transparent"></div>
                                    )}
                                </span>
                            )}
                        </button>
                    </li>
                );
            })}
        </ul>
    );
}
"use client";

import React from 'react';
import { useAdminPermissions } from '../../hooks/useAdminPermissions';
import { AdminPermissions } from '../../types/auth';

interface AdminGuardProps {
    children: React.ReactNode;

    // Permission-based access
    requiredPermission?: keyof AdminPermissions;
    requiredPermissions?: (keyof AdminPermissions)[]; // User must have ALL permissions
    anyPermissions?: (keyof AdminPermissions)[]; // User must have ANY permission

    // Role-based access
    requiredRole?: string;
    requiredRoles?: string[]; // User must have ALL roles
    anyRoles?: string[]; // User must have ANY role

    // General admin access
    requireAdmin?: boolean; // User must have any admin role

    // Fallback content
    fallback?: React.ReactNode;

    // Loading state
    showLoadingState?: boolean;
}

/**
 * AdminGuard component for conditional rendering based on admin permissions and roles
 * 
 * Usage examples:
 * 
 * // Require specific permission
 * <AdminGuard requiredPermission="manageUsers">
 *   <UserManagementPanel />
 * </AdminGuard>
 * 
 * // Require any admin role
 * <AdminGuard requireAdmin>
 *   <AdminDashboard />
 * </AdminGuard>
 * 
 * // Require multiple permissions (ALL)
 * <AdminGuard requiredPermissions={["manageUsers", "editSales"]}>
 *   <AdvancedAdminPanel />
 * </AdminGuard>
 * 
 * // Require any of the permissions
 * <AdminGuard anyPermissions={["viewAndEditPjp", "pjpTracking"]}>
 *   <PjpManagement />
 * </AdminGuard>
 */
export const AdminGuard: React.FC<AdminGuardProps> = ({
    children,
    requiredPermission,
    requiredPermissions,
    anyPermissions,
    requiredRole,
    requiredRoles,
    anyRoles,
    requireAdmin = false,
    fallback = null,
    showLoadingState = true
}) => {
    const {
        isLoadingAdminRoles,
        isAdmin,
        checkPermission,
        checkRole,
        hasAllPermissions,
        hasAnyPermission
    } = useAdminPermissions();

    // Show loading state if requested and currently loading
    if (showLoadingState && isLoadingAdminRoles) {
        return (
            <div className="flex items-center justify-center p-4">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                <span className="ml-2 text-sm text-gray-600">Loading permissions...</span>
            </div>
        );
    }

    // Check admin requirement
    if (requireAdmin && !isAdmin()) {
        return <>{fallback}</>;
    }

    // Check single permission
    if (requiredPermission && !checkPermission(requiredPermission)) {
        return <>{fallback}</>;
    }

    // Check multiple permissions (ALL required)
    if (requiredPermissions && !hasAllPermissions(requiredPermissions)) {
        return <>{fallback}</>;
    }

    // Check any permissions (ANY required)
    if (anyPermissions && !hasAnyPermission(anyPermissions)) {
        return <>{fallback}</>;
    }

    // Check single role
    if (requiredRole && !checkRole(requiredRole)) {
        return <>{fallback}</>;
    }

    // Check multiple roles (ALL required)
    if (requiredRoles && !requiredRoles.every(role => checkRole(role))) {
        return <>{fallback}</>;
    }

    // Check any roles (ANY required)
    if (anyRoles && !anyRoles.some(role => checkRole(role))) {
        return <>{fallback}</>;
    }

    // All checks passed, render children
    return <>{children}</>;
}; 
import React from 'react';
import SkeletonLoader from './SkeletonLoader';

export default function SkuCardSkeleton() {
    return (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            {/* Product Image Skeleton - 1:1 aspect ratio with padding */}
            <div className="relative aspect-square p-2">
                <SkeletonLoader
                    width="w-full"
                    height="h-full"
                    rounded="lg"
                    className="absolute inset-2"
                />
                {/* Discount Badge Skeleton */}
                <div className="absolute top-0 left-0">
                    <SkeletonLoader
                        width="w-12"
                        height="h-5"
                        rounded="sm"
                    />
                </div>
            </div>

            {/* Product Details Skeleton */}
            <div className="p-2 space-y-1">
                {/* Product Title - 2 lines */}
                <div className="space-y-1">
                    <SkeletonLoader
                        width="w-full"
                        height="h-3"
                        rounded="sm"
                    />
                    <SkeletonLoader
                        width="w-3/4"
                        height="h-3"
                        rounded="sm"
                    />
                </div>

                {/* Variant/Unit */}
                <SkeletonLoader
                    width="w-16"
                    height="h-3"
                    rounded="sm"
                />
            </div>

            {/* Price and ADD Button Row */}
            <div className="p-2 pt-0 flex items-center justify-between">
                {/* Price Section - Stacked */}
                <div className="flex flex-col space-y-1">
                    <SkeletonLoader
                        width="w-10"
                        height="h-4"
                        rounded="sm"
                    />
                    <SkeletonLoader
                        width="w-8"
                        height="h-3"
                        rounded="sm"
                    />
                </div>

                {/* ADD Button */}
                <SkeletonLoader
                    width="w-14"
                    height="h-6"
                    rounded="md"
                />
            </div>
        </div>
    );
} 
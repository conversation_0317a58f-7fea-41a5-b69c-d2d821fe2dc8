import React, { forwardRef } from 'react';
import ReactDatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';

interface DatePickerProps {
    value: string; // YYYY-MM-DD format for API compatibility
    onChange: (value: string) => void;
    placeholder?: string;
    disabled?: boolean;
    className?: string;
    label?: string;
    error?: string;
    required?: boolean;
}

/**
 * Custom DatePicker component that consistently displays dd/MM/YYYY format
 * regardless of user's browser/system locale
 */
const DatePicker: React.FC<DatePickerProps> = ({
    value,
    onChange,
    placeholder,
    disabled = false,
    className = '',
    label,
    error,
    required = false
}) => {
    // Convert YYYY-MM-DD string to Date object for react-datepicker
    const parseDate = (dateString: string): Date | null => {
        if (!dateString) return null;
        const date = new Date(dateString);
        return isNaN(date.getTime()) ? null : date;
    };

    // Convert Date object to YYYY-MM-DD string for API
    const formatDateForAPI = (date: Date | null): string => {
        if (!date) return '';
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    };

    // Format date as dd/MM/YYYY for display (currently unused but kept for future use)
    // const formatDateForDisplay = (date: Date): string => {
    //     const day = String(date.getDate()).padStart(2, '0');
    //     const month = String(date.getMonth() + 1).padStart(2, '0');
    //     const year = date.getFullYear();
    //     return `${day}/${month}/${year}`;
    // };

    const selectedDate = parseDate(value);

    const handleDateChange = (date: Date | null) => {
        const formattedDate = formatDateForAPI(date);
        onChange(formattedDate);
    };

    // Custom input component to ensure consistent styling
    const CustomInput = forwardRef<HTMLInputElement, { value?: string; onClick?: () => void; placeholder?: string }>(({ value, onClick, placeholder }, ref) => (
        <input
            ref={ref}
            value={value}
            onClick={onClick}
            placeholder={placeholder}
            readOnly
            className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white cursor-pointer ${disabled ? 'bg-gray-100 cursor-not-allowed' : ''
                } ${error ? 'border-red-500' : ''} ${className}`}
        />
    ));

    CustomInput.displayName = 'CustomInput';

    return (
        <div className="w-full">
            {label && (
                <label className="block text-sm font-medium text-gray-700 mb-2">
                    {label}
                    {required && <span className="text-red-500 ml-1">*</span>}
                </label>
            )}

            <ReactDatePicker
                selected={selectedDate}
                onChange={handleDateChange}
                dateFormat="dd/MM/yyyy"
                placeholderText={placeholder || 'dd/MM/yyyy'}
                disabled={disabled}
                customInput={<CustomInput />}
                showPopperArrow={false}
                popperClassName="z-50"
                calendarClassName="shadow-lg border border-gray-200"
                dayClassName={(_date) =>
                    'hover:bg-blue-500 hover:text-white rounded-md transition-colors cursor-pointer'
                }
                weekDayClassName={() => 'text-gray-600 font-medium'}
                monthClassName={() => 'hover:bg-blue-500 hover:text-white rounded-md transition-colors cursor-pointer'}
                showMonthDropdown
                showYearDropdown
                dropdownMode="select"
            />

            {error && (
                <p className="mt-1 text-sm text-red-600">
                    {error}
                </p>
            )}
        </div>
    );
};

export default DatePicker; 
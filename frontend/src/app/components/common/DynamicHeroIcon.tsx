import React, { useMemo } from 'react';
import * as HIconsOutline from '@heroicons/react/24/outline';
import * as HIconsSolid from '@heroicons/react/24/solid'; // Assuming you might use solid icons too
import DOMPurify from 'dompurify';

// Combine all icons into one object for easier lookup.
// Ensure there are no name clashes if using both outline and solid with identical names.
// If there are, you might need to prefix them or handle them differently.
const allHeroIcons = {
    ...HIconsOutline,
    ...HIconsSolid,
};

// Create a type that represents valid HeroIcon names based on the keys of the imported object.
export type HeroIconName = keyof typeof allHeroIcons;

// SVG size limit (10KB)
const MAX_SVG_SIZE = 10240;

// DOMPurify configuration for SVGs
const svgSanitizeConfig = {
    USE_PROFILES: { svg: true, svgFilters: true },
    ALLOWED_TAGS: [
        'svg', 'path', 'circle', 'rect', 'g', 'polygon', 'polyline',
        'line', 'ellipse', 'text', 'defs', 'linearGradient',
        'radialGradient', 'stop', 'clipPath', 'mask'
    ],
    ALLOWED_ATTR: [
        'viewBox', 'width', 'height', 'd', 'cx', 'cy', 'r', 'x', 'y',
        'fill', 'stroke', 'stroke-width', 'transform', 'class', 'id',
        'opacity', 'fill-opacity', 'stroke-opacity', 'stroke-linecap',
        'stroke-linejoin', 'stroke-dasharray', 'stroke-dashoffset'
    ],
    FORBID_TAGS: ['script', 'iframe', 'object', 'embed', 'link', 'style'],
    FORBID_ATTR: ['onclick', 'onload', 'onerror', 'onmouseover', 'javascript', 'href']
};

interface DynamicHeroIconProps {
    iconName?: HeroIconName | string; // Allow string for flexibility, but HeroIconName is preferred for type safety
    className?: string;
    ariaHidden?: boolean;
}

/**
 * Validates and sanitizes SVG content
 */
const validateAndSanitizeSVG = (svgContent: string): { isValid: boolean; sanitized?: string; error?: string } => {
    try {
        // Size check
        if (svgContent.length > MAX_SVG_SIZE) {
            return {
                isValid: false,
                error: `SVG too large (${Math.round(svgContent.length / 1024)}KB). Maximum size is 10KB.`
            };
        }

        // Basic structure check
        if (!svgContent.trim().startsWith('<svg') || !svgContent.trim().endsWith('</svg>')) {
            return {
                isValid: false,
                error: 'Invalid SVG structure. Must start with <svg and end with </svg>'
            };
        }

        // Sanitize with DOMPurify
        const sanitized = DOMPurify.sanitize(svgContent, svgSanitizeConfig);

        if (!sanitized || sanitized.trim().length === 0) {
            return {
                isValid: false,
                error: 'SVG content was removed during sanitization (may contain unsafe elements)'
            };
        }

        // Test if sanitized SVG can be parsed
        const parser = new DOMParser();
        const doc = parser.parseFromString(sanitized, 'image/svg+xml');
        const parserError = doc.querySelector('parsererror');

        if (parserError) {
            return {
                isValid: false,
                error: 'SVG contains syntax errors and cannot be parsed'
            };
        }

        return { isValid: true, sanitized };

    } catch (error) {
        return {
            isValid: false,
            error: `SVG validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
        };
    }
};

const DynamicHeroIcon: React.FC<DynamicHeroIconProps> = ({
    iconName,
    className = 'h-5 w-5', // Default size
    ariaHidden = true
}) => {
    const renderContent = useMemo(() => {
        if (!iconName) {
            // Optionally render a default fallback or nothing if no iconName is provided
            return <HIconsOutline.QuestionMarkCircleIcon className={className} aria-hidden={ariaHidden} />;
        }

        // Check if it's an SVG string
        if (typeof iconName === 'string' && iconName.trim().startsWith('<svg')) {
            const svgResult = validateAndSanitizeSVG(iconName);

            if (!svgResult.isValid) {
                console.warn(`DynamicHeroIcon: SVG validation failed - ${svgResult.error}`);
                return <HIconsOutline.ExclamationTriangleIcon className={className} aria-hidden={ariaHidden} title={svgResult.error} />;
            }

            // Render sanitized SVG
            return (
                <div
                    className={className}
                    aria-hidden={ariaHidden}
                    dangerouslySetInnerHTML={{ __html: svgResult.sanitized || '' }}
                />
            );
        }

        // Handle HeroIcon names
        const IconComponent = allHeroIcons[iconName as HeroIconName];

        if (!IconComponent) {
            // Fallback for unknown icon names
            console.warn(`DynamicHeroIcon: Icon "${iconName}" not found. Rendering default.`);
            return <HIconsOutline.QuestionMarkCircleIcon className={className} aria-hidden={ariaHidden} />;
        }

        return <IconComponent className={className} aria-hidden={ariaHidden} />;
    }, [iconName, className, ariaHidden]);

    return <>{renderContent}</>;
};

export default DynamicHeroIcon; 
import React from 'react';

interface SkeletonLoaderProps {
    className?: string;
    width?: string;
    height?: string;
    rounded?: 'none' | 'sm' | 'md' | 'lg' | 'full';
}

export default function SkeletonLoader({
    className = '',
    width = 'w-full',
    height = 'h-4',
    rounded = 'md'
}: SkeletonLoaderProps) {
    const roundedClass = {
        none: 'rounded-none',
        sm: 'rounded-sm',
        md: 'rounded-md',
        lg: 'rounded-lg',
        full: 'rounded-full'
    }[rounded];

    return (
        <div
            className={`${width} ${height} ${roundedClass} bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 bg-[length:200px_100%] animate-shimmer ${className}`}
            style={{
                backgroundImage: 'linear-gradient(90deg, #f3f4f6 0%, #e5e7eb 50%, #f3f4f6 100%)',
                backgroundSize: '200px 100%',
                animation: 'shimmer 1.5s ease-in-out infinite'
            }}
        />
    );
} 
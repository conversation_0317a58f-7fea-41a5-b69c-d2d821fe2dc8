import React from 'react';
import SkeletonLoader from './SkeletonLoader';
import SkuCardSkeleton from './SkuCardSkeleton';

interface CategorySectionSkeletonProps {
    showSkuSkeletons?: boolean;
}

export default function CategorySectionSkeleton({ showSkuSkeletons = true }: CategorySectionSkeletonProps) {
    return (
        <section className="mb-8 relative">
            {/* Header Section */}
            <div className="flex justify-between items-center mb-3">
                {/* Category Title Skeleton */}
                <SkeletonLoader
                    width="w-32"
                    height="h-6"
                    rounded="md"
                />
                {/* View More Link Skeleton */}
                <SkeletonLoader
                    width="w-20"
                    height="h-5"
                    rounded="md"
                />
            </div>

            {/* Chevron Buttons Skeleton */}
            <div className="absolute left-0 top-1/2 -translate-y-1/2 z-10">
                <SkeletonLoader
                    width="w-10"
                    height="h-10"
                    rounded="full"
                />
            </div>
            <div className="absolute right-0 top-1/2 -translate-y-1/2 z-10">
                <SkeletonLoader
                    width="w-10"
                    height="h-10"
                    rounded="full"
                />
            </div>

            {/* Products Container */}
            {showSkuSkeletons ? (
                <div className="flex overflow-x-auto pb-4 gap-3 scrollbar-hide">
                    {/* Render 10 SKU card skeletons */}
                    {Array.from({ length: 10 }, (_, index) => (
                        <div key={index} className="w-32 md:w-36 flex-shrink-0">
                            <SkuCardSkeleton />
                        </div>
                    ))}
                </div>
            ) : (
                /* Simple loading text placeholder when SKUs are not being loaded yet */
                <div className="py-8">
                    <SkeletonLoader
                        width="w-32"
                        height="h-5"
                        rounded="md"
                        className="mx-auto"
                    />
                </div>
            )}
        </section>
    );
} 
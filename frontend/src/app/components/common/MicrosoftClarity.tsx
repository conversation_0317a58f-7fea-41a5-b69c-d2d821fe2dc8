'use client';

import { useEffect } from 'react';
import clarity from '@microsoft/clarity';
import { useAuth } from '../../context/AuthContext';
import { useSession } from '../../context/SessionContext';

const CLARITY_PROJECT_ID = 'rpnlp1y0lf';

// Utility functions for Clarity events
export const clarityUtils = {
    // Track user authentication
    trackUserLogin: (userId: string, userName: string) => {
        if (typeof window !== 'undefined' && clarity) {
            clarity.identify(userId, undefined, undefined, userName);
            clarity.event('user_login');
        }
    },

    // Track user logout
    trackUserLogout: () => {
        if (typeof window !== 'undefined' && clarity) {
            clarity.event('user_logout');
        }
    },

    // Track cart events
    trackAddToCart: (productId: number, productName: string, quantity: number) => {
        if (typeof window !== 'undefined' && clarity) {
            clarity.setTag('product_id', productId.toString());
            clarity.setTag('product_name', productName);
            clarity.setTag('quantity', quantity.toString());
            clarity.event('add_to_cart');
        }
    },

    // Track checkout events
    trackCheckoutStart: (cartValue: number, itemCount: number) => {
        if (typeof window !== 'undefined' && clarity) {
            clarity.setTag('cart_value', cartValue.toString());
            clarity.setTag('item_count', itemCount.toString());
            clarity.event('checkout_start');
        }
    },

    // Track order completion
    trackOrderComplete: (orderValue: number, itemCount: number) => {
        if (typeof window !== 'undefined' && clarity) {
            clarity.setTag('order_value', orderValue.toString());
            clarity.setTag('item_count', itemCount.toString());
            clarity.event('order_complete');
        }
    },

    // Track session events
    trackSessionStart: (sessionType: string) => {
        if (typeof window !== 'undefined' && clarity) {
            clarity.setTag('session_type', sessionType);
            clarity.event('session_start');
        }
    },

    // 🆕 SKU Service Performance Tracking
    // Track SKU data loading performance
    trackSkuLoadPerformance: (method: string, duration: number, cacheHit: boolean, itemCount?: number) => {
        if (typeof window !== 'undefined' && clarity) {
            clarity.setTag('sku_method', method);
            clarity.setTag('sku_duration', duration.toString());
            clarity.setTag('sku_cache_hit', cacheHit ? 'true' : 'false');
            if (itemCount !== undefined) {
                clarity.setTag('sku_item_count', itemCount.toString());
            }
            clarity.event('sku_load_performance');
        }
    },

    // Track progressive search metrics
    trackProgressiveSearch: (query: string, finalResults: number, totalDuration: number, categoriesLoaded: number) => {
        if (typeof window !== 'undefined' && clarity) {
            clarity.setTag('search_query', query);
            clarity.setTag('search_results', finalResults.toString());
            clarity.setTag('search_duration', totalDuration.toString());
            clarity.setTag('search_categories_loaded', categoriesLoaded.toString());
            clarity.event('progressive_search_complete');
        }
    },

    // Track cache efficiency
    trackCacheEfficiency: (operation: string, hitRate: number, totalCategories: number, loadedCategories: number) => {
        if (typeof window !== 'undefined' && clarity) {
            clarity.setTag('cache_operation', operation);
            clarity.setTag('cache_hit_rate', hitRate.toString());
            clarity.setTag('cache_total_categories', totalCategories.toString());
            clarity.setTag('cache_loaded_categories', loadedCategories.toString());
            clarity.event('cache_efficiency_metrics');
        }
    },

    // Track rural market optimizations
    trackRuralOptimization: (feature: string, improvement: string, metrics?: Record<string, string>) => {
        if (typeof window !== 'undefined' && clarity) {
            clarity.setTag('rural_feature', feature);
            clarity.setTag('rural_improvement', improvement);

            // Add custom metrics if provided
            if (metrics) {
                Object.entries(metrics).forEach(([key, value]) => {
                    clarity.setTag(`rural_${key}`, value);
                });
            }

            clarity.event('rural_optimization');
        }
    },

    // Track component migration success
    trackMigrationMetrics: (component: string, method: string, success: boolean, errorDetails?: string) => {
        if (typeof window !== 'undefined' && clarity) {
            clarity.setTag('migration_component', component);
            clarity.setTag('migration_method', method);
            clarity.setTag('migration_success', success ? 'true' : 'false');

            if (errorDetails) {
                clarity.setTag('migration_error', errorDetails);
            }

            clarity.event('migration_metrics');
        }
    },

    // Set custom tags
    setTag: (key: string, value: string | string[]) => {
        if (typeof window !== 'undefined' && clarity) {
            clarity.setTag(key, value);
        }
    },

    // Send custom events
    event: (eventName: string) => {
        if (typeof window !== 'undefined' && clarity) {
            clarity.event(eventName);
        }
    },

    // Upgrade session priority
    upgradeSession: (reason: string) => {
        if (typeof window !== 'undefined' && clarity) {
            clarity.upgrade(reason);
        }
    }
};

export default function MicrosoftClarity() {
    const { userDetails } = useAuth();
    const { activeSessionId, currentCustomerName } = useSession();

    useEffect(() => {
        // Only initialize Clarity on the client side
        if (typeof window !== 'undefined') {
            // Check if Clarity should be enabled (production or explicitly enabled)
            const isProduction = process.env.NODE_ENV === 'production';
            const isClarityEnabled = process.env.NEXT_PUBLIC_ENABLE_CLARITY === 'true';

            if (isProduction || isClarityEnabled) {
                try {
                    // Initialize Microsoft Clarity
                    clarity.init(CLARITY_PROJECT_ID);
                    console.log('Microsoft Clarity initialized successfully');
                } catch (error) {
                    console.error('Failed to initialize Microsoft Clarity:', error);
                }
            } else {
                console.log('Microsoft Clarity disabled in development mode');
            }
        }
    }, []);

    // Track user authentication changes
    useEffect(() => {
        if (userDetails && userDetails.id) {
            clarityUtils.trackUserLogin(userDetails.id.toString(), userDetails.name);
        }
    }, [userDetails]);

    // Track session changes
    useEffect(() => {
        if (activeSessionId && currentCustomerName) {
            clarityUtils.trackSessionStart(currentCustomerName);
        }
    }, [activeSessionId, currentCustomerName]);

    // This component doesn't render anything
    return null;
}
'use client';

import React from 'react';
import { useTranslation } from 'react-i18next';
import { useSession } from '../../context/SessionContext';
import { isCustomerFacingMode } from '../../../lib/utils';

interface WhatsAppFloatingButtonProps {
    phoneNumber?: string;
}

export default function WhatsAppFloatingButton({
    phoneNumber = process.env.NEXT_PUBLIC_WHATSAPP_NUMBER || '919876543210'
}: WhatsAppFloatingButtonProps) {
    const { t } = useTranslation();
    const {
        activeSessionId,
        sessions,
        cartDetailsQuery
    } = useSession();

    // Only show in customer-facing mode
    if (!isCustomerFacingMode()) {
        return null;
    }

    // Get active session details
    const activeSession = sessions.find(s => s.id === activeSessionId);
    const cartDetails = cartDetailsQuery.data;
    const cartItemCount = cartDetails?.totalCartQuantity || 0;
    const cartTotalPrice = cartDetails?.itemsTotalPrice || 0;

    // Generate WhatsApp message
    const generateWhatsAppMessage = (): string => {
        const greeting = t('whatsapp.greeting', 'Hello! I need assistance with my order.');
        const sessionInfo = activeSessionId ? `\nSession: ${activeSessionId}` : '';
        const cartInfo = cartItemCount > 0
            ? `\nCart: ${cartItemCount} ${cartItemCount === 1 ? 'item' : 'items'} (₹${cartTotalPrice})`
            : '';
        const customerInfo = activeSession?.customerName
            ? `\nCustomer: ${activeSession.customerName}`
            : '';

        return `${greeting}${sessionInfo}${cartInfo}${customerInfo}`;
    };

    // Handle WhatsApp button click
    const handleWhatsAppClick = () => {
        const message = generateWhatsAppMessage();
        const encodedMessage = encodeURIComponent(message);
        const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodedMessage}`;

        // Open WhatsApp in new tab/window
        window.open(whatsappUrl, '_blank', 'noopener,noreferrer');
    };

    return (
        <div className="fixed bottom-6 right-6 z-50 flex flex-col items-center">
            {/* Arc Message */}
            <div className="mb-2 relative">
                <div className="whatsapp-arc-text text-sm font-medium text-gray-700 bg-white px-3 py-1 rounded-full shadow-lg border border-gray-200 whitespace-nowrap">
                    {t('whatsapp.arcMessage', 'Need Help?')}
                </div>
                {/* Small arrow pointing down to button */}
                <div className="absolute left-1/2 transform -translate-x-1/2 top-full">
                    <div className="w-0 h-0 border-l-4 border-r-4 border-t-4 border-l-transparent border-r-transparent border-t-white"></div>
                </div>
            </div>

            {/* WhatsApp Button */}
            <button
                onClick={handleWhatsAppClick}
                className="whatsapp-floating-button bg-green-500 hover:bg-green-600 text-white rounded-full p-4 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 focus:outline-none focus:ring-4 focus:ring-green-300 focus:ring-opacity-50"
                aria-label={t('whatsapp.buttonLabel', 'Chat with us on WhatsApp')}
                title={t('whatsapp.buttonTitle', 'Click to chat with us on WhatsApp')}
            >
                {/* WhatsApp Icon SVG */}
                <svg
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                    className="w-6 h-6"
                >
                    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.893 3.106" />
                </svg>
            </button>

            <style jsx>{`
        .whatsapp-floating-button {
          animation: whatsapp-pulse 2s infinite;
        }

        @keyframes whatsapp-pulse {
          0% {
            box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7);
          }
          70% {
            box-shadow: 0 0 0 10px rgba(34, 197, 94, 0);
          }
          100% {
            box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
          }
        }

        .whatsapp-arc-text {
          animation: whatsapp-bounce 3s infinite;
        }

        @keyframes whatsapp-bounce {
          0%, 20%, 50%, 80%, 100% {
            transform: translateY(0);
          }
          40% {
            transform: translateY(-5px);
          }
          60% {
            transform: translateY(-3px);
          }
        }

        /* Mobile responsiveness */
        @media (max-width: 768px) {
          .whatsapp-floating-button {
            padding: 12px;
          }
          
          .whatsapp-arc-text {
            font-size: 12px;
            padding: 6px 12px;
          }

          /* Add more bottom spacing on mobile to avoid covering bottom navigation */
          .fixed.bottom-6.right-6 {
            bottom: 5rem; /* Increased from 1.5rem (bottom-6) to 5rem */
          }
        }
      `}</style>
        </div>
    );
} 
"use client";

import React, { useState, useEffect, useRef, useCallback, memo, useMemo } from 'react';
import Modal from 'react-modal';
import { useAuth, OTP_LENGTH } from '../context/AuthContext';
import { XMarkIcon, ShieldCheckIcon, ArrowLeftIcon } from '@heroicons/react/24/outline';
import { useTranslation } from 'react-i18next';

interface LoginModalProps {
    isOpen: boolean;
    onClose: () => void;
    dismissable?: boolean;
}

// Custom hook for OTP input management
const useOTPInput = (length: number = OTP_LENGTH) => {
    const [otp, setOtp] = useState(new Array(length).fill(""));
    const inputRefs = useRef<Array<HTMLInputElement | null>>([]);

    useEffect(() => {
        inputRefs.current = inputRefs.current.slice(0, length);
    }, [length]);

    const handleOtpChange = useCallback((e: React.ChangeEvent<HTMLInputElement>, index: number) => {
        const value = e.target.value.replace(/[^0-9]/g, '');
        if (value.length <= 1) {
            const newOtp = [...otp];
            newOtp[index] = value;
            setOtp(newOtp);

            // Focus next input if current one is filled
            if (value && index < length - 1 && inputRefs.current[index + 1]) {
                inputRefs.current[index + 1]?.focus();
            }
        }
    }, [otp, length]);

    const handleOtpKeyDown = useCallback((e: React.KeyboardEvent<HTMLInputElement>, index: number) => {
        if (e.key === 'Backspace' && !otp[index] && index > 0 && inputRefs.current[index - 1]) {
            inputRefs.current[index - 1]?.focus();
        }
    }, [otp]);

    const resetOtp = useCallback(() => {
        setOtp(new Array(length).fill(""));
    }, [length]);

    const focusFirstInput = useCallback(() => {
        setTimeout(() => {
            if (inputRefs.current[0]) {
                inputRefs.current[0]?.focus();
            }
        }, 100);
    }, []);

    const getOtpString = useCallback(() => otp.join(''), [otp]);

    return {
        otp,
        inputRefs,
        handleOtpChange,
        handleOtpKeyDown,
        resetOtp,
        focusFirstInput,
        getOtpString
    };
};

// Custom hook for modal flow management
const useModalFlow = (isOpen: boolean, dismissable: boolean, onClose: () => void) => {
    const [localOtpSent, setLocalOtpSent] = useState(false);
    const [mobileNumber, setMobileNumber] = useState("");
    const [formError, setFormError] = useState<string | null>(null);

    const { resetOtpState, otpSent, isUserLoggedIn } = useAuth();

    const resetAllStates = useCallback(() => {
        setMobileNumber("");
        setFormError(null);
        setLocalOtpSent(false);
        resetOtpState();
    }, [resetOtpState]);

    const handleModalClose = useCallback(() => {
        if (!dismissable) return;
        resetAllStates();
        onClose();
    }, [dismissable, resetAllStates, onClose]);

    const handleBackToMobile = useCallback(() => {
        setLocalOtpSent(false);
        setFormError(null);
        resetOtpState();

        setTimeout(() => {
            const mobileInput = document.getElementById('mobileNumber');
            if (mobileInput) {
                mobileInput.focus();
            }
        }, 100);
    }, [resetOtpState]);

    // Effect for successful login
    useEffect(() => {
        if (isUserLoggedIn && isOpen) {
            handleModalClose();
        }
    }, [isUserLoggedIn, isOpen, handleModalClose]);

    // Effect for OTP state sync
    useEffect(() => {
        if (otpSent && !localOtpSent) {
            setLocalOtpSent(true);
        }
        if (!otpSent && localOtpSent) {
            setLocalOtpSent(false);
        }
    }, [otpSent, localOtpSent]);

    // Effect for modal open/close
    useEffect(() => {
        if (isOpen) {
            resetAllStates();
        }
    }, [isOpen, resetAllStates]);

    return {
        localOtpSent,
        mobileNumber,
        setMobileNumber,
        formError,
        setFormError,
        handleModalClose,
        handleBackToMobile
    };
};

// Custom hook for form validation
const useFormValidation = () => {
    const { t } = useTranslation();

    const validateMobileNumber = useCallback((mobile: string) => {
        if (mobile.length !== 10) {
            return t('loginModal.error.invalidMobile');
        }
        return null;
    }, [t]);

    const validateOTP = useCallback((otpString: string) => {
        if (otpString.length !== OTP_LENGTH) {
            return t('loginModal.error.invalidOtpLength', { length: OTP_LENGTH });
        }
        return null;
    }, [t]);

    return { validateMobileNumber, validateOTP };
};

// Memoized Loading Spinner Component
const LoadingSpinner = memo(function LoadingSpinner() {
    return (
        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
    );
});

// Memoized Modal Header Component
const ModalHeader = memo(function ModalHeader({
    dismissable,
    localOtpSent,
    onClose,
    onBack
}: {
    dismissable: boolean;
    localOtpSent: boolean;
    onClose: () => void;
    onBack: () => void;
}) {
    const { t } = useTranslation();

    return (
        <>
            {dismissable && (
                <button
                    onClick={onClose}
                    className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors z-10"
                    aria-label={t('common.close')}
                >
                    <XMarkIcon className="h-6 w-6" />
                </button>
            )}

            {localOtpSent && (
                <button
                    onClick={onBack}
                    className="absolute top-4 left-4 text-gray-400 hover:text-gray-600 transition-colors z-10 flex items-center"
                    aria-label="Go back to mobile number"
                >
                    <ArrowLeftIcon className="h-6 w-6" />
                </button>
            )}

            <div className="text-center mb-6">
                <ShieldCheckIcon className="h-12 w-12 text-green-500 mx-auto mb-3" />
                <h2 className="text-2xl font-semibold text-gray-800">
                    {!dismissable ? t('auth.loginModal.adminTitle', 'Admin Login Required') : t('auth.loginModal.title')}
                </h2>
                <p className="text-sm text-gray-600 mt-1">
                    {!dismissable && !localOtpSent
                        ? t('auth.loginModal.adminSubtitle', 'Login as Bhuvanesh to access admin features. You can browse categories and products while logging in.')
                        : localOtpSent
                            ? t('auth.loginModal.subtitleOtp')
                            : t('auth.loginModal.subtitleMobile')
                    }
                </p>
            </div>
        </>
    );
});

// Memoized Error Display Component
const ErrorDisplay = memo(function ErrorDisplay({
    formError,
    authError
}: {
    formError: string | null;
    authError: string | null;
}) {
    if (!formError && !authError) return null;

    return (
        <div className="bg-red-100 border border-red-300 text-red-700 px-4 py-3 rounded-md mb-4 text-sm" role="alert">
            <p>{formError || authError}</p>
        </div>
    );
});

// Memoized Mobile Number Form Component
const MobileNumberForm = memo(function MobileNumberForm({
    mobileNumber,
    onMobileChange,
    onSubmit,
    isSending,
    formError
}: {
    mobileNumber: string;
    onMobileChange: (value: string) => void;
    onSubmit: () => void;
    isSending: boolean;
    formError: string | null;
}) {
    const { t } = useTranslation();

    const handleMobileNumberChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value.replace(/[^0-9]/g, '');
        if (value.length <= 10) {
            onMobileChange(value);
        }
    }, [onMobileChange]);

    const handleSubmit = useCallback((e: React.FormEvent) => {
        e.preventDefault();
        onSubmit();
    }, [onSubmit]);

    return (
        <form onSubmit={handleSubmit}>
            <div className="mb-5 relative">
                <label htmlFor="mobileNumber" className="block text-sm font-medium text-gray-700 mb-1">
                    {t('auth.loginModal.label.mobileNumber')}
                </label>
                <div className="flex items-center border border-gray-300 rounded-md focus-within:ring-2 focus-within:ring-green-500 focus-within:border-green-500 transition-all">
                    <span className="inline-flex items-center px-3 bg-gray-50 text-gray-500 sm:text-sm h-11 rounded-l-md border-r border-gray-300">
                        +91
                    </span>
                    <input
                        type="tel"
                        id="mobileNumber"
                        name="mobileNumber"
                        value={mobileNumber}
                        onChange={handleMobileNumberChange}
                        placeholder={t('auth.loginModal.placeholder.mobileNumber')}
                        className="block w-full sm:text-sm border-0 rounded-r-md p-3 h-11 focus:ring-0 focus:outline-none bg-white"
                        maxLength={10}
                        disabled={isSending}
                        autoFocus
                    />
                </div>
            </div>
            <button
                type="submit"
                disabled={isSending || mobileNumber.length !== 10}
                className="w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-4 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-60 disabled:cursor-not-allowed transition-colors duration-150 flex items-center justify-center"
            >
                {isSending ? (
                    <>
                        <LoadingSpinner />
                        {t('auth.loginModal.button.sendingOtp')}
                    </>
                ) : t('auth.loginModal.button.sendOtp')}
            </button>
        </form>
    );
});

// Memoized OTP Form Component
const OTPForm = memo(function OTPForm({
    otp,
    inputRefs,
    mobileNumber,
    onOtpChange,
    onOtpKeyDown,
    onSubmit,
    onResend,
    isVerifying,
    isSending
}: {
    otp: string[];
    inputRefs: React.MutableRefObject<Array<HTMLInputElement | null>>;
    mobileNumber: string;
    onOtpChange: (e: React.ChangeEvent<HTMLInputElement>, index: number) => void;
    onOtpKeyDown: (e: React.KeyboardEvent<HTMLInputElement>, index: number) => void;
    onSubmit: () => void;
    onResend: () => void;
    isVerifying: boolean;
    isSending: boolean;
}) {
    const { t } = useTranslation();

    const handleSubmit = useCallback((e: React.FormEvent) => {
        e.preventDefault();
        onSubmit();
    }, [onSubmit]);

    return (
        <form onSubmit={handleSubmit}>
            <p className="text-sm text-gray-600 mb-3 text-center">
                {t('auth.loginModal.otpSentTo', { mobileNumber })}
                <button
                    type="button"
                    onClick={onResend}
                    disabled={isSending}
                    className="ml-1 text-green-600 hover:text-green-700 text-sm font-medium disabled:opacity-70"
                >
                    {isSending ? t('auth.loginModal.button.resending') : t('auth.loginModal.button.resend')}
                </button>
            </p>
            <div className="mb-5 flex justify-center space-x-2 sm:space-x-3">
                {otp.map((digit, index) => (
                    <input
                        key={index}
                        ref={el => { inputRefs.current[index] = el; }}
                        type="tel"
                        maxLength={1}
                        value={digit}
                        onChange={(e) => onOtpChange(e, index)}
                        onKeyDown={(e) => onOtpKeyDown(e, index)}
                        className="w-12 h-12 sm:w-14 sm:h-14 text-center text-xl sm:text-2xl font-medium border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors"
                        disabled={isVerifying}
                    />
                ))}
            </div>
            <button
                type="submit"
                disabled={isVerifying || otp.join('').length !== OTP_LENGTH}
                className="w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-4 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-60 disabled:cursor-not-allowed transition-colors duration-150 flex items-center justify-center"
            >
                {isVerifying ? (
                    <>
                        <LoadingSpinner />
                        {t('auth.loginModal.button.verifyingOtp')}
                    </>
                ) : t('auth.loginModal.button.verifyOtp')}
            </button>
            <p className="text-xs text-gray-500 mt-3 text-center">
                {t('auth.loginModal.otpNotReceived')}
            </p>
        </form>
    );
});

// Main Login Modal Component
const LoginModal = memo(function LoginModal({
    isOpen,
    onClose,
    dismissable = true
}: LoginModalProps) {
    const {
        sendOtp,
        verifyOtp,
        isSendingOtp,
        isVerifyingOtp,
        error: authError
    } = useAuth();

    // Custom hooks
    const {
        localOtpSent,
        mobileNumber,
        setMobileNumber,
        formError,
        setFormError,
        handleModalClose,
        handleBackToMobile
    } = useModalFlow(isOpen, dismissable, onClose);

    const {
        otp,
        inputRefs,
        handleOtpChange,
        handleOtpKeyDown,
        resetOtp,
        focusFirstInput,
        getOtpString
    } = useOTPInput();

    const { validateMobileNumber, validateOTP } = useFormValidation();

    // Memoized modal styles
    const customModalStyles = useMemo(() => ({
        overlay: {
            backgroundColor: 'rgba(0, 0, 0, 0.6)',
            backdropFilter: 'blur(4px)',
            zIndex: 100,
        },
        content: {
            top: '50%',
            left: '50%',
            right: 'auto',
            bottom: 'auto',
            marginRight: '-50%',
            transform: 'translate(-50%, -50%)',
            border: 'none',
            background: 'transparent',
            padding: '0',
            borderRadius: '0',
            width: '100%',
            maxWidth: '28rem',
            overflow: 'visible'
        },
    }), []);

    // Event handlers
    const handleMobileChange = useCallback((value: string) => {
        setMobileNumber(value);
        setFormError(null);
    }, [setMobileNumber, setFormError]);

    const handleSendOtp = useCallback(async () => {
        const error = validateMobileNumber(mobileNumber);
        if (error) {
            setFormError(error);
            return;
        }
        setFormError(null);
        const success = await sendOtp(mobileNumber);
        if (success) {
            focusFirstInput();
        }
    }, [mobileNumber, validateMobileNumber, setFormError, sendOtp, focusFirstInput]);

    const handleVerifyOtp = useCallback(async () => {
        const otpString = getOtpString();
        const error = validateOTP(otpString);
        if (error) {
            setFormError(error);
            return;
        }
        setFormError(null);
        await verifyOtp(mobileNumber, otpString);
    }, [getOtpString, validateOTP, setFormError, verifyOtp, mobileNumber]);

    const handleResendOtp = useCallback(async () => {
        setFormError(null);
        await sendOtp(mobileNumber);
    }, [setFormError, sendOtp, mobileNumber]);

    const handleOtpChangeWithReset = useCallback((e: React.ChangeEvent<HTMLInputElement>, index: number) => {
        setFormError(null);
        handleOtpChange(e, index);
    }, [setFormError, handleOtpChange]);

    const handleBackWithReset = useCallback(() => {
        resetOtp();
        handleBackToMobile();
    }, [resetOtp, handleBackToMobile]);

    if (!isOpen) return null;

    return (
        <Modal
            isOpen={isOpen}
            onRequestClose={handleModalClose}
            style={customModalStyles}
            contentLabel="Login Modal"
            shouldCloseOnOverlayClick={dismissable}
            shouldCloseOnEsc={dismissable}
        >
            <div className="bg-white p-6 sm:p-8 rounded-lg shadow-xl w-full relative">
                <ModalHeader
                    dismissable={dismissable}
                    localOtpSent={localOtpSent}
                    onClose={handleModalClose}
                    onBack={handleBackWithReset}
                />

                <ErrorDisplay formError={formError} authError={authError} />

                {!localOtpSent ? (
                    <MobileNumberForm
                        mobileNumber={mobileNumber}
                        onMobileChange={handleMobileChange}
                        onSubmit={handleSendOtp}
                        isSending={isSendingOtp}
                        formError={formError}
                    />
                ) : (
                    <OTPForm
                        otp={otp}
                        inputRefs={inputRefs}
                        mobileNumber={mobileNumber}
                        onOtpChange={handleOtpChangeWithReset}
                        onOtpKeyDown={handleOtpKeyDown}
                        onSubmit={handleVerifyOtp}
                        onResend={handleResendOtp}
                        isVerifying={isVerifyingOtp}
                        isSending={isSendingOtp}
                    />
                )}
            </div>
        </Modal>
    );
});

export default LoginModal;
import { useQuery } from '@tanstack/react-query';
import Link from 'next/link';
import { useEffect, useRef, useState, useCallback, memo, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { SKU } from '../types/sku';
import { getSkusByCategory } from '../services/skuService';
import SkuCard from './SkuCard';
import { useLazyLoad } from '../hooks/useLazyLoad';
import CategorySectionSkeleton from './common/CategorySectionSkeleton';

// Memoized Icon Components
const ChevronLeftIcon = memo(function ChevronLeftIcon() {
  return <span className="text-2xl leading-none">&lsaquo;</span>;
});

const ChevronRightIcon = memo(function ChevronRightIcon() {
  return <span className="text-2xl leading-none">&rsaquo;</span>;
});

interface CategorySectionProps {
  title: string;
  categoryId: string;
  isFirstCategory?: boolean;
  maxSkusToShow?: number;
  backgroundColor?: string;
}

// Custom hook for scroll functionality
const useScrollableContainer = (displaySkus: SKU[]) => {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);

  const checkScrollability = useCallback(() => {
    const container = scrollContainerRef.current;
    if (container && displaySkus && displaySkus.length > 0) {
      const { scrollLeft, scrollWidth, clientWidth } = container;
      setCanScrollLeft(scrollLeft > 5);
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 5);
    } else if (container) {
      setCanScrollLeft(false);
      setCanScrollRight(false);
    }
  }, [displaySkus]);

  const scrollManually = useCallback((direction: 'left' | 'right') => {
    const container = scrollContainerRef.current;
    if (container) {
      const scrollAmount = container.clientWidth * 0.8;
      const newScrollLeft = direction === 'left'
        ? container.scrollLeft - scrollAmount
        : container.scrollLeft + scrollAmount;
      container.scrollTo({
        left: newScrollLeft,
        behavior: 'smooth'
      });
    }
  }, []);

  const handleScroll = useCallback(() => {
    checkScrollability();
  }, [checkScrollability]);

  useEffect(() => {
    checkScrollability();
  }, [displaySkus, checkScrollability]);

  return {
    scrollContainerRef,
    canScrollLeft,
    canScrollRight,
    scrollManually,
    handleScroll
  };
};

// Custom hook for category data fetching
const useCategoryData = (categoryId: string, isFirstCategory: boolean, maxSkusToShow: number) => {
  const [shouldFetchSkus, setShouldFetchSkus] = useState(isFirstCategory);

  const handleIntersection = useCallback(() => {
    setShouldFetchSkus(true);
  }, []);

  const { targetRef } = useLazyLoad(handleIntersection, {
    enabled: !isFirstCategory,
    rootMargin: '100px'
  });

  const {
    data: skus,
    isLoading: isLoadingSkus,
    isError: isErrorSkus,
    error: SkusError,
  } = useQuery<SKU[], Error>({
    queryKey: ['products', categoryId],
    queryFn: async () => {
      const skusArray = await getSkusByCategory(categoryId); // Customer component - excludes inactive SKUs by default
      return skusArray;
    },
    enabled: shouldFetchSkus,
  });

  const displaySkus = useMemo(() => {
    return skus ? skus.slice(0, maxSkusToShow) : [];
  }, [skus, maxSkusToShow]);

  return {
    displaySkus,
    isLoadingSkus,
    isErrorSkus,
    SkusError,
    shouldFetchSkus,
    targetRef
  };
};

// Custom hook for styling
const useCategoryStyles = (backgroundColor?: string) => {
  const containerStyles = useMemo(() => ({
    backgroundColor: backgroundColor || 'transparent',
    padding: '1rem 1rem',
    borderRadius: '0rem',
    marginBottom: '1rem'
  }), [backgroundColor]);

  const containerClasses = useMemo(() => {
    const baseClasses = 'mb-8 relative';
    return backgroundColor ? `${baseClasses} border border-gray-100` : baseClasses;
  }, [backgroundColor]);

  return { containerStyles, containerClasses };
};

// Memoized Category Header Component
const CategoryHeader = memo(function CategoryHeader({
  title,
  categoryId
}: {
  title: string;
  categoryId: string;
}) {
  const { t } = useTranslation();

  return (
    <div className="flex justify-between items-center mb-3">
      <h2 className="text-lg font-semibold text-gray-900">{title}</h2>
      <Link href={`/categories/${categoryId}`} className="text-sm text-blue-600 hover:text-blue-800 font-medium">
        {t('categories.section.viewMore')} &rarr;
      </Link>
    </div>
  );
});

// Memoized Scroll Buttons Component
const ScrollButtons = memo(function ScrollButtons({
  canScrollLeft,
  canScrollRight,
  onScroll,
  hasItems
}: {
  canScrollLeft: boolean;
  canScrollRight: boolean;
  onScroll: (direction: 'left' | 'right') => void;
  hasItems: boolean;
}) {
  const { t } = useTranslation();

  if (!hasItems) return null;

  return (
    <>
      <button
        onClick={() => onScroll('left')}
        disabled={!canScrollLeft}
        className={`absolute left-0 top-1/2 -translate-y-1/2 z-10 w-10 h-10 flex items-center justify-center bg-white/80 hover:bg-white rounded-full shadow-md transition-opacity duration-300 ${!canScrollLeft ? 'opacity-30 cursor-not-allowed' : 'opacity-100'}`}
        aria-label={t('categories.section.accessibility.scrollLeft')}
      >
        <ChevronLeftIcon />
      </button>

      <button
        onClick={() => onScroll('right')}
        disabled={!canScrollRight}
        className={`absolute right-0 top-1/2 -translate-y-1/2 z-10 w-10 h-10 flex items-center justify-center bg-white/80 hover:bg-white rounded-full shadow-md transition-opacity duration-300 ${!canScrollRight ? 'opacity-30 cursor-not-allowed' : 'opacity-100'}`}
        aria-label={t('categories.section.accessibility.scrollRight')}
      >
        <ChevronRightIcon />
      </button>
    </>
  );
});

// Memoized SKU List Component
const SkuList = memo(function SkuList({
  displaySkus,
  scrollContainerRef,
  onScroll,
  categoryId
}: {
  displaySkus: SKU[];
  scrollContainerRef: React.RefObject<HTMLDivElement | null>;
  onScroll: () => void;
  categoryId: string;
}) {
  return (
    <div
      ref={scrollContainerRef}
      onScroll={onScroll}
      className="flex overflow-x-auto pb-4 gap-3 scrollbar-hide"
    >
      {displaySkus.map(sku => (
        <div key={`${categoryId}-${sku.skuId}`} className="w-32 md:w-36 flex-shrink-0">
          <SkuCard sku={sku} />
        </div>
      ))}
    </div>
  );
});

// Memoized Loading State Component
const LoadingState = memo(function LoadingState({
  targetRef,
  isFirstCategory,
  containerClasses,
  containerStyles
}: {
  targetRef?: React.RefObject<HTMLElement | null>;
  isFirstCategory: boolean;
  containerClasses: string;
  containerStyles: React.CSSProperties;
}) {
  return (
    <section ref={isFirstCategory ? undefined : targetRef} className={containerClasses} style={containerStyles}>
      <CategorySectionSkeleton showSkuSkeletons={true} />
    </section>
  );
});

// Memoized Error State Component
const ErrorState = memo(function ErrorState({
  targetRef,
  isFirstCategory,
  containerClasses,
  containerStyles,
  title,
  categoryId,
  error
}: {
  targetRef?: React.RefObject<HTMLElement | null>;
  isFirstCategory: boolean;
  containerClasses: string;
  containerStyles: React.CSSProperties;
  title: string;
  categoryId: string;
  error?: Error | null;
}) {
  const { t } = useTranslation();

  return (
    <section ref={isFirstCategory ? undefined : targetRef} className={containerClasses} style={containerStyles}>
      <CategoryHeader title={title} categoryId={categoryId} />
      <p className="text-red-600">
        {t('categories.section.error.loadingProducts')} {error?.message || t('common.unknownError')}
      </p>
    </section>
  );
});

// Main Category Section Component
const CategorySection = memo(function CategorySection({
  title,
  categoryId,
  isFirstCategory = false,
  maxSkusToShow = 20,
  backgroundColor
}: CategorySectionProps) {
  // Custom hooks
  const { containerStyles, containerClasses } = useCategoryStyles(backgroundColor);
  const {
    displaySkus,
    isLoadingSkus,
    isErrorSkus,
    SkusError,
    shouldFetchSkus,
    targetRef
  } = useCategoryData(categoryId, isFirstCategory, maxSkusToShow);

  const {
    scrollContainerRef,
    canScrollLeft,
    canScrollRight,
    scrollManually,
    handleScroll
  } = useScrollableContainer(displaySkus);

  // Show skeleton while waiting for lazy load trigger (non-first categories)
  if (!shouldFetchSkus && !isFirstCategory) {
    return (
      <LoadingState
        targetRef={targetRef}
        isFirstCategory={isFirstCategory}
        containerClasses={containerClasses}
        containerStyles={containerStyles}
      />
    );
  }

  // Show loading skeleton while fetching SKUs
  if (isLoadingSkus) {
    return (
      <LoadingState
        targetRef={isFirstCategory ? undefined : targetRef}
        isFirstCategory={isFirstCategory}
        containerClasses={containerClasses}
        containerStyles={containerStyles}
      />
    );
  }

  // Show error state
  if (isErrorSkus) {
    return (
      <ErrorState
        targetRef={isFirstCategory ? undefined : targetRef}
        isFirstCategory={isFirstCategory}
        containerClasses={containerClasses}
        containerStyles={containerStyles}
        title={title}
        categoryId={categoryId}
        error={SkusError}
      />
    );
  }

  // Early return if no products - render nothing
  if (!displaySkus || displaySkus.length === 0) {
    return null;
  }

  // Main render - category with products
  return (
    <section ref={isFirstCategory ? undefined : targetRef} className={containerClasses} style={containerStyles}>
      <CategoryHeader title={title} categoryId={categoryId} />

      <ScrollButtons
        canScrollLeft={canScrollLeft}
        canScrollRight={canScrollRight}
        onScroll={scrollManually}
        hasItems={displaySkus.length > 0}
      />

      <SkuList
        displaySkus={displaySkus}
        scrollContainerRef={scrollContainerRef}
        onScroll={handleScroll}
        categoryId={categoryId}
      />
    </section>
  );
});

export default CategorySection;
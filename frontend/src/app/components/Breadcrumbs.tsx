'use client';

import React, { memo } from 'react';
import Link from 'next/link';

interface BreadcrumbItem {
    name: string;
    href: string;
}

interface BreadcrumbsProps {
    items: BreadcrumbItem[];
}

// Memoized chevron component to prevent recreation on every render
const ChevronIcon = memo(function ChevronIcon() {
    return (
        <svg
            className="w-3 h-3 text-gray-400 mx-2"
            fill="currentColor"
            viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg"
            aria-hidden="true"
        >
            <path
                fillRule="evenodd"
                d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                clipRule="evenodd"
            />
        </svg>
    );
});

// Memoized breadcrumb item component
const BreadcrumbItem = memo(function BreadcrumbItem({
    item,
    index,
    isLast
}: {
    item: BreadcrumbItem;
    index: number;
    isLast: boolean;
}) {
    return (
        <li className="flex items-center">
            {index > 0 && <ChevronIcon />}
            {isLast ? (
                <span className="font-medium text-gray-800" aria-current="page">
                    {item.name}
                </span>
            ) : (
                <Link href={item.href} className="hover:text-indigo-600 hover:underline">
                    {item.name}
                </Link>
            )}
        </li>
    );
});

const Breadcrumbs = memo(function Breadcrumbs({ items = [] }: BreadcrumbsProps) {
    // Early return for empty or invalid items
    if (!items || items.length === 0) {
        return null;
    }

    return (
        <nav aria-label="Breadcrumb" className="mb-4 text-sm text-gray-600">
            <ol className="flex items-center space-x-2">
                {items.map((item, index) => (
                    <BreadcrumbItem
                        key={`breadcrumb-${index}-${item.href}`}
                        item={item}
                        index={index}
                        isLast={index === items.length - 1}
                    />
                ))}
            </ol>
        </nav>
    );
});

export default Breadcrumbs;
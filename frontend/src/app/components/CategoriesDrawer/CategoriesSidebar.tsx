'use client';

import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';
import CategoriesList from '@/app/components/common/CategoriesList';
import { useCurrentCategory } from '@/app/hooks/useCurrentCategory';

interface CategoriesSidebarProps {
    onSelectCategory: (categoryId: string) => void;
    onCollapseChange?: (isCollapsed: boolean) => void;
}

export default function CategoriesSidebar({ onSelectCategory, onCollapseChange }: CategoriesSidebarProps) {
    const { t } = useTranslation();
    const currentCategoryId = useCurrentCategory();
    const [isCollapsed, setIsCollapsed] = useState(false);

    const handleToggleCollapse = () => {
        const newCollapsedState = !isCollapsed;
        setIsCollapsed(newCollapsedState);
        onCollapseChange?.(newCollapsedState);
    };

    return (
        <aside className={`hidden md:flex md:flex-col bg-white border-r border-gray-200 flex-shrink-0 fixed left-0 top-0 h-screen z-30 transition-all duration-300 ease-in-out ${isCollapsed ? 'w-16' : 'w-64'
            }`}>
            {/* Sidebar Header */}
            <div className="px-4 pb-2 pt-1 border-b border-gray-200 bg-white flex-shrink-0 flex items-center justify-between" style={{ marginTop: '76px' }}>
                {!isCollapsed && (
                    <h2 className="text-lg font-semibold text-gray-800">
                        {t('categoriesDrawer.title', 'Categories')}
                    </h2>
                )}

                {/* Collapse Toggle Button */}
                <button
                    onClick={handleToggleCollapse}
                    className={`p-1.5 rounded-md hover:bg-gray-100 transition-colors ${isCollapsed ? 'mx-auto' : 'ml-auto'
                        }`}
                    title={isCollapsed ? t('categoriesDrawer.expand', 'Expand sidebar') : t('categoriesDrawer.collapse', 'Collapse sidebar')}
                >
                    {isCollapsed ? (
                        <ChevronRightIcon className="h-5 w-5 text-gray-600" />
                    ) : (
                        <ChevronLeftIcon className="h-5 w-5 text-gray-600" />
                    )}
                </button>
            </div>

            {/* Categories List */}
            <nav className={`flex-1 overflow-y-auto ${isCollapsed ? 'px-2 py-4' : 'p-4'}`}>
                <CategoriesList
                    onSelectCategory={onSelectCategory}
                    activeCategoryId={currentCategoryId || undefined}
                    isCollapsed={isCollapsed}
                />
            </nav>
        </aside>
    );
} 
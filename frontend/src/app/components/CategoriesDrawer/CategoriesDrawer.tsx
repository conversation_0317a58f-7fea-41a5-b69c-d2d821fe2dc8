'use client';

import React from 'react';
import { useTranslation } from 'react-i18next';
import { XMarkIcon } from '@heroicons/react/24/outline';
import CategoriesList from '@/app/components/common/CategoriesList';
import { useCurrentCategory } from '@/app/hooks/useCurrentCategory';

interface CategoriesDrawerProps {
    isOpen: boolean;
    onClose: () => void;
    onSelectCategory: (categoryId: string) => void;
    onPrefetchCategory?: (categoryId: string) => void;
}

export default function CategoriesDrawer({ isOpen, onClose, onSelectCategory, onPrefetchCategory }: CategoriesDrawerProps) {
    const { t } = useTranslation();
    const currentCategoryId = useCurrentCategory();

    const handleCategorySelect = (categoryId: string) => {
        onSelectCategory(categoryId);
        onClose();
    };

    return (
        <>
            {/* Overlay for mobile only when drawer is open */}
            <div
                className={`md:hidden fixed inset-0 bg-black/50 z-40 transition-opacity duration-300 ease-in-out ${isOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'}`}
                onClick={onClose}
                aria-hidden="true"
            />

            {/* Drawer Panel - Mobile only */}
            <div
                className={`md:hidden fixed top-0 left-0 h-full bg-white shadow-xl z-50 w-full max-w-xs sm:max-w-sm 
                           transform transition-transform duration-300 ease-in-out
                           ${isOpen ? 'translate-x-0' : '-translate-x-full'}
                           flex flex-col`}
                role="dialog"
                aria-modal="true"
                aria-labelledby="categories-drawer-title"
            >
                {/* Drawer Header */}
                <div className="flex items-center justify-between p-4 border-b border-gray-200 sticky top-0 bg-white z-10">
                    <h2 id="categories-drawer-title" className="text-lg font-semibold text-gray-800">
                        {t('categoriesDrawer.title', 'Categories')}
                    </h2>
                    <button
                        onClick={onClose}
                        className="text-gray-500 hover:text-gray-700 p-1 rounded-full hover:bg-gray-100"
                        aria-label={t('common.button.close', 'Close')}
                    >
                        <XMarkIcon className="h-6 w-6" />
                    </button>
                </div>

                {/* Category List */}
                <nav className="flex-grow p-4 space-y-2 overflow-y-auto">
                    <CategoriesList
                        onSelectCategory={handleCategorySelect}
                        activeCategoryId={currentCategoryId || undefined}
                        onPrefetchCategory={onPrefetchCategory}
                    />
                </nav>
            </div>
        </>
    );
} 
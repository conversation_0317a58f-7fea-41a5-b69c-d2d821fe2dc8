'use client';

import React, { useState, useRef, useEffect, forwardRef, useCallback, useImperativeHandle } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslation } from 'react-i18next';
import { searchSkusProgressive } from '../services/skuService';
import { searchCategories } from '../services/categoryService';
import { clarityUtils } from '@/app/components/common/MicrosoftClarity';
import type { SKU } from '@/app/types/sku';
import type { Category } from '@/app/types/category';
import Image from 'next/image';
import DynamicHeroIcon from './common/DynamicHeroIcon';
import i18n from '@/i18n';

interface SearchResult {
  type: 'category' | 'sku';
  id: string;
  name: string;
  imageUrl?: string;
  price?: {
    selling: number;
    mrp: number;
  };
  data: any;
}

interface SearchBarProps { }

const SearchBar = forwardRef<HTMLInputElement, SearchBarProps>((props, ref) => {
  const { t } = useTranslation();
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showResultsDropdown, setShowResultsDropdown] = useState(false);
  const currentSearchRef = useRef<string>('');
  const searchRef = useRef<HTMLDivElement>(null);

  const handleSearch = useCallback(async (searchQuery: string) => {
    const trimmedQuery = searchQuery.trim();
    currentSearchRef.current = trimmedQuery;

    if (!trimmedQuery) {
      setSearchResults([]);
      setIsLoading(false);
      setShowResultsDropdown(false);
      return;
    }

    setIsLoading(true);
    setShowResultsDropdown(true);
    const searchStartTime = Date.now();

    try {
      // Track search initiation
      clarityUtils.setTag('search_query', trimmedQuery);
      clarityUtils.setTag('search_type', 'progressive');

      // Get categories immediately (fast)
      const categories = await searchCategories(trimmedQuery, { maxResults: 5 });
      const categoryResults: SearchResult[] = categories.map(cat => ({
        type: 'category' as const,
        id: cat.id.toString(),
        name: cat.name.en || 'Unnamed Category',
        imageUrl: undefined,
        data: cat
      }));

      // Start progressive SKU search but don't show progress messages to user
      let finalSkuResults: SearchResult[] = [];
      let searchComplete = false;

      await searchSkusProgressive(
        trimmedQuery,
        (progressData) => {
          // Only update if this is still the current search
          if (currentSearchRef.current === trimmedQuery) {
            const skuResults: SearchResult[] = progressData.skus.map(sku => ({
              type: 'sku' as const,
              id: sku.skuId.toString(),
              name: sku.name[i18n.language] || sku.name.en || 'Unnamed Product',
              imageUrl: sku.imageUrl,
              price: {
                selling: sku.sellingPrice || 0,
                mrp: sku.mrp || 0
              },
              data: sku
            }));

            finalSkuResults = skuResults;
            const combinedResults = [...skuResults, ...categoryResults];
            setSearchResults(combinedResults);

            if (progressData.isComplete) {
              searchComplete = true;
              setIsLoading(false);
              const searchDuration = Date.now() - searchStartTime;

              // Track completed progressive search
              clarityUtils.trackProgressiveSearch(
                trimmedQuery,
                finalSkuResults.length,
                searchDuration,
                progressData.loadedCategories
              );

              // Track search completion
              clarityUtils.setTag('search_results_count', combinedResults.length.toString());
              clarityUtils.setTag('search_sku_count', finalSkuResults.length.toString());
              clarityUtils.setTag('search_category_count', categoryResults.length.toString());
              clarityUtils.setTag('search_duration', searchDuration.toString());
              clarityUtils.event('search_completed');
            }
          }
        },
        { maxResults: 20, language: i18n.language },

      ).catch(error => {
        console.error('Search error:', error);
        if (currentSearchRef.current === trimmedQuery) {
          setIsLoading(false);
        }
      });

      // If search completed immediately (all cached)
      if (searchComplete) {
        const finalCombinedResults = [...finalSkuResults, ...categoryResults];
        setSearchResults(finalCombinedResults);
        setIsLoading(false);
      }

    } catch (error) {
      const searchDuration = Date.now() - searchStartTime;

      // Track search error
      clarityUtils.trackMigrationMetrics('SearchBar', 'progressive_search', false, error?.toString());
      clarityUtils.setTag('search_error_duration', searchDuration.toString());
      clarityUtils.event('search_error');

      console.error('SearchBar: Search error:', error);
      if (currentSearchRef.current === trimmedQuery) {
        setIsLoading(false);
      }
    }
  }, []);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const term = e.target.value;
    setSearchTerm(term);

    // Debounce search
    setTimeout(() => {
      if (currentSearchRef.current === term) {
        handleSearch(term);
      }
    }, 300);

    currentSearchRef.current = term;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchTerm.trim()) {
      handleSearch(searchTerm);
    }
  };

  const handleResultClick = (result: SearchResult) => {
    if (result.type === 'category') {
      router.push(`/categories/${result.id}`);
    } else if (result.type === 'sku') {
      // Generate product slug from name
      router.push(`/product/${result.id}`);
    }
    setShowResultsDropdown(false);
    setSearchTerm('');
  };

  // Click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowResultsDropdown(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div ref={searchRef} className="relative flex-1 max-w-2xl mx-4">
      <form onSubmit={handleSubmit}>
        <div className="relative">
          <input
            ref={ref}
            type="text"
            className="w-full py-2 pl-10 pr-4 text-gray-700 bg-gray-50 border border-gray-300 rounded-lg focus:outline-none focus:border-gray-400 focus:ring-1 focus:ring-gray-400"
            placeholder={t('search.placeholder', 'Search products...')}
            value={searchTerm}
            onChange={handleSearchChange}
            onFocus={() => searchTerm.trim().length > 0 && setShowResultsDropdown(true)}
          />
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>
        </div>
      </form>

      {showResultsDropdown && (
        <div className="absolute z-20 w-full mt-1 bg-white rounded-md shadow-lg max-h-96 overflow-y-auto">
          {isLoading && (
            <p className="p-4 text-gray-500 text-center">{t('search.loading', 'Searching...')}</p>
          )}
          {!isLoading && searchResults.length === 0 && searchTerm.trim() && (
            <p className="p-4 text-gray-500 text-center">{t('search.noResults', 'No results found')}</p>
          )}
          {searchResults.length > 0 && (
            <ul>
              {searchResults.map((result: SearchResult) => (
                <li
                  key={`${result.type}-${result.id}`}
                  className="border-b border-gray-100 last:border-none hover:bg-gray-50 cursor-pointer"
                  onClick={() => handleResultClick(result)}
                >
                  <div className="flex items-center p-3">
                    <div className="w-10 h-10 mr-3 bg-gray-100 rounded-md overflow-hidden flex-shrink-0 relative flex items-center justify-center">
                      {result.type === 'category' ? (
                        <DynamicHeroIcon
                          iconName={result.data?.icon || 'FolderIcon'}
                          className="w-6 h-6 text-gray-600"
                        />
                      ) : (
                        result.imageUrl && (
                          <Image
                            src={result.imageUrl}
                            alt={result.name}
                            fill
                            sizes="(max-width: 40px) 100vw, 40px"
                            className="object-cover"
                          />
                        )
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <p className="text-sm font-medium text-gray-900 truncate">{result.name}</p>
                      </div>
                    </div>
                    {result.type === 'sku' && result.price && (
                      <div className="text-right flex-shrink-0 ml-2">
                        <p className="text-sm font-medium text-gray-800">₹{result.price.selling}</p>
                        {result.price.mrp > result.price.selling && (
                          <p className="text-xs text-gray-500 line-through">₹{result.price.mrp}</p>
                        )}
                      </div>
                    )}
                  </div>
                </li>
              ))}
            </ul>
          )}
        </div>
      )}
    </div>
  );
});

SearchBar.displayName = 'SearchBar';

export default SearchBar;

'use client';

import { LocalizedName } from '@/app/types/common';
import React, { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSession } from '../../context/SessionContext'; // Corrected path
import { CartItem } from '../../types/session'; // Import CartItem for typing
import { SKU, SKUVariant } from '../../types/sku'; // Corrected path
import { useSessionCheck } from '../../hooks/useSessionCheck';
import { SessionModal } from '../Header/SessionManager';

interface ProductInformationProps {
    sku: SKU;
}

const ProductInformation: React.FC<ProductInformationProps> = ({ sku: sku }) => {
    const { t, i18n } = useTranslation();
    const currentLanguage = useMemo(() => i18n.language as keyof LocalizedName, [i18n.language]);
    const { skuId, name, sellingPrice, mrp, variants, description } = sku;

    // Filter active variants only
    const activeVariants = useMemo(() => {
        if (!variants) return [];
        return variants.filter(variant => variant.isActive === 1);
    }, [variants]);

    // Check if main SKU is active
    const isSkuActive = sku.isActive === 1;

    const [quantity, setQuantity] = useState(0);
    const [selectedVariant, setSelectedVariant] = useState<SKUVariant | null>(() => {
        if (activeVariants && activeVariants.length > 0) return activeVariants[0];
        return null;
    });

    const {
        getActiveSessionCart,
        addItemToCartMutation,
        updateCartItemQuantityMutation,
    } = useSession();

    const {
        checkSessionAndExecute,
        showSessionModal,
        setShowSessionModal,
        handleSessionCreated
    } = useSessionCheck();

    useEffect(() => {
        const cart = getActiveSessionCart();
        const itemInCart = cart.find((item: CartItem) =>
            item.skuId === skuId &&
            (selectedVariant ? item.variantSkuId === selectedVariant.skuId : !item.variantSkuId)
        );
        setQuantity(itemInCart ? itemInCart.quantity : 0);
    }, [skuId, selectedVariant, getActiveSessionCart, variants, addItemToCartMutation.isSuccess, updateCartItemQuantityMutation.isSuccess]);

    const currentDisplayPrice = useMemo(() => selectedVariant ? selectedVariant.sellingPrice : sellingPrice, [selectedVariant, sellingPrice]);
    const currentMrp = useMemo(() => selectedVariant ? selectedVariant.mrp : mrp, [selectedVariant, mrp]);
    const discountValue = useMemo(() => currentMrp! - currentDisplayPrice!, [currentMrp, currentDisplayPrice]);
    const calculatedDiscountText = useMemo(() => discountValue > 0
        ? `${Math.round((discountValue / currentMrp!) * 100)}% OFF`
        : null, [discountValue, currentMrp]);

    // Check if current selection is available (SKU active and variant active if applicable)
    const isCurrentSelectionActive = useMemo(() => {
        if (!isSkuActive) return false;
        if (selectedVariant) {
            return selectedVariant.isActive === 1;
        }
        return true;
    }, [isSkuActive, selectedVariant]);

    const handleInitialAddToCart = () => {
        if (!isCurrentSelectionActive) return;

        const variantToUse = selectedVariant || (activeVariants && activeVariants.length === 1 ? activeVariants[0] : null);

        checkSessionAndExecute(() => {
            addItemToCartMutation.mutate({
                skuId: skuId,
                quantity: 1,
                variantSkuId: variantToUse?.skuId
            });
        });
    };

    const handleQuantityChange = (increment: boolean) => {
        if (!isCurrentSelectionActive) return;

        const variantToUse = selectedVariant || (activeVariants && activeVariants.length === 1 ? activeVariants[0] : null);
        if (increment) {
            updateCartItemQuantityMutation.mutate({
                productId: skuId,
                variantId: variantToUse?.skuId,
                newQuantity: quantity + 1
            });
        } else if (quantity > 0) {
            updateCartItemQuantityMutation.mutate({
                productId: skuId,
                variantId: variantToUse?.skuId,
                newQuantity: quantity - 1
            });
        }
    };

    const handleSelectVariant = (variant: SKUVariant) => {
        if (variant.isActive === 1) {
            setSelectedVariant(variant);
        }
    };

    // Fixed variant name display: use variantName with fallback to name
    const displayUnitName = useMemo(() => {
        if (selectedVariant) {
            return selectedVariant.variantName?.[currentLanguage] ||
                selectedVariant.variantName?.en ||
                selectedVariant.name[currentLanguage] ||
                selectedVariant.name.en;
        }
        if (activeVariants && activeVariants.length === 1) {
            const variant = activeVariants[0];
            return variant.variantName?.[currentLanguage] ||
                variant.variantName?.en ||
                variant.name[currentLanguage] ||
                variant.name.en;
        }
        return null;
    }, [selectedVariant, activeVariants, currentLanguage]);

    const displayName = useMemo(() => name[currentLanguage], [name, currentLanguage]);

    return (
        <div className="flex flex-col space-y-6">
            <h1 className="text-3xl font-bold text-gray-800">{displayName}</h1>

            {/* Pricing Section */}
            <div className="flex items-baseline space-x-2">
                <p className="text-3xl font-semibold text-primary">₹{currentDisplayPrice}</p>
                {currentMrp && currentDisplayPrice && currentMrp > currentDisplayPrice && (
                    <p className="text-lg text-gray-400 line-through">₹{currentMrp}</p>
                )}
                {calculatedDiscountText && (
                    <span className="text-sm font-semibold text-green-600 bg-green-100 px-2 py-1 rounded">
                        {calculatedDiscountText}
                    </span>
                )}
            </div>

            {/* Out of Stock Message */}
            {!isCurrentSelectionActive && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <p className="text-red-600 font-medium">
                        {t('product.detail.outOfStock', 'Out of Stock')}
                    </p>
                </div>
            )}

            {/* Variant Selection */}
            {variants && variants.length > 0 && (
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                        {t('product.detail.selectVariant', 'Select Size/Variant')}
                    </label>
                    {variants.length > 1 ? (
                        <div className="flex flex-wrap gap-2">
                            {variants.map((variant) => {
                                const isVariantActive = variant.isActive === 1;
                                const isSelected = selectedVariant?.skuId === variant.skuId;
                                const variantDisplayName = variant.variantName?.[currentLanguage] ||
                                    variant.variantName?.en ||
                                    variant.name[currentLanguage] ||
                                    variant.name.en;

                                return (
                                    <button
                                        key={variant.skuId}
                                        onClick={() => handleSelectVariant(variant)}
                                        disabled={!isVariantActive}
                                        className={`px-4 py-2 border rounded-lg text-sm font-medium transition-colors duration-150 focus:outline-none 
                                            ${!isVariantActive
                                                ? 'bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed opacity-50'
                                                : isSelected
                                                    ? 'bg-green-600 text-white border-green-600 focus:ring-2 focus:ring-offset-2 focus:ring-green-500'
                                                    : 'bg-gray-100 hover:bg-gray-200 text-gray-700 border-gray-300 focus:ring-2 focus:ring-offset-2 focus:ring-green-500'
                                            }`}
                                    >
                                        {variantDisplayName} - ₹{variant.sellingPrice}
                                        {!isVariantActive && (
                                            <span className="block text-xs text-gray-400 mt-1">
                                                {t('product.detail.outOfStock', 'Out of Stock')}
                                            </span>
                                        )}
                                    </button>
                                );
                            })}
                        </div>
                    ) : (
                        <div className={`p-3 border rounded-lg ${isCurrentSelectionActive ? 'border-gray-200 bg-gray-50' : 'border-red-200 bg-red-50'}`}>
                            <span className={isCurrentSelectionActive ? 'text-gray-700' : 'text-red-600'}>
                                {displayUnitName}
                                {!isCurrentSelectionActive && (
                                    <span className="block text-sm text-red-500 mt-1">
                                        {t('product.detail.outOfStock', 'Out of Stock')}
                                    </span>
                                )}
                            </span>
                        </div>
                    )}
                </div>
            )}

            {/* Add to Cart / Quantity Control */}
            <div className="mt-6">
                {!isCurrentSelectionActive ? (
                    <button
                        disabled
                        className="w-full sm:w-auto flex items-center justify-center px-4 py-2 border border-gray-300 text-base font-semibold rounded-lg text-gray-400 bg-gray-100 cursor-not-allowed text-sm"
                    >
                        {t('product.detail.outOfStock', 'Out of Stock')}
                    </button>
                ) : quantity === 0 ? (
                    <button
                        onClick={handleInitialAddToCart}
                        disabled={addItemToCartMutation.isPending}
                        className="w-full sm:w-auto flex items-center justify-center px-4 py-2 border border-green-600 text-base font-semibold rounded-lg text-green-600 bg-green-50 hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200 disabled:opacity-50 text-sm"
                    >
                        {addItemToCartMutation.isPending ? t('product.detail.adding', 'Adding...') : t('product.detail.addToCart', 'Add to Cart')}
                    </button>
                ) : (
                    <div className="flex items-center justify-start w-full sm:w-auto">
                        <div className="inline-flex items-center justify-between bg-green-600 text-white rounded-lg h-10 shadow-sm" style={{ width: '120px' /* Approximate width of ProductCard controls */ }}>
                            <button
                                onClick={() => handleQuantityChange(false)}
                                disabled={updateCartItemQuantityMutation.isPending}
                                className="w-10 h-10 flex items-center justify-center text-xl hover:bg-green-700 rounded-l-lg transition-colors duration-200 disabled:opacity-70"
                                aria-label={t('product.detail.decreaseQuantity', 'Decrease quantity')}
                            >
                                -
                            </button>
                            <span className="font-medium text-sm tabular-nums">
                                {updateCartItemQuantityMutation.isPending && updateCartItemQuantityMutation.variables?.newQuantity === quantity - 1 ? '...' : quantity}
                            </span>
                            <button
                                onClick={() => handleQuantityChange(true)}
                                disabled={updateCartItemQuantityMutation.isPending}
                                className="w-10 h-10 flex items-center justify-center text-xl hover:bg-green-700 rounded-r-lg transition-colors duration-200 disabled:opacity-70"
                                aria-label={t('product.detail.increaseQuantity', 'Increase quantity')}
                            >
                                +
                            </button>
                        </div>
                    </div>
                )}
            </div>

            {/* Product Description */}
            {description && (
                <div className="pt-6 border-t border-gray-200">
                    <h2 className="text-xl font-semibold text-gray-800 mb-2">{t('product.detail.description', 'Product Description')}</h2>
                    <p className="text-gray-600 whitespace-pre-line">{description[currentLanguage]}</p>
                </div>
            )}

            {/* Session Modal */}
            <SessionModal
                isOpen={showSessionModal}
                onClose={() => setShowSessionModal(false)}
                onSave={handleSessionCreated}
                initialData={{ customerName: '', customerPhone: '', location: undefined }}
            />
        </div>
    );
};

export default ProductInformation; 
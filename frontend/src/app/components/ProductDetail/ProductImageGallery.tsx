'use client';

import React, { useState, useMemo } from 'react';
import Image from 'next/image';

interface ProductImageGalleryProps {
    productName: string;
    primaryImageUrl: string;
    additionalImages?: string[];
}

// Gray SVG placeholder component (matching SkuCard.tsx style)
const ImagePlaceholder: React.FC<{ className?: string }> = ({ className = "" }) => (
    <div className={`bg-gray-200 flex items-center justify-center ${className}`}>
        <svg
            className="w-16 h-16 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
            />
        </svg>
    </div>
);

const ProductImageGallery: React.FC<ProductImageGalleryProps> = ({
    productName,
    primaryImageUrl,
    additionalImages
}) => {
    const allImages = useMemo(() => [primaryImageUrl, ...(additionalImages || [])], [primaryImageUrl, additionalImages]);
    const [currentImage, setCurrentImage] = useState(primaryImageUrl);
    const [failedImages, setFailedImages] = useState<Set<string>>(new Set());

    // Ensure there are no duplicate keys if primaryImageUrl is also in additionalImages
    const uniqueImages = useMemo(() => Array.from(new Set(allImages)), [allImages]);

    const handleImageError = (imageUrl: string) => {
        setFailedImages(prev => new Set(prev).add(imageUrl));
    };

    const isImageFailed = (imageUrl: string) => {
        return !imageUrl || imageUrl.trim() === '' || failedImages.has(imageUrl);
    };

    return (
        <div className="flex flex-col items-center">
            <div className="relative w-full aspect-square rounded-lg overflow-hidden shadow-lg mb-4 border border-gray-200">
                {isImageFailed(currentImage) ? (
                    <ImagePlaceholder className="w-full h-full rounded-lg" />
                ) : (
                    <Image
                        src={currentImage}
                        alt={`Image of ${productName}`}
                        fill
                        className="object-contain"
                        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                        priority
                        onError={() => handleImageError(currentImage)}
                    />
                )}
            </div>
            {uniqueImages.length > 1 && (
                <div className="flex space-x-2 overflow-x-auto pb-2">
                    {uniqueImages.map((imgUrl, index) => (
                        <button
                            key={index}
                            onClick={() => setCurrentImage(imgUrl)}
                            className={`relative w-20 h-20 rounded-md overflow-hidden border-2 transition-colors duration-150 hover:border-primary focus:outline-none focus:border-primary ${currentImage === imgUrl ? 'border-primary' : 'border-gray-300'}`}
                        >
                            {isImageFailed(imgUrl) ? (
                                <ImagePlaceholder className="w-full h-full rounded-md" />
                            ) : (
                                <Image
                                    src={imgUrl}
                                    alt={`${productName} thumbnail ${index + 1}`}
                                    fill
                                    className="object-cover"
                                    sizes="80px"
                                    onError={() => handleImageError(imgUrl)}
                                />
                            )}
                        </button>
                    ))}
                </div>
            )}
        </div>
    );
};

export default ProductImageGallery; 
"use client";

import React, { useState } from 'react';
import { Header } from "../Header/Header";
import ResponsiveLayout from "./ResponsiveLayout";

interface AuthenticatedLayoutProps {
    children: React.ReactNode;
}

const AuthenticatedLayout: React.FC<AuthenticatedLayoutProps> = ({ children }) => {
    const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);

    const handleSidebarCollapseChange = (isCollapsed: boolean) => {
        setIsSidebarCollapsed(isCollapsed);
    };

    return (
        <div className="min-h-screen flex flex-col">
            <Header isSidebarCollapsed={isSidebarCollapsed} />
            <ResponsiveLayout
                isSidebarCollapsed={isSidebarCollapsed}
                onSidebarCollapseChange={handleSidebarCollapseChange}
            >
                {children}
            </ResponsiveLayout>
        </div>
    );
};

export default AuthenticatedLayout; 
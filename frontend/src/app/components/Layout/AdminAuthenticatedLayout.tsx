"use client";

import React, { useState, useEffect } from 'react';
import { useAuth } from '../../context/AuthContext';
import { useAdminPermissions } from '../../hooks/useAdminPermissions';
import LoginModal from '../LoginModal';
import { useTranslation } from 'react-i18next';

interface AdminAuthenticatedLayoutProps {
    children: React.ReactNode;
}

const AdminAuthenticatedLayout: React.FC<AdminAuthenticatedLayoutProps> = ({ children }) => {
    const { t } = useTranslation();
    const { isUserLoggedIn, isLoading } = useAuth();
    const { isAdmin, isLoadingAdminRoles, adminRoles, adminRolesChecked } = useAdminPermissions();
    const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);

    // Show login modal when user is not logged in
    useEffect(() => {
        if (!isLoading && !isUserLoggedIn) {
            setIsLoginModalOpen(true);
        } else if (isUserLoggedIn) {
            setIsLoginModalOpen(false);
        }
    }, [isUserLoggedIn]);

    // Show login modal if user is not logged in
    if (!isUserLoggedIn) {
        return (
            <>
                <LoginModal
                    isOpen={isLoginModalOpen}
                    onClose={() => { }} // Don't allow closing for admin routes
                    dismissable={false} // Make login mandatory
                />
            </>
        );
    }

    // Show loading state while admin roles are being loaded (only if we haven't checked yet)
    if (isUserLoggedIn && isLoadingAdminRoles && !adminRolesChecked) {
        return (
            <div className="fixed inset-0 flex items-center justify-center bg-gray-50">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <p className="text-gray-600">{t('admin.auth.loadingPermissions', 'Loading permissions...')}</p>
                </div>
            </div>
        );
    }

    return <>{children}</>;
};

export default AdminAuthenticatedLayout; 
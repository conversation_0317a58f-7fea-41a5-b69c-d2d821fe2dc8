'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import CategoriesSidebar from '@/app/components/CategoriesDrawer/CategoriesSidebar';

interface ResponsiveLayoutProps {
    children: React.ReactNode;
    isSidebarCollapsed?: boolean;
    onSidebarCollapseChange?: (isCollapsed: boolean) => void;
}

export default function ResponsiveLayout({
    children,
    isSidebarCollapsed = false,
    onSidebarCollapseChange
}: ResponsiveLayoutProps) {
    const router = useRouter();

    const handleSelectCategory = (categoryId: string) => {
        console.log('Selected category ID from ResponsiveLayout:', categoryId);
        router.push(`/categories/${categoryId}`);
    };

    return (
        <div className="flex-1">
            {/* Categories Sidebar - Desktop/Tablet only */}
            <CategoriesSidebar
                onSelectCategory={handleSelectCategory}
                onCollapseChange={onSidebarCollapseChange}
            />

            {/* Main Content Area */}
            <main className={`overflow-y-auto bg-gray-50 min-h-screen transition-all duration-300 ease-in-out ${isSidebarCollapsed ? 'md:ml-16' : 'md:ml-64'
                }`}>
                {children}
            </main>
        </div>
    );
} 
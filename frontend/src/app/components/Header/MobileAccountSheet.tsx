'use client';

import React from 'react';
import { useTranslation } from 'react-i18next';
import { UserDetails } from '../../types/auth';

interface MobileAccountSheetProps {
    isOpen: boolean;
    onClose: () => void;
    isUserLoggedIn: boolean;
    userDetails: UserDetails | null;
    onUserLogin: () => void;
    onUserLogout: () => void;
    onAccountClick: () => void;
}

export default function MobileAccountSheet({
    isOpen,
    onClose,
    isUserLoggedIn,
    userDetails,
    onUserLogin,
    onUserLogout,
    onAccountClick,
}: MobileAccountSheetProps) {
    const { t } = useTranslation();

    if (!isOpen) return null;

    const handleLogin = () => {
        onUserLogin();
        onClose(); // Close sheet after action
    };

    const handleLogout = () => {
        onUserLogout();
        onClose(); // Close sheet after action
    };

    const handleAccount = () => {
        onAccountClick();
        onClose();
    };

    const handleProfileClick = () => {
        // Placeholder for profile navigation
        console.log('Navigate to profile');
        onClose();
    };

    const handleSettingsClick = () => {
        // Placeholder for settings navigation
        console.log('Navigate to settings');
        onClose();
    };

    return (
        <div className="md:hidden fixed inset-0 z-50 flex items-end">
            {/* Overlay */}
            <div className="fixed inset-0 bg-black/50 transition-opacity duration-300 ease-in-out" onClick={onClose}></div>

            {/* Bottom Sheet Panel */}
            <div className="relative w-full bg-white shadow-xl rounded-t-lg transition-transform duration-300 ease-in-out transform translate-y-0 flex flex-col max-h-[80vh]">
                {/* Header */}
                <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-white flex-shrink-0">
                    <h3 className="text-lg font-semibold text-gray-800">
                        {isUserLoggedIn ? (userDetails?.name || t('mobileAccountSheet.myAccount', 'My Account')) : t('mobileAccountSheet.loginRequired', 'Login / Sign Up')}
                    </h3>
                    <button onClick={onClose} className="text-gray-500 hover:text-gray-700 p-1 rounded-full hover:bg-gray-100">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>

                {/* Account Options - Scrollable if content grows */}
                <div className="flex-grow overflow-y-auto p-4 space-y-2">
                    {isUserLoggedIn ? (
                        <>
                            {userDetails?.name && (
                                <div className="mb-4 p-3 bg-gray-100 rounded-md">
                                    <p className="text-sm text-gray-600">{t('mobileAccountSheet.loggedInAs', 'Logged in as:')}</p>
                                    <p className="font-semibold text-gray-800">{userDetails.name}</p>
                                </div>
                            )}
                            <button
                                onClick={handleAccount}
                                className="w-full text-left px-3 py-3 text-gray-700 hover:bg-gray-100 rounded-md transition-colors font-medium"
                            >
                                {t('header.accountMenu.account', 'Account')}
                            </button>
                            {/* <button
                                onClick={handleProfileClick}
                                className="w-full text-left px-3 py-3 text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
                            >
                                {t('mobileAccountSheet.profile', 'View Profile')}
                            </button>
                            <button
                                onClick={handleSettingsClick}
                                className="w-full text-left px-3 py-3 text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
                            >
                                {t('mobileAccountSheet.settings', 'Account Settings')}
                            </button> */}
                            <div className="pt-2">
                                <button
                                    onClick={handleLogout}
                                    className="w-full text-left px-3 py-3 text-red-600 hover:bg-red-50 rounded-md transition-colors font-medium"
                                >
                                    {t('mobileAccountSheet.logout', 'Logout')}
                                </button>
                            </div>
                        </>
                    ) : (
                        <button
                            onClick={handleLogin}
                            className="w-full py-3 px-4 bg-[var(--color-green-600)] hover:brightness-90 text-white rounded-md shadow-sm transition-colors duration-150 ease-in-out font-medium"
                        >
                            {t('mobileAccountSheet.login', 'Login / Sign Up')}
                        </button>
                    )}
                </div>
            </div>
        </div>
    );
} 
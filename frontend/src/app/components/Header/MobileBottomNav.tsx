'use client';

import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import Link from 'next/link';

interface MobileBottomNavItemProps {
    id: string;
    label: string;
    icon: React.ReactNode;
    action?: () => void;
    badgeCount?: number;
    display: boolean;
}

interface MobileBottomNavProps {
    onCategoriesClick: () => void;
    onHomeClick: () => void;
    onSessionsClick: () => void;
    onCartClick: () => void;
    cartItemCount: number;
    showSessionsIcon?: boolean;
}

export default function MobileBottomNav({
    onCategoriesClick,
    onHomeClick,
    onSessionsClick,
    onCartClick,
    cartItemCount,
    showSessionsIcon = true,
}: MobileBottomNavProps) {
    const { t } = useTranslation();

    const allNavItems = useMemo<MobileBottomNavItemProps[]>(() => [
        {
            id: 'categories',
            label: t('mobileNav.categories', 'Categories'),
            icon: (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                    <path strokeLinecap="round" strokeLinejoin="round" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                </svg>
            ),
            action: onCategoriesClick,
            display: true,
        },
        {
            id: 'home',
            label: t('mobileNav.home', 'Home'),
            icon: (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                    <path strokeLinecap="round" strokeLinejoin="round" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                </svg>
            ),
            action: onHomeClick,
            display: true,
        },
        {
            id: 'sessions',
            label: t('mobileNav.sessions', 'Sessions'),
            icon: (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                    <path strokeLinecap="round" strokeLinejoin="round" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 016-6h6a6 6 0 016 6v1h-3M15 21a3 3 0 00-3-3H9a3 3 0 00-3 3m12 0a3 3 0 003-3V9M9 3.75M15 3.75M3 9h18" />
                </svg>
            ),
            action: onSessionsClick,
            display: showSessionsIcon,
        },
        {
            id: 'cart',
            label: t('mobileNav.cart', 'Cart'),
            icon: (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                    <path strokeLinecap="round" strokeLinejoin="round" d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
            ),
            action: onCartClick,
            badgeCount: cartItemCount,
            display: true,
        },
    ], [t, onCategoriesClick, onHomeClick, onSessionsClick, onCartClick, cartItemCount, showSessionsIcon]);

    const navItems = allNavItems.filter(item => item.display);

    return (
        <div className="md:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-t-lg z-40">
            <div className="flex justify-around items-center h-16">
                {navItems.map((item) => (
                    <button
                        key={item.id}
                        onClick={item.action}
                        className="flex flex-col items-center justify-center text-gray-600 hover:text-[var(--color-green-600)] transition-colors px-2 py-1 w-1/4 relative"
                        aria-label={item.label}
                    >
                        <div className="relative">
                            {item.icon}
                            {item.badgeCount !== undefined && item.badgeCount > 0 && (
                                <span className="absolute -top-2 -right-2.5 bg-red-500 text-white text-xs font-semibold rounded-full h-5 w-5 flex items-center justify-center border-2 border-white">
                                    {item.badgeCount > 9 ? '9+' : item.badgeCount}
                                </span>
                            )}
                        </div>
                        <span className="text-xs mt-1">{item.label}</span>
                    </button>
                ))}
            </div>
        </div>
    );
} 
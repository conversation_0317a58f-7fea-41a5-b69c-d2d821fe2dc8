'use client';

import React, { useState } from 'react';
import { useSession } from '../../context/SessionContext';
import { SessionTabDisplay } from '../../types/session'; // Re-using this type for now
import { useTranslation } from 'react-i18next';
import { SessionModal, ConfirmationDialog } from './SessionManager'; // Re-use SessionModal for adding/editing and ConfirmationDialog
import { getNextGuestNumber } from '../../../lib/utils';

interface MobileSessionsSheetProps {
    isOpen: boolean;
    onClose: () => void;
    // Props from Header for session management actions, similar to SessionManager
    sessions: SessionTabDisplay[];
    activeSessionId: string;
    onTabSelect: (tabId: string) => void;
    onNewTab: (sessionData: { customerName: string; customerPhone?: string; location?: { lat: number; lng: number } }) => void;
    onCloseTab: (tabId: string) => void;
    onUpdateTab: (tabId: string, data: { customerName: string; customerPhone?: string; location?: { lat: number; lng: number } }) => void;
}

export default function MobileSessionsSheet({
    isOpen,
    onClose,
    sessions,
    activeSessionId,
    onTabSelect,
    onNewTab,
    onCloseTab,
    onUpdateTab
}: MobileSessionsSheetProps) {
    const { t } = useTranslation();
    const [isSessionModalOpen, setIsSessionModalOpen] = useState(false);
    const [editingSessionId, setEditingSessionId] = useState<string | null>(null);
    const [isConfirmDeleteDialogOpen, setIsConfirmDeleteDialogOpen] = useState(false);
    const [sessionToDeleteId, setSessionToDeleteId] = useState<string | null>(null);

    if (!isOpen) return null;

    const handleAddNewSession = () => {
        const sessionNames = sessions.map(session => session.customerName || '');
        const nextNumber = getNextGuestNumber(sessionNames);
        const guestName = `Guest ${nextNumber}`;

        onNewTab({
            customerName: guestName,
            customerPhone: undefined,
            location: undefined
        });

        // Optionally close the sheet after creating new session
        // onClose();
    };

    const handleEditSession = (sessionId: string) => {
        setEditingSessionId(sessionId);
        setIsSessionModalOpen(true);
    };

    const handleSaveSessionModal = (data: { customerName: string; customerPhone?: string; location?: { lat: number; lng: number } }) => {
        if (editingSessionId) {
            onUpdateTab(editingSessionId, data);
        } else {
            onNewTab(data);
        }
        setIsSessionModalOpen(false);
        setEditingSessionId(null);
        // Optionally, you might want to close the main sheet after saving if it's a new session
        // if (!editingSessionId) onClose(); 
    };

    const getInitialModalData = () => {
        if (editingSessionId) {
            const session = sessions.find(s => s.id === editingSessionId);
            if (session) {
                return {
                    customerName: session.customerName || '',
                    customerPhone: session.customerPhone || '',
                    location: session.location
                };
            }
        }
        return { customerName: '', customerPhone: '', location: undefined };
    };

    const handleDeleteRequest = (sessionId: string) => {
        setSessionToDeleteId(sessionId);
        setIsConfirmDeleteDialogOpen(true);
    };

    const executeDeleteSession = () => {
        if (sessionToDeleteId) {
            onCloseTab(sessionToDeleteId);
        }
        setIsConfirmDeleteDialogOpen(false);
        setSessionToDeleteId(null);
    };

    return (
        <div className="md:hidden fixed inset-0 z-50 flex items-end">
            {/* Overlay */}
            <div className="fixed inset-0 bg-black/50 transition-opacity duration-300 ease-in-out" onClick={onClose}></div>

            {/* Bottom Sheet Panel */}
            <div className="relative w-full bg-gray-50 shadow-xl rounded-t-lg transition-transform duration-300 ease-in-out transform translate-y-0 flex flex-col max-h-[80vh]">
                {/* Header */}
                <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-white flex-shrink-0">
                    <h3 className="text-lg font-semibold text-gray-800">{t('mobileSessionsSheet.title', 'Manage Sessions')}</h3>
                    <button onClick={onClose} className="text-gray-500 hover:text-gray-700 p-1 rounded-full hover:bg-gray-100">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>

                {/* Sessions List - Scrollable */}
                <div className="flex-grow overflow-y-auto p-4 space-y-3">
                    {sessions.map((session, index) => (
                        <div
                            key={session.id}
                            className={`p-3 rounded-lg border transition-all cursor-pointer ${activeSessionId === session.id ? 'bg-emerald-50 border-emerald-500 shadow-sm' : 'bg-white border-gray-200 hover:bg-gray-100'}`}
                        >
                            <div className="flex items-center justify-between">
                                <div onClick={() => { onTabSelect(session.id); onClose(); }} className="flex-grow min-w-0">
                                    <p className={`font-medium truncate ${activeSessionId === session.id ? 'text-emerald-700' : 'text-gray-800'}`}>
                                        {session.customerName || `${t('session.session', 'Session')} ${index + 1}`}
                                    </p>
                                    {session.customerPhone && <p className="text-xs text-gray-500 truncate">{session.customerPhone}</p>}
                                    {(session.cartItemsCount || 0) > 0 && (
                                        <p className="text-xs text-emerald-600 mt-0.5">
                                            {session.cartItemsCount || 0} {(session.cartItemsCount || 0) === 1 ? t('session.itemInCart', 'item') : t('session.itemsInCart', 'items')}
                                        </p>
                                    )}
                                </div>
                                <div className="flex-shrink-0 space-x-2 ml-2">
                                    <button
                                        onClick={() => handleEditSession(session.id)}
                                        className="p-1.5 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-md"
                                        aria-label={t('common.edit', 'Edit')}
                                    >
                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path d="M17.414 2.586a2 2 0 00-2.828 0L7 10.172V13h2.828l7.586-7.586a2 2 0 000-2.828z" /><path fillRule="evenodd" d="M2 6a2 2 0 012-2h4a1 1 0 010 2H4v10h10v-4a1 1 0 112 0v4a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" clipRule="evenodd" /></svg>
                                    </button>
                                    {sessions.length > 1 && (
                                        <button
                                            onClick={() => handleDeleteRequest(session.id)}
                                            className="p-1.5 text-gray-500 hover:text-red-600 hover:bg-red-50 rounded-md"
                                            aria-label={t('common.delete', 'Delete')}
                                        >
                                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" /></svg>
                                        </button>
                                    )}
                                </div>
                            </div>
                        </div>
                    ))}
                    {sessions.length === 0 && (
                        <p className="text-center text-gray-500 py-4">{t('mobileSessionsSheet.noSessions', 'No active sessions.')}</p>
                    )}
                </div>

                {/* Add New Session Button */}
                <div className="p-4 border-t border-gray-200 bg-white flex-shrink-0">
                    <button
                        onClick={handleAddNewSession}
                        className="w-full flex items-center justify-center space-x-2 py-3 px-4 bg-[var(--color-green-600)] hover:brightness-90 text-white rounded-md shadow-sm transition-colors duration-150 ease-in-out"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                            <path strokeLinecap="round" strokeLinejoin="round" d="M12 4v16m8-8H4" />
                        </svg>
                        <span>{t('session.addNew', 'Add New Session')}</span>
                    </button>
                </div>
            </div>

            {/* Session Modal for Add/Edit - Re-using from SessionManager */}
            <SessionModal
                isOpen={isSessionModalOpen}
                onClose={() => {
                    setIsSessionModalOpen(false);
                    setEditingSessionId(null);
                }}
                onSave={handleSaveSessionModal}
                initialData={getInitialModalData()}
            />

            {/* Confirmation Dialog for Delete */}
            <ConfirmationDialog
                isOpen={isConfirmDeleteDialogOpen}
                onClose={() => setIsConfirmDeleteDialogOpen(false)}
                onConfirm={executeDeleteSession}
                title={t('session.confirmDelete', 'Delete Session')}
                message={t('session.confirmDeleteMessage', 'Are you sure you want to delete this session? This action cannot be undone.')}
            />
        </div>
    );
} 
import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslation } from 'react-i18next';
import { Button } from "@/components/ui/button";
import { ChevronDownIcon } from '@heroicons/react/24/outline';

interface Language {
    code: string;
    name: string;
    flag: string;
}

const languages: Language[] = [
    { code: 'en', name: 'English', flag: '🇬🇧' },
    { code: 'ta', name: 'தமிழ்', flag: '🇮🇳' }
];

export function LanguageSwitcher() {
    const router = useRouter();
    const { i18n } = useTranslation();
    const [isOpen, setIsOpen] = useState(false);
    const [currentLang, setCurrentLang] = useState<Language>(languages[0]);
    const dropdownRef = useRef<HTMLDivElement>(null);

    // Get current language from i18n on component mount
    useEffect(() => {
        const currentLangCode = i18n.language || 'en';
        const lang = languages.find(l => l.code === currentLangCode) || languages[0];
        setCurrentLang(lang);
    }, [i18n.language]);

    // Handle click outside to close dropdown
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setIsOpen(false);
            }
        };

        // Only add listener when dropdown is open
        if (isOpen) {
            document.addEventListener('mousedown', handleClickOutside);
        }

        // Cleanup function to remove event listener
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [isOpen]);

    const handleLanguageChange = (language: Language) => {
        setCurrentLang(language);
        i18n.changeLanguage(language.code);
        localStorage.setItem('preferredLanguage', language.code);

        // In a real implementation with locale-based routing:
        // router.push(`/${language.code}${pathname.substr(3)}`);

        setIsOpen(false);
    };

    return (
        <div className="relative" ref={dropdownRef}>
            <Button
                variant="outline"
                size="sm"
                className="flex items-center space-x-1 h-9 text-foreground"
                onClick={() => setIsOpen(!isOpen)}
            >
                <span className="hidden sm:inline text-foreground">{currentLang.name}</span>
                <ChevronDownIcon
                    className={`h-4 w-4 transition-transform text-foreground ${isOpen ? 'rotate-180' : ''}`}
                />
            </Button>

            {isOpen && (
                <div className="absolute right-0 mt-2 bg-popover rounded-md shadow-lg z-10 w-40 border border-border">
                    <ul className="py-1">
                        {languages.map((language) => (
                            <li key={language.code}>
                                <button
                                    className={`w-full text-left px-4 py-2 text-sm transition-colors ${currentLang.code === language.code
                                        ? 'bg-accent text-accent-foreground font-semibold'
                                        : 'text-popover-foreground hover:bg-accent hover:text-accent-foreground'
                                        }`}
                                    onClick={() => handleLanguageChange(language)}
                                >
                                    {language.name}
                                </button>
                            </li>
                        ))}
                    </ul>
                </div>
            )}
        </div>
    );
} 
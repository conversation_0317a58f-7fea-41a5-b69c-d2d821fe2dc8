import React, { useState, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import MapSelector from '../MapSelector';
import Modal from 'react-modal';
import { SessionTabDisplay } from '../../types/session';
import { getNextGuestNumber } from '../../../lib/utils';

interface SessionManagerProps {
    tabs: SessionTabDisplay[];
    activeTabId: string;
    onTabSelect: (tabId: string) => void;
    onNewTab: (sessionData: { customerName: string; customerPhone?: string; location?: { lat: number; lng: number } }) => void;
    onCloseTab: (tabId: string) => void;
    onUpdateTab?: (tabId: string, data: { customerName: string; customerPhone?: string; location?: { lat: number; lng: number } }) => void;
}

// Custom styles for the Modal
const customModalStyles = {
    overlay: {
        backgroundColor: 'rgba(0, 0, 0, 0.3)',
        backdropFilter: 'blur(4px)',
        zIndex: 50
    },
    content: {
        top: '50%',
        left: '50%',
        right: 'auto',
        bottom: 'auto',
        marginRight: '-50%',
        transform: 'translate(-50%, -50%)',
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        backdropFilter: 'blur(4px)',
        borderRadius: '0.5rem',
        padding: '1.5rem',
        maxWidth: '500px',
        width: '100%'
    }
};

// Session Modal component for adding/editing sessions
export const SessionModal = ({
    isOpen,
    onClose,
    onSave,
    initialData = { customerName: '', customerPhone: '', location: undefined }
}: {
    isOpen: boolean;
    onClose: () => void;
    onSave: (data: { customerName: string; customerPhone?: string; location?: { lat: number; lng: number } }) => void;
    initialData?: { customerName: string; customerPhone?: string; location?: { lat: number; lng: number } };
}) => {
    const { t } = useTranslation();
    const [name, setName] = useState(initialData.customerName || '');
    const [phone, setPhone] = useState(initialData.customerPhone || '');
    const [location, setLocation] = useState(initialData.location);
    const [nameError, setNameError] = useState('');

    // Reset form when modal opens with new data
    useEffect(() => {
        if (isOpen) {
            setName(initialData.customerName || '');
            setPhone(initialData.customerPhone || '');
            setLocation(initialData.location);
            setNameError('');
        }
    }, [isOpen, initialData]);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        // Validate name is not empty
        if (!name.trim()) {
            setNameError(t('session.nameRequired', 'Name is required'));
            return;
        }

        onSave({
            customerName: name.trim(),
            customerPhone: phone.trim() || undefined,
            location
        });
        onClose();
    };

    const handleLocationSelect = (newLocation: { lat: number; lng: number }) => {
        setLocation(newLocation);
    };

    return (
        <Modal
            isOpen={isOpen}
            onRequestClose={onClose}
            style={customModalStyles}
            contentLabel={initialData.customerName ? t('session.editSession', 'Edit Cart Session') : t('session.newSession', 'New Cart Session')}
            closeTimeoutMS={200}
        >
            <h3 className="text-lg font-semibold mb-4">
                {initialData.customerName ? t('session.editSession', 'Edit Cart Session') : t('session.newSession', 'New Cart Session')}
            </h3>

            <form onSubmit={handleSubmit}>
                <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                        {t('session.name', 'Name')} <span className="text-red-500">*</span>
                    </label>
                    <input
                        type="text"
                        className={`w-full px-3 py-2 border ${nameError ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500`}
                        value={name}
                        onChange={(e) => {
                            setName(e.target.value);
                            if (e.target.value.trim()) setNameError('');
                        }}
                        placeholder={t('session.namePlaceholder', 'Enter customer name')}
                    />
                    {nameError && <p className="text-red-500 text-xs mt-1">{nameError}</p>}
                </div>

                <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                        {t('session.phone', 'Phone Number')}
                    </label>
                    <input
                        type="tel"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                        value={phone}
                        onChange={(e) => setPhone(e.target.value)}
                        placeholder={t('session.phonePlaceholder', 'Enter phone number (optional)')}
                    />
                </div>

                <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                        {t('session.location', 'Location')}
                    </label>

                    {/* Always show the map selector */}
                    <div className="mb-3">
                        <MapSelector
                            initialLocation={location}
                            onLocationSelect={handleLocationSelect}
                        />
                        {location && (
                            <p className="mt-2 text-xs text-gray-500">
                                {t('session.selectedCoordinates', 'Selected coordinates')}: {location.lat.toFixed(4)}, {location.lng.toFixed(4)}
                            </p>
                        )}
                    </div>
                </div>

                <div className="flex justify-end space-x-3">
                    <button
                        type="button"
                        className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500"
                        onClick={onClose}
                    >
                        {t('common.cancel', 'Cancel')}
                    </button>
                    <button
                        type="submit"
                        className="px-4 py-2 bg-emerald-500 text-white rounded-md hover:bg-emerald-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500"
                    >
                        {t('common.save', 'Save')}
                    </button>
                </div>
            </form>
        </Modal>
    );
};

// Confirmation Dialog component using react-modal
export const ConfirmationDialog = ({
    isOpen,
    onClose,
    onConfirm,
    title,
    message
}: {
    isOpen: boolean;
    onClose: () => void;
    onConfirm: () => void;
    title: string;
    message: string;
}) => {
    const { t } = useTranslation();

    return (
        <Modal
            isOpen={isOpen}
            onRequestClose={onClose}
            style={customModalStyles}
            contentLabel={title}
            closeTimeoutMS={200}
        >
            <h3 className="text-lg font-semibold mb-2">{title}</h3>
            <p className="text-gray-600 mb-6">{message}</p>

            <div className="flex justify-end space-x-3">
                <button
                    className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500"
                    onClick={onClose}
                >
                    {t('common.cancel', 'Cancel')}
                </button>
                <button
                    type="button"
                    className="px-4 py-2 bg-emerald-600 text-white rounded-md hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500"
                    onClick={() => { onConfirm(); onClose(); }}
                >
                    {t('common.delete', 'Delete')}
                </button>
            </div>
        </Modal>
    );
};

export function SessionManager({
    tabs,
    activeTabId,
    onTabSelect,
    onNewTab,
    onCloseTab,
    onUpdateTab
}: SessionManagerProps) {
    const { t } = useTranslation();
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);
    const [editingTabId, setEditingTabId] = useState<string | null>(null);
    const [tabToDelete, setTabToDelete] = useState<string | null>(null);
    const [contextMenuVisible, setContextMenuVisible] = useState(false);
    const [contextMenuPosition, setContextMenuPosition] = useState({ x: 0, y: 0 });
    const [contextMenuTabId, setContextMenuTabId] = useState<string | null>(null);
    const longPressTimeoutRef = useRef<NodeJS.Timeout | null>(null);

    // Handle creating new guest session directly
    const handleCreateGuestSession = () => {
        const sessionNames = tabs.map(tab => tab.customerName || '');
        const nextNumber = getNextGuestNumber(sessionNames);
        const guestName = `Guest ${nextNumber}`;

        onNewTab({
            customerName: guestName,
            customerPhone: undefined,
            location: undefined
        });
    };

    // Add event listeners for click outside to close context menu
    useEffect(() => {
        const handleClickOutside = (e: MouseEvent) => {
            // Don't close on right-click
            if (e.button === 2) return;

            if (contextMenuVisible) {
                // Check if the click is on the context menu itself
                const contextMenu = document.querySelector('.context-menu');
                if (contextMenu && contextMenu.contains(e.target as Node)) {
                    return;
                }
                setContextMenuVisible(false);
            }
        };

        // Use mousedown instead of click to avoid conflicts with right-click
        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [contextMenuVisible]);

    // Handle long press for mobile
    const handleMouseDown = (e: React.MouseEvent, tabId: string) => {
        // Don't start long press timer on right click
        if (e.button === 2) return;

        longPressTimeoutRef.current = setTimeout(() => {
            const rect = (e.target as HTMLElement).getBoundingClientRect();

            // Use viewport coordinates for positioning
            setContextMenuPosition({ x: e.clientX, y: rect.bottom });
            setContextMenuTabId(tabId);
            setContextMenuVisible(true);
        }, 500); // 500ms long press
    };

    const handleMouseUp = () => {
        if (longPressTimeoutRef.current) {
            clearTimeout(longPressTimeoutRef.current);
            longPressTimeoutRef.current = null;
        }
    };

    // Handle context menu (right click)
    const handleContextMenu = (e: React.MouseEvent, tabId: string) => {
        e.preventDefault();
        e.stopPropagation();

        // Use viewport coordinates for positioning
        setContextMenuPosition({ x: e.clientX, y: e.clientY });
        setContextMenuTabId(tabId);
        setContextMenuVisible(true);
    };

    // Handle opening edit modal
    const handleEditTab = (tabId: string) => {
        const tab = tabs.find(t => t.id === tabId);
        if (tab) {
            setEditingTabId(tabId);
            setIsModalOpen(true);
            setContextMenuVisible(false);
        }
    };

    // Handle saving new session
    const handleSaveNewSession = (data: { customerName: string; customerPhone?: string; location?: { lat: number; lng: number } }) => {
        onNewTab(data);
    };

    // Handle updating existing session
    const handleUpdateSession = (data: { customerName: string; customerPhone?: string; location?: { lat: number; lng: number } }) => {
        if (editingTabId && onUpdateTab) {
            onUpdateTab(editingTabId, data);
            setEditingTabId(null);
        }
    };

    // Handle tab deletion confirmation
    const handleConfirmDelete = (tabId: string) => {
        setTabToDelete(tabId);
        setIsConfirmDialogOpen(true);
        setContextMenuVisible(false);
    };

    // Execute tab deletion after confirmation
    const executeDelete = () => {
        if (tabToDelete) {
            onCloseTab(tabToDelete);
            setTabToDelete(null);
        }
    };

    // Get current tab data for editing
    const getInitialData = () => {
        if (editingTabId) {
            const tab = tabs.find(t => t.id === editingTabId);
            if (tab) {
                return {
                    customerName: tab.customerName || '',
                    customerPhone: tab.customerPhone || '',
                    location: tab.location
                };
            }
        }
        return { customerName: '', customerPhone: '', location: undefined };
    };

    return (
        <div className="session-manager">
            <div className="tabs-container flex items-center overflow-x-auto no-scrollbar">
                {tabs.map((tab) => (
                    <div
                        key={tab.id}
                        className={`tab flex items-center min-w-[200px] max-w-[250px] mr-1 px-3 py-2 rounded-t-lg cursor-pointer transition-all ${activeTabId === tab.id
                            ? 'bg-green-100 text-green-800 font-medium border border-green-300 border-b-0 shadow-sm'
                            : 'bg-white text-white-700 hover:bg-white-300 border-b border-white'
                            }`}
                        onClick={() => onTabSelect(tab.id)}
                        onMouseDown={(e) => handleMouseDown(e, tab.id)}
                        onMouseUp={handleMouseUp}
                        onTouchStart={() => {
                            longPressTimeoutRef.current = setTimeout(() => {
                                handleEditTab(tab.id);
                            }, 800);
                        }}
                        onTouchEnd={() => {
                            if (longPressTimeoutRef.current) {
                                clearTimeout(longPressTimeoutRef.current);
                                longPressTimeoutRef.current = null;
                            }
                        }}
                        onContextMenu={(e) => handleContextMenu(e, tab.id)}
                        title={tab.customerName || t('session.newSession', 'New Session')}
                    >
                        <div className="flex-grow overflow-hidden pr-2">
                            <div className="text-sm font-medium truncate">
                                {tab.customerName || `${t('session.session', 'Session')} ${tabs.findIndex(t => t.id === tab.id) + 1}`}
                            </div>
                            {tab.customerPhone ? (
                                <div className={`text-xs truncate ${activeTabId === tab.id ? 'text-gray-600' : 'text-gray-500'}`}>
                                    {tab.customerPhone}
                                </div>
                            ) : (
                                <div className="text-xs text-gray-400 truncate">{t('session.noPhone', 'No phone')}</div>
                            )}
                        </div>

                        {tab.cartItemsCount && tab.cartItemsCount > 0 && (
                            <span className={`cart-badge ml-auto mr-2 flex items-center justify-center w-5 h-5 text-xs font-semibold rounded-full ${activeTabId === tab.id ? 'bg-[var(--color-green-600)] text-white' : 'bg-gray-700 text-white'}`}>
                                {tab.cartItemsCount}
                            </span>
                        )}

                        {tabs.length > 1 && (
                            <button
                                className={`close-tab ml-auto flex-shrink-0 p-0.5 rounded-full transition-colors duration-150 ease-in-out ${activeTabId === tab.id ? 'text-gray-500 hover:text-gray-700 hover:bg-gray-200' : 'text-gray-400 hover:text-gray-600 hover:bg-gray-300/70'}`}
                                onClick={(e) => {
                                    e.stopPropagation();
                                    handleConfirmDelete(tab.id);
                                }}
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                                </svg>
                            </button>
                        )}
                    </div>
                ))}

                <button
                    className="new-tab-button flex items-center space-x-2 py-4 px-4 bg-[var(--color-green-600)] hover:brightness-90 text-white rounded-t-md shadow-sm transition-colors duration-150 ease-in-out ml-2 flex-shrink-0"
                    onClick={handleCreateGuestSession}
                >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                        <path strokeLinecap="round" strokeLinejoin="round" d="M12 4v16m8-8H4" />
                    </svg>
                    <span className="text-sm font-medium">{t('session.addNew', 'Add New Session')}</span>
                </button>
            </div>

            {/* Context Menu */}
            {contextMenuVisible && contextMenuTabId && (
                <div
                    className="context-menu fixed bg-white shadow-lg rounded-md py-1 z-[60] w-48 border border-gray-200"
                    style={{ top: `${contextMenuPosition.y}px`, left: `${contextMenuPosition.x}px` }}
                >
                    <button
                        className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-emerald-50"
                        onClick={() => handleEditTab(contextMenuTabId)}
                    >
                        {t('session.edit', 'Edit Session')}
                    </button>
                    <button
                        className="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                        onClick={() => handleConfirmDelete(contextMenuTabId)}
                    >
                        {t('session.delete', 'Delete Session')}
                    </button>
                </div>
            )}

            {/* Session Modal */}
            <SessionModal
                isOpen={isModalOpen}
                onClose={() => {
                    setIsModalOpen(false);
                    setEditingTabId(null);
                }}
                onSave={editingTabId ? handleUpdateSession : handleSaveNewSession}
                initialData={getInitialData()}
            />

            {/* Confirmation Dialog */}
            <ConfirmationDialog
                isOpen={isConfirmDialogOpen}
                onClose={() => setIsConfirmDialogOpen(false)}
                onConfirm={executeDelete}
                title={t('session.confirmDelete', 'Delete Session')}
                message={t('session.confirmDeleteMessage', 'Are you sure you want to delete this session? This action cannot be undone.')}
            />
        </div>
    );
} 
"use client";

import React, { useState, useEffect, useCallback, memo, useMemo } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useSession } from '../context/SessionContext';
import { SKU, SKUVariant, isParentSKU, isChildSKU, isOrphanChild } from '../types/sku';
import { LocalizedName } from '../types/common';
import { useTranslation } from 'react-i18next';
import { useSessionCheck } from '../hooks/useSessionCheck';
import { SessionModal } from './Header/SessionManager';
import { clarityUtils } from './common/MicrosoftClarity';

interface SkuCardProps {
    sku: SKU;
}

// Custom hook for SKU cart management
const useSkuCart = (sku: SKU, selectedVariant: SKUVariant | null) => {
    const [quantity, setQuantity] = useState(0);
    const [isLoading, setIsLoading] = useState(false); // Per-SKU loading state

    const {
        getActiveSessionCart,
        addItemToCartMutation,
        updateCartItemQuantityMutation,
    } = useSession();

    // Get cart items directly - getActiveSessionCart is already memoized in SessionContext
    const cart = getActiveSessionCart();

    // Memoize cart item lookup
    const cartItem = useMemo(() => {
        return cart.find(item =>
            item.skuId === sku.skuId &&
            (selectedVariant ? item.variantSkuId === selectedVariant.skuId : !item.variantSkuId)
        );
    }, [cart, sku.skuId, selectedVariant]);

    // Sync quantity from cart
    useEffect(() => {
        const newQuantity = cartItem ? cartItem.quantity : 0;
        setQuantity(newQuantity);
    }, [cartItem]);

    // Memoized cart operations
    const addToCart = useCallback(async (variantToUse: SKUVariant | null = null) => {
        const cartItem = {
            skuId: sku.skuId,
            quantity: 1,
            variantSkuId: variantToUse?.skuId
        };

        setIsLoading(true);
        try {
            await addItemToCartMutation.mutateAsync(cartItem);
        } finally {
            setIsLoading(false);
        }
    }, [sku.skuId, addItemToCartMutation]);

    const updateQuantity = useCallback(async (newQuantity: number, variantToUse: SKUVariant | null = null) => {
        setIsLoading(true);
        try {
            await updateCartItemQuantityMutation.mutateAsync({
                productId: sku.skuId,
                variantId: variantToUse?.skuId,
                newQuantity
            });
        } finally {
            setIsLoading(false);
        }
    }, [sku.skuId, updateCartItemQuantityMutation]);

    return {
        quantity,
        isLoading,
        addToCart,
        updateQuantity
    };
};

// Custom hook for variant management
const useVariantSelection = (sku: SKU) => {
    const [selectedVariant, setSelectedVariant] = useState<SKUVariant | null>(() => {
        return sku.variants?.length ? sku.variants[0] : null;
    });
    const [showVariantsModal, setShowVariantsModal] = useState(false);

    const hasMultipleVariants = useMemo(() =>
        sku.variants && sku.variants.length > 1,
        [sku.variants]
    );

    const currentVariant = useMemo(() =>
        selectedVariant || (sku.variants?.length === 1 ? sku.variants[0] : null),
        [selectedVariant, sku.variants]
    );

    return {
        selectedVariant,
        setSelectedVariant,
        showVariantsModal,
        setShowVariantsModal,
        hasMultipleVariants,
        currentVariant
    };
};

// Custom hook for price calculations
const usePriceCalculation = (sku: SKU, selectedVariant: SKUVariant | null) => {
    const { t } = useTranslation();

    return useMemo(() => {
        const currentPrice = selectedVariant ? selectedVariant.sellingPrice : sku.sellingPrice;
        const currentMrp = selectedVariant ? selectedVariant.mrp : sku.mrp;
        const discountValue = currentMrp! - currentPrice!;

        const discountText = discountValue > 0
            ? `${Math.round((discountValue / currentMrp!) * 100)}% ${t('product.card.off', 'OFF')}`
            : null;

        return {
            currentPrice,
            currentMrp,
            discountValue,
            discountText
        };
    }, [selectedVariant, sku.sellingPrice, sku.mrp, t]);
};

// Memoized Discount Badge Component
const DiscountBadge = memo(function DiscountBadge({ discountText }: { discountText: string | null }) {
    if (!discountText) return null;

    return (
        <div className="absolute top-0 left-0 bg-blue-500 text-white text-xs font-bold px-2 py-1 rounded-br-lg">
            {discountText}
        </div>
    );
});

// Memoized Product Image Component with fallback
const ProductImage = memo(function ProductImage({
    sku,
    skuDisplayName,
    discountText
}: {
    sku: SKU;
    skuDisplayName: string;
    discountText: string | null;
}) {
    const [imageError, setImageError] = useState(false);

    const handleImageError = useCallback(() => {
        setImageError(true);
    }, []);

    // Inline SVG placeholder
    const PlaceholderSVG = () => (
        <div className="w-full h-full flex items-center justify-center bg-gray-100 rounded-lg">
            <svg
                width="48"
                height="48"
                viewBox="0 0 24 24"
                fill="none"
                className="text-gray-400"
            >
                <path
                    d="M21 19V5C21 3.9 20.1 3 19 3H5C3.9 3 3 3.9 3 5V19C3 20.1 3.9 21 5 21H19C20.1 21 21 20.1 21 19ZM8.5 13.5L11 16.51L14.5 12L19 18H5L8.5 13.5Z"
                    fill="currentColor"
                />
            </svg>
        </div>
    );

    return (
        <div className="relative w-full aspect-square bg-white group-hover:opacity-90 transition-opacity duration-200 p-2">
            {imageError || !sku.imageUrl ? (
                <PlaceholderSVG />
            ) : (
                <Image
                    src={sku.imageUrl}
                    alt={skuDisplayName}
                    fill
                    className="object-cover rounded-lg p-2"
                    sizes="(max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw"
                    onError={handleImageError}
                />
            )}
            <DiscountBadge discountText={discountText} />
        </div>
    );
});

// Memoized Variant Selector Component
const VariantSelector = memo(function VariantSelector({
    sku,
    displayUnitName,
    hasMultipleVariants,
    isOrphan,
    onVariantClick
}: {
    sku: SKU;
    displayUnitName: string | null;
    hasMultipleVariants: boolean;
    isOrphan: boolean;
    onVariantClick: (e: React.MouseEvent) => void;
}) {
    const { t, i18n } = useTranslation();
    const currentLanguage = i18n.language as keyof LocalizedName;

    // For orphan child SKUs, show variantName if available but without click functionality
    if (isOrphan && sku.variantName) {
        const variantDisplayName = sku.variantName?.[currentLanguage] || sku.variantName?.en;

        if (variantDisplayName) {
            return (
                <div className="min-h-[10px] mb-2">
                    <div className="text-xs text-gray-600 line-clamp-1" title={variantDisplayName}>
                        {variantDisplayName}
                    </div>
                </div>
            );
        }
    }

    // For parent SKUs with variants
    if (!sku.variants?.length) return null;

    return (
        <div className="min-h-[10px] mb-2">
            <div
                className={`text-xs text-gray-600 line-clamp-1 ${hasMultipleVariants ? 'cursor-pointer hover:text-primary' : ''}`}
                onClick={hasMultipleVariants ? onVariantClick : undefined}
                title={hasMultipleVariants ? t('product.card.selectVariantTooltip', 'Select variant') : (displayUnitName || '')}
            >
                {displayUnitName || t('product.card.defaultUnit', 'Unit')}
                {hasMultipleVariants && <span className="ml-1">&#9662;</span>}
            </div>
        </div>
    );
});

// Memoized Price Display Component
const PriceDisplay = memo(function PriceDisplay({
    currentPrice,
    currentMrp
}: {
    currentPrice: number;
    currentMrp: number;
}) {
    return (
        <div className="flex flex-col">
            <span className="text-gray-900 font-semibold text-sm">₹{currentPrice}</span>
            {currentMrp && currentPrice && currentMrp > currentPrice && (
                <span className="text-gray-400 text-xs line-through">₹{currentMrp}</span>
            )}
        </div>
    );
});

// Memoized Add Button Component
const AddButton = memo(function AddButton({
    onAdd,
    isLoading
}: {
    onAdd: () => void;
    isLoading: boolean;
}) {
    const { t } = useTranslation();

    return (
        <button
            onClick={onAdd}
            className="bg-green-50 hover:bg-green-100 text-green-600 border border-green-600 font-semibold py-1.5 px-3 rounded-md transition-colors duration-200 text-xs min-w-[60px]"
            disabled={isLoading}
        >
            {isLoading ? t('product.card.adding', 'Adding...') : t('product.card.add', 'ADD')}
        </button>
    );
});

// Memoized Quantity Controls Component
const QuantityControls = memo(function QuantityControls({
    quantity,
    onDecrease,
    onIncrease,
    isLoading
}: {
    quantity: number;
    onDecrease: () => void;
    onIncrease: () => void;
    isLoading: boolean;
}) {
    const { t } = useTranslation();

    return (
        <div className="flex items-center bg-green-600 text-white rounded-md h-7 min-w-[66px]">
            <button
                onClick={onDecrease}
                className="w-6 h-7 flex items-center justify-center text-sm hover:bg-green-700 rounded-l-md transition-colors duration-200"
                aria-label={t('product.card.decreaseQuantity', "Decrease quantity")}
                disabled={isLoading}
            >
                -
            </button>
            <span className="font-medium text-xs px-1 min-w-[20px] text-center">{quantity}</span>
            <button
                onClick={onIncrease}
                className="w-6 h-7 flex items-center justify-center text-sm hover:bg-green-700 rounded-r-md transition-colors duration-200"
                aria-label={t('product.card.increaseQuantity', "Increase quantity")}
                disabled={isLoading}
            >
                +
            </button>
        </div>
    );
});

// Memoized Variant Selection Modal Component
const VariantSelectionModal = memo(function VariantSelectionModal({
    sku,
    selectedVariant,
    showModal,
    onClose,
    onSelectVariant
}: {
    sku: SKU;
    selectedVariant: SKUVariant | null;
    showModal: boolean;
    onClose: () => void;
    onSelectVariant: (variant: SKUVariant) => void;
}) {
    const { t, i18n } = useTranslation();
    const currentLanguage = i18n.language as keyof LocalizedName;

    const handleVariantSelect = useCallback((variant: SKUVariant) => {
        onSelectVariant(variant);
        onClose();
    }, [onSelectVariant, onClose]);

    if (!showModal || !sku.variants || sku.variants.length <= 1) return null;

    return (
        <div className="fixed inset-0 bg-black/50 flex items-end sm:items-center justify-center z-50 p-4">
            <div className="bg-white w-full max-w-sm rounded-2xl overflow-hidden shadow-xl">
                <div className="p-4 border-b flex justify-between items-center">
                    <h4 className="text-lg font-semibold text-gray-800">
                        {t('product.card.selectVariantTitle', 'Select Variant')}
                    </h4>
                    <button
                        onClick={onClose}
                        className="w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors duration-200"
                        aria-label={t('common.close', 'Close')}
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" className="w-5 h-5 text-gray-500">
                            <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div className="p-4 max-h-[60vh] overflow-y-auto">
                    {sku.variants.map(variant => {
                        const variantName = variant.variantName?.[currentLanguage] || variant.variantName?.en || variant.name?.[currentLanguage] || variant.name?.en || t('common.unnamedVariant', 'Unnamed Variant');
                        const isSelected = selectedVariant?.skuId === variant.skuId;

                        return (
                            <button
                                key={variant.skuId}
                                onClick={() => handleVariantSelect(variant)}
                                className={`w-full mb-3 p-4 border rounded-xl transition-all duration-200 text-left ${isSelected
                                    ? 'bg-green-50 border-green-500 shadow-md'
                                    : 'bg-white hover:bg-gray-50 hover:border-gray-300'
                                    }`}
                            >
                                <div className="flex justify-between items-center">
                                    <span className="font-medium text-gray-800">{variantName}</span>
                                    <span className="text-primary font-semibold">₹{variant.sellingPrice}</span>
                                </div>
                                {variant.mrp > variant.sellingPrice && (
                                    <p className="text-xs text-gray-400 line-through">MRP: ₹{variant.mrp}</p>
                                )}
                                {isSelected && (
                                    <p className="text-xs text-green-600 mt-1 font-semibold">
                                        {t('product.card.selected', 'Selected')}
                                    </p>
                                )}
                            </button>
                        );
                    })}
                </div>
                <div className="p-4 border-t">
                    <button
                        onClick={onClose}
                        className="w-full bg-primary text-white font-semibold py-2.5 px-4 rounded-lg hover:bg-primary-dark transition-colors duration-200"
                    >
                        {t('common.done', 'Done')}
                    </button>
                </div>
            </div>
        </div>
    );
});

// Main SkuCard Component
const SkuCard = memo(function SkuCard({ sku }: SkuCardProps) {
    const { t, i18n } = useTranslation();
    const currentLanguage = i18n.language as keyof LocalizedName;

    // Check if this is an orphan child SKU
    const isOrphan = isOrphanChild(sku);

    // Filter out inactive variants for parent SKUs
    const activeVariants = useMemo(() => {
        if (!sku.variants) return [];
        return sku.variants.filter(variant => variant.isActive === 1);
    }, [sku.variants]);

    // Use activeVariants instead of sku.variants for parent SKUs
    const variantsToUse = isParentSKU(sku) ? activeVariants : sku.variants;

    // Create a modified SKU object with filtered variants for parent SKUs
    const processedSku = useMemo(() => {
        if (isParentSKU(sku)) {
            return { ...sku, variants: activeVariants };
        }
        return sku;
    }, [sku, activeVariants]);

    // Custom hooks (using processedSku to ensure they work with active variants only)
    const {
        selectedVariant,
        setSelectedVariant,
        showVariantsModal,
        setShowVariantsModal,
        hasMultipleVariants,
        currentVariant
    } = useVariantSelection(processedSku);

    const {
        quantity,
        isLoading,
        addToCart,
        updateQuantity
    } = useSkuCart(sku, selectedVariant);

    const { currentPrice, currentMrp, discountText } = usePriceCalculation(sku, selectedVariant);

    const {
        checkSessionAndExecute,
        showSessionModal,
        setShowSessionModal,
        handleSessionCreated
    } = useSessionCheck();

    // Get session context functions (moved outside of useCallback)
    const { getActiveSessionCart } = useSession();

    // Memoized display values
    const skuDisplayName = useMemo(() =>
        sku.name?.[currentLanguage] || sku.name?.en || t('common.unnamedProduct', 'Unnamed Product'),
        [sku.name, currentLanguage, t]
    );

    const displayUnitName = useMemo(() => {
        // For parent SKUs, use variantName from selected/first variant (from active variants only)
        const selectedVariantName = selectedVariant?.variantName?.[currentLanguage] || selectedVariant?.variantName?.en;
        const singleVariantName = variantsToUse?.length === 1
            ? (variantsToUse[0].variantName?.[currentLanguage] || variantsToUse[0].variantName?.en)
            : null;
        return selectedVariantName || singleVariantName || null;
    }, [selectedVariant, variantsToUse, currentLanguage]);

    const productPageUrl = useMemo(() => `/product/${sku.skuId}`, [sku.skuId]);

    // Event handlers
    const handleInitialAddToCart = useCallback(() => {
        if (hasMultipleVariants) {
            setShowVariantsModal(true);
            return;
        }

        checkSessionAndExecute(async () => {
            clarityUtils.trackAddToCart(sku.skuId, skuDisplayName, 1);
            await addToCart(currentVariant);
        });
    }, [hasMultipleVariants, setShowVariantsModal, checkSessionAndExecute, sku.skuId, skuDisplayName, addToCart, currentVariant]);

    const handleQuantityChange = useCallback((increment: boolean) => {
        const newQuantity = increment ? quantity + 1 : quantity - 1;
        if (newQuantity >= 0) {
            updateQuantity(newQuantity, currentVariant);
        }
    }, [quantity, updateQuantity, currentVariant]);

    const handleVariantClick = useCallback((e: React.MouseEvent) => {
        if (hasMultipleVariants) {
            e.preventDefault();
            e.stopPropagation();
            setShowVariantsModal(true);
        }
    }, [hasMultipleVariants, setShowVariantsModal]);

    const handleSelectVariant = useCallback((variant: SKUVariant) => {
        const cart = getActiveSessionCart();
        const itemInCart = cart.find((item: any) =>
            item.skuId === sku.skuId && item.variantSkuId === variant.skuId
        );

        setSelectedVariant(variant);

        if (!itemInCart) {
            checkSessionAndExecute(async () => {
                const variantName = variant.variantName?.[currentLanguage] || variant.variantName?.en || variant.name?.[currentLanguage] || variant.name?.en || t('common.unnamedVariant', 'Unnamed Variant');
                clarityUtils.trackAddToCart(sku.skuId, `${skuDisplayName} - ${variantName}`, 1);
                await addToCart(variant);
            });
        }
    }, [getActiveSessionCart, setSelectedVariant, sku.skuId, checkSessionAndExecute, currentLanguage, t, skuDisplayName, addToCart]);

    // Determine if we should hide this SKU after all hooks are called
    const shouldHide = useMemo(() => {
        // 1. Hide inactive SKUs (customer pages should not show inactive SKUs)
        if (sku.isActive === 0) return true;

        // 2. Hide parent SKUs without children
        if (isParentSKU(sku) && (!sku.variants || sku.variants.length === 0)) return true;

        // 3. Hide parent SKUs if all variants are inactive
        if (isParentSKU(sku) && activeVariants.length === 0) return true;

        return false;
    }, [sku, activeVariants]);

    // Hide SKUs that should not be displayed
    if (shouldHide) {
        return null;
    }

    return (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow duration-200 flex flex-col h-full">
            <Link href={productPageUrl} className="block cursor-pointer group">
                <div className="flex flex-col">
                    <ProductImage
                        sku={sku}
                        skuDisplayName={skuDisplayName}
                        discountText={discountText}
                    />

                    <div className="p-2 flex-1 flex flex-col">
                        <h3 className="font-medium text-gray-800 text-xs leading-tight line-clamp-2 mb-1 min-h-[34px] flex items-start" title={skuDisplayName}>
                            {skuDisplayName}
                        </h3>

                        <VariantSelector
                            sku={processedSku}
                            displayUnitName={displayUnitName}
                            hasMultipleVariants={hasMultipleVariants || false}
                            isOrphan={isOrphan}
                            onVariantClick={handleVariantClick}
                        />
                    </div>
                </div>
            </Link>

            <div className="p-2 pt-0 flex items-center justify-between">
                <PriceDisplay currentPrice={currentPrice!} currentMrp={currentMrp!} />

                <div className="flex-shrink-0">
                    {quantity === 0 ? (
                        <AddButton onAdd={handleInitialAddToCart} isLoading={isLoading} />
                    ) : (
                        <QuantityControls
                            quantity={quantity}
                            onDecrease={() => handleQuantityChange(false)}
                            onIncrease={() => handleQuantityChange(true)}
                            isLoading={isLoading}
                        />
                    )}
                </div>
            </div>

            <VariantSelectionModal
                sku={processedSku}
                selectedVariant={selectedVariant}
                showModal={showVariantsModal}
                onClose={() => setShowVariantsModal(false)}
                onSelectVariant={handleSelectVariant}
            />

            <SessionModal
                isOpen={showSessionModal}
                onClose={() => setShowSessionModal(false)}
                onSave={handleSessionCreated}
                initialData={{ customerName: '', customerPhone: '', location: undefined }}
            />
        </div>
    );
});

export default SkuCard;
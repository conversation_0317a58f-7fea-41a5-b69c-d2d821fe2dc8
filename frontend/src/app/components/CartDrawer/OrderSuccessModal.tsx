"use client";

import React, { useEffect, useState } from 'react';
import Modal from 'react-modal';
import { useTranslation } from 'react-i18next';
import { CheckCircleIcon, PhoneIcon } from '@heroicons/react/24/outline';
import Confetti from 'react-confetti';

interface OrderSuccessModalProps {
    isOpen: boolean;
    onClose: () => void;
    orderDetails?: {
        total: number;
        customerName: string;
    };
}

const OrderSuccessModal: React.FC<OrderSuccessModalProps> = ({
    isOpen,
    onClose,
    orderDetails
}) => {
    const { t } = useTranslation();
    const [showConfetti, setShowConfetti] = useState(false);
    const [windowDimensions, setWindowDimensions] = useState({ width: 0, height: 0 });

    // Get window dimensions for confetti
    useEffect(() => {
        const updateWindowDimensions = () => {
            setWindowDimensions({
                width: window.innerWidth,
                height: window.innerHeight
            });
        };

        if (typeof window !== 'undefined') {
            updateWindowDimensions();
            window.addEventListener('resize', updateWindowDimensions);
            return () => window.removeEventListener('resize', updateWindowDimensions);
        }
    }, []);

    // Show confetti when modal opens
    useEffect(() => {
        if (isOpen) {
            setShowConfetti(true);
            // Stop confetti after 5 seconds
            const timer = setTimeout(() => {
                setShowConfetti(false);
            }, 5000);
            return () => clearTimeout(timer);
        } else {
            setShowConfetti(false);
        }
    }, [isOpen]);

    // Auto-close modal after 10 seconds
    useEffect(() => {
        if (isOpen) {
            const timer = setTimeout(() => {
                onClose();
            }, 10000);
            return () => clearTimeout(timer);
        }
    }, [isOpen, onClose]);

    const customModalStyles = {
        overlay: {
            backgroundColor: 'rgba(0, 0, 0, 0.6)',
            backdropFilter: 'blur(4px)',
            zIndex: 200, // Higher than checkout modal
        },
        content: {
            top: '50%',
            left: '50%',
            right: 'auto',
            bottom: 'auto',
            marginRight: '-50%',
            transform: 'translate(-50%, -50%)',
            border: 'none',
            background: 'transparent',
            padding: '0',
            borderRadius: '0',
            width: '100%',
            maxWidth: '28rem',
            overflow: 'visible'
        },
    };

    if (!isOpen) return null;

    return (
        <>
            {/* Confetti Animation */}
            {showConfetti && (
                <div style={{ position: 'fixed', top: 0, left: 0, width: '100%', height: '100%', pointerEvents: 'none', zIndex: 9999 }}>
                    <Confetti
                        width={windowDimensions.width}
                        height={windowDimensions.height}
                        recycle={false}
                        numberOfPieces={200}
                        gravity={0.3}
                        colors={['#10B981', '#059669', '#047857', '#065F46', '#064E3B']}
                    />
                </div>
            )}

            <Modal
                isOpen={isOpen}
                onRequestClose={onClose}
                style={customModalStyles}
                contentLabel={t('order.success.title', 'Order Successful')}
                shouldCloseOnOverlayClick={true}
            >
                <div className="bg-white p-6 sm:p-8 rounded-lg shadow-xl w-full relative text-center">
                    {/* Success Icon */}
                    <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-4">
                        <CheckCircleIcon className="h-10 w-10 text-green-600" />
                    </div>

                    {/* Success Message */}
                    <h2 className="text-2xl font-bold text-gray-900 mb-2">
                        {t('order.success.title', 'Order Successful!')}
                    </h2>

                    <p className="text-gray-600 mb-4">
                        {t('order.success.message', 'Thank you for your order.')}
                    </p>

                    {/* Call confirmation message with info box + prominent text */}
                    <div className="bg-blue-50 border-l-4 border-blue-400 rounded-lg p-4 mb-6">
                        <div className="flex items-center justify-center gap-3">
                            <PhoneIcon className="h-6 w-6 text-blue-600 flex-shrink-0" />
                            <p className="text-blue-900 font-semibold text-lg text-center">
                                {t('order.success.callConfirmation', 'We will call you for order confirmation')}
                            </p>
                        </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex flex-col sm:flex-row gap-3">
                        <button
                            onClick={onClose}
                            className="flex-1 bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-4 rounded-md transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
                        >
                            {t('orderSuccess.continue', 'Continue Shopping')}
                        </button>

                    </div>

                    {/* Auto-close indicator */}
                    <p className="text-xs text-gray-500 mt-4">
                        {t('orderSuccess.autoClose', 'This dialog will close automatically in a few seconds')}
                    </p>
                </div>
            </Modal>
        </>
    );
};

export default OrderSuccessModal; 
import React from 'react';
import { useTranslation } from 'react-i18next';

const CancellationPolicySection: React.FC = React.memo(() => {
    const { t } = useTranslation();

    return (
        <div className="bg-white p-4 rounded-lg shadow-sm">
            <h3 className="text-md font-semibold text-gray-800 mb-2">{t('cartDrawer.cancellationPolicy', 'Cancellation Policy')}</h3>
            <p className="text-xs text-gray-600">
                {t('cartDrawer.cancellationText', 'Orders cannot be cancelled once packed for delivery. In case of unexpected delays, a refund will be provided, if applicable.')}
            </p>
        </div>
    );
});

CancellationPolicySection.displayName = 'CancellationPolicySection';
export default CancellationPolicySection; 
import React from 'react';
import { useTranslation } from 'react-i18next';

interface ProceedToCheckoutButtonProps {
    grandTotal: number;
    isUserLoggedIn: boolean; // Renamed from isLoggedIn for clarity within this component
    isLoading?: boolean;
    onLogin: () => void;
    onCloseCart: () => void; // Renamed from onClose for clarity
    onCheckout: () => void; // New checkout handler
}

const ProceedToCheckoutButton: React.FC<ProceedToCheckoutButtonProps> = React.memo(({
    grandTotal,
    isUserLoggedIn,
    isLoading,
    onLogin,
    onCloseCart,
    onCheckout,
}) => {
    const { t } = useTranslation();

    const handleCheckout = () => {
        if (isUserLoggedIn) {
            onCheckout(); // Trigger checkout modal
        } else {
            onLogin(); // Trigger login modal
            onCloseCart(); // Close cart after triggering login
        }
    };

    return (
        <div className="p-4 bg-white border-t border-gray-200">
            <button
                onClick={handleCheckout}
                className="w-full bg-[var(--color-green-600)] hover:brightness-90 text-white rounded-lg py-3 px-4 flex items-center justify-between text-sm font-medium transition-all"
                disabled={isLoading}
            >
                <div className="flex flex-col items-start">
                    <span className="text-lg font-bold">₹{grandTotal}</span>
                    <span className="text-xs opacity-90">{t('cartDrawer.total', 'TOTAL')}</span>
                </div>
                <div className="flex items-center">
                    <span>{isUserLoggedIn ? t('cartDrawer.checkout', 'Checkout') : t('cartDrawer.loginToProceed', 'Login to Proceed')}</span>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                        <path strokeLinecap="round" strokeLinejoin="round" d="M9 5l7 7-7 7" />
                    </svg>
                </div>
            </button>
        </div>
    );
});

ProceedToCheckoutButton.displayName = 'ProceedToCheckoutButton';
export default ProceedToCheckoutButton; 
import React, { useMemo } from 'react';
import Image from 'next/image';
import { useTranslation } from 'react-i18next';
import { DetailedCartItem } from '@/app/types/session'; // Adjusted import path
import { LocalizedName } from '@/app/types/common';


interface CartItemCardProps {
    item: DetailedCartItem;
    onQuantityChange: (item: DetailedCartItem, newQuantity: number) => void;
    // removeItemFromCart: (args: { productId: number; variantId?: number }) => void; // Add if needed later
    isUpdatingQuantity?: boolean;
    isLoading?: boolean;
}

const CartItemCard: React.FC<CartItemCardProps> = React.memo(({
    item,
    onQuantityChange,
    isUpdatingQuantity,
    isLoading,
}) => {
    const { t, i18n } = useTranslation();
    const currentLanguage = useMemo(() => i18n.language as keyof LocalizedName, [i18n.language]);

    const name = useMemo(() => {
        if (item.name[currentLanguage]) {
            return item.name[currentLanguage];
        }
        return item.name.en;
    }, [item.name, currentLanguage]);

    const variantName = useMemo(() => {
        if (item.variantName?.[currentLanguage]) {
            return item.variantName[currentLanguage];
        }
        return item.variantName?.en;
    }, [item.variantName, currentLanguage]);




    return (
        <div className="p-4 flex space-x-4">
            <div className="w-16 h-16 relative flex-shrink-0">
                <Image src={item.imageUrl} alt={name} fill className="object-cover rounded-md" />
            </div>
            <div className="flex-grow min-w-0">
                <p className="text-sm font-medium text-gray-800 truncate">{name}</p>
                {variantName && <p className="text-xs text-gray-500">{variantName}</p>}
                <div className="flex items-baseline mt-1">
                    <p className="text-sm font-semibold text-gray-700 mr-2">₹{item.pricePerUnit}</p>
                    {item.mrpPerUnit > item.pricePerUnit && (
                        <p className="text-xs text-gray-400 line-through">₹{item.mrpPerUnit}</p>
                    )}
                </div>
            </div>
            <div className="flex-shrink-0 w-28">
                <div className="flex items-center justify-end bg-emerald-600 text-white rounded-lg h-9">
                    <button
                        onClick={() => onQuantityChange(item, item.quantity - 1)}
                        className="w-9 h-9 flex items-center justify-center text-lg hover:bg-emerald-700 rounded-l-lg transition-colors"
                        aria-label={t('cartDrawer.decreaseQuantity', 'Decrease quantity')}
                        disabled={isUpdatingQuantity || isLoading || item.quantity <= 0}
                    >
                        -
                    </button>
                    <span className="font-medium text-sm px-2">{item.quantity}</span>
                    <button
                        onClick={() => onQuantityChange(item, item.quantity + 1)}
                        className="w-9 h-9 flex items-center justify-center text-lg hover:bg-emerald-700 rounded-r-lg transition-colors"
                        aria-label={t('cartDrawer.increaseQuantity', 'Increase quantity')}
                        disabled={isUpdatingQuantity || isLoading}
                    >
                        +
                    </button>
                </div>
            </div>
        </div>
    );
});

CartItemCard.displayName = 'CartItemCard'; // Good practice for React.memo components

export default CartItemCard; 
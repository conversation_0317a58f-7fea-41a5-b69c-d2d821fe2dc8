import React from 'react';
import { useTranslation } from 'react-i18next';

interface DeliveryInfoProps {
    totalCartQuantity: number;
}

const DeliveryInfo: React.FC<DeliveryInfoProps> = React.memo(({ totalCartQuantity }) => {
    const { t } = useTranslation();

    return (
        <div className="bg-white p-4 rounded-lg shadow-sm">
            <div className="flex items-center">
                <div className="bg-gray-100 p-2 rounded-full mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                        <path strokeLinecap="round" strokeLinejoin="round" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <div>
                    <p className="font-semibold text-gray-800">{t('cart.deliveryTime', 'Delivery in 6 minutes')}</p>
                    <p className="text-xs text-gray-500">
                        {t('cart.shipmentOf', 'Shipment of')} {totalCartQuantity} {totalCartQuantity === 1 ? t('cart.item', 'item') : t('cart.items', 'items')}
                    </p>
                </div>
            </div>
        </div>
    );
});

DeliveryInfo.displayName = 'DeliveryInfo';
export default DeliveryInfo; 
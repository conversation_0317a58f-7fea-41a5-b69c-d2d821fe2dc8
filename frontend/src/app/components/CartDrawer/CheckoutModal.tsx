"use client";

import React, { useState, useEffect, useCallback, useRef } from 'react';
import Modal from 'react-modal';
import { useTranslation } from 'react-i18next';
import MapSelector, { defaultLocation } from '../MapSelector';
import { logger } from '@/lib/logger';

interface CheckoutData {
    customerName: string;
    customerPhone: string;
    landmark?: string;
    location: {
        lat: number;
        lng: number;
    };
}

interface CheckoutModalProps {
    isOpen: boolean;
    onClose: () => void;
    onCheckout: (data: CheckoutData) => Promise<void>;
    initialData?: Partial<CheckoutData>;
    grandTotal: number;
    isProcessing?: boolean;
    isCustomerFacingMode?: boolean;
    onCustomerDataChange?: (data: Partial<CheckoutData>) => void;
}

// Custom styles for the Modal - Mobile optimized
const customModalStyles = {
    overlay: {
        backgroundColor: 'rgba(0, 0, 0, 0.6)',
        backdropFilter: 'blur(4px)',
        zIndex: 100,
        padding: '1rem',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'fixed' as const,
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
    },
    content: {
        position: 'static' as const,
        top: 'auto',
        left: 'auto',
        right: 'auto',
        bottom: 'auto',
        margin: '0 auto',
        transform: 'none',
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        backdropFilter: 'blur(4px)',
        borderRadius: '0.75rem',
        padding: '0',
        width: '100%',
        maxWidth: '500px',
        maxHeight: '90vh',
        display: 'flex',
        flexDirection: 'column' as const,
        overflow: 'hidden',
        border: 'none',
        outline: 'none',
        boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
    }
};

// Custom styles for Confirmation Modal - Higher z-index to appear on top
const confirmationModalStyles = {
    overlay: {
        backgroundColor: 'rgba(0, 0, 0, 0.75)',
        backdropFilter: 'blur(6px)',
        zIndex: 200, // Higher than checkout modal
        padding: '1rem',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'fixed' as const,
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
    },
    content: {
        position: 'static' as const,
        top: 'auto',
        left: 'auto',
        right: 'auto',
        bottom: 'auto',
        margin: '0 auto',
        transform: 'none',
        backgroundColor: 'white',
        borderRadius: '0.5rem',
        padding: '1.25rem',
        width: '100%',
        maxWidth: '28rem',
        border: '1px solid #e5e7eb',
        outline: 'none',
        boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.4)',
    }
};

const CheckoutModal: React.FC<CheckoutModalProps> = ({
    isOpen,
    onClose,
    onCheckout,
    initialData = { customerName: '', customerPhone: '', landmark: '', location: undefined },
    grandTotal,
    isProcessing = false,
    isCustomerFacingMode = true,
    onCustomerDataChange
}) => {
    const { t } = useTranslation();

    const [customerName, setCustomerName] = useState('');
    const [customerPhone, setCustomerPhone] = useState('');
    const [landmark, setLandmark] = useState(initialData.landmark || '');
    const [location, setLocation] = useState(initialData.location);
    const [nameError, setNameError] = useState('');
    const [phoneError, setPhoneError] = useState('');
    const [checkoutError, setCheckoutError] = useState('');
    const [showConfirmDialog, setShowConfirmDialog] = useState(false);

    // Debounce timer ref for auto-sync
    const syncTimeoutRef = useRef<NodeJS.Timeout | null>(null);

    // Reset form when modal opens with new data
    useEffect(() => {
        if (isOpen) {
            setCustomerName(initialData.customerName || '');
            setCustomerPhone(initialData.customerPhone || '');
            setLandmark(initialData.landmark || '');
            setLocation(initialData.location);
            setNameError('');
            setPhoneError('');
            setCheckoutError('');
        }
    }, [isOpen]); // Only reset when modal opens, not when initialData changes

    // Separate effect to update form when initialData changes, but only if fields are empty
    useEffect(() => {
        if (isOpen && initialData) {
            logger.debug('CheckoutModal: Initial data changed', {
                initialData,
                currentValues: { customerName, customerPhone, landmark, location }
            });

            // Only update fields if they are currently empty to avoid clearing user input
            if (initialData.customerName) {
                logger.debug('CheckoutModal: Updating customerName from initialData');
                setCustomerName(initialData.customerName);
            }
            if (initialData.customerPhone) {
                logger.debug('CheckoutModal: Updating customerPhone from initialData');
                setCustomerPhone(initialData.customerPhone);
            }
            if (initialData.landmark) {
                logger.debug('CheckoutModal: Updating landmark from initialData');
                setLandmark(initialData.landmark);
            }
            if (initialData.location) {
                logger.debug('CheckoutModal: Updating location from initialData');
                setLocation(initialData.location);
            }
        }
    }, [isOpen, initialData]);

    const validateForm = (): boolean => {
        let isValid = true;

        // Validate name - mandatory for admin mode (when isCustomerFacingMode is false)
        if (!customerName.trim() && !isCustomerFacingMode) {
            setNameError(t('checkout.errors.nameRequired', 'Customer name is required'));
            isValid = false;
        } else {
            setNameError('');
        }

        // Validate phone
        if (!customerPhone.trim()) {
            setPhoneError(t('checkout.errors.phoneRequired', 'Phone number is required'));
            isValid = false;
        } else if (!/^\d{10}$/.test(customerPhone.replace(/\D/g, ''))) {
            setPhoneError(t('checkout.errors.phoneInvalid', 'Please enter a valid 10-digit phone number'));
            isValid = false;
        } else {
            setPhoneError('');
        }

        // Location is optional for now, but we can add validation if needed
        if (!location) {
            // For now, use default location if none selected
            setLocation(defaultLocation);
        }

        return isValid;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setCheckoutError('');

        if (validateForm()) {
            // Show confirmation dialog instead of directly placing order
            setShowConfirmDialog(true);
        } else {
            logger.warn('CheckoutModal: Form validation failed');
        }
    };

    const handleConfirmOrder = async () => {
        setShowConfirmDialog(false);

        const checkoutData = {
            customerName: customerName.trim(),
            customerPhone: customerPhone.trim(),
            landmark: landmark.trim(),
            location: location || defaultLocation
        };

        try {
            await onCheckout(checkoutData);
        } catch (error) {
            logger.error('CheckoutModal: Error calling onCheckout:', error);
            setCheckoutError(error instanceof Error ? error.message : 'Checkout failed. Please try again.');
        }
    };

    const handleCancelConfirmation = () => {
        setShowConfirmDialog(false);
    };



    const handleLocationSelect = (newLocation: { lat: number; lng: number }) => {
        setLocation(newLocation);
        triggerAutoSync({ location: newLocation }, 'location');
    };

    // Debounced auto-sync function - only sync if the specified field is valid
    const triggerAutoSync = useCallback((updatedData?: Partial<CheckoutData>, changedField?: 'phone' | 'name' | 'location' | 'landmark') => {
        // Clear existing timeout
        if (syncTimeoutRef.current) {
            clearTimeout(syncTimeoutRef.current);
        }

        // Set new timeout for debounced sync
        syncTimeoutRef.current = setTimeout(() => {
            const currentData = {
                customerName,
                customerPhone,
                landmark,
                location,
                ...updatedData // Override with any immediately updated data
            };

            // Validate only the specific field that changed
            let isChangedFieldValid = false;

            if (changedField === 'phone') {
                isChangedFieldValid = !!(currentData.customerPhone && /^\d{10}$/.test(currentData.customerPhone.replace(/\D/g, '')));
            } else if (changedField === 'name') {
                isChangedFieldValid = !!(currentData.customerName && currentData.customerName.trim().length > 0);
            } else if (changedField === 'location') {
                isChangedFieldValid = !!(currentData.location && (currentData.location.lat !== 0 || currentData.location.lng !== 0));
            } else if (changedField === 'landmark') {
                // Landmark is optional, so always considered valid
                isChangedFieldValid = true;
            }

            // Only sync if the changed field is valid
            if (isChangedFieldValid) {
                logger.info('CheckoutModal: Auto-syncing customer data', {
                    changedField,
                    isChangedFieldValid,
                    dataLength: {
                        name: currentData.customerName?.length || 0,
                        phone: currentData.customerPhone?.length || 0
                    }
                });

                // Sync to parent component
                onCustomerDataChange?.(currentData);
            }
        }, 500); // 500ms debounce as approved
    }, [customerName, customerPhone, landmark, location, onCustomerDataChange]);

    // Enhanced phone change handler with auto-sync
    const handlePhoneChangeEnhanced = useCallback((value: string) => {
        const cleanedValue = value.replace(/\D/g, '').slice(0, 10);
        setCustomerPhone(cleanedValue);
        if (cleanedValue.trim()) setPhoneError('');

        // Trigger auto-sync with updated phone
        triggerAutoSync({ customerPhone: cleanedValue }, 'phone');
    }, [triggerAutoSync]);

    // Enhanced name change handler with auto-sync  
    const handleNameChangeEnhanced = useCallback((value: string) => {
        setCustomerName(value);
        if (value.trim()) setNameError('');

        // Trigger auto-sync with updated name
        triggerAutoSync({ customerName: value }, 'name');
    }, [triggerAutoSync]);

    // Enhanced landmark change handler with auto-sync
    const handleLandmarkChangeEnhanced = useCallback((value: string) => {
        logger.info('CheckoutModal: Landmark changed', { newValue: value, prevValue: landmark });
        setLandmark(value);

        // Trigger auto-sync with updated landmark
        triggerAutoSync({ landmark: value }, 'landmark');
    }, [triggerAutoSync, landmark]);

    // Cleanup timeout on unmount
    useEffect(() => {
        return () => {
            if (syncTimeoutRef.current) {
                clearTimeout(syncTimeoutRef.current);
            }
        };
    }, []);

    return (
        <Modal
            isOpen={isOpen}
            onRequestClose={isProcessing ? undefined : onClose}
            style={customModalStyles}
            contentLabel={t('checkout.title', 'Checkout')}
            closeTimeoutMS={200}
            shouldCloseOnOverlayClick={!isProcessing}
        >
            {/* Fixed Header */}
            <div className="flex-shrink-0 p-4 sm:p-6 text-center border-b border-gray-100">
                <div className="h-10 w-10 sm:h-12 sm:w-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <svg className="h-5 w-5 sm:h-6 sm:w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                </div>
                <h3 className="text-lg sm:text-xl font-semibold mb-2">{t('checkout.title', 'Checkout')}</h3>
                <p className="text-sm text-gray-600 mb-3">
                    {t('checkout.subtitle', 'Please confirm your details to complete the order')}
                </p>
                <div className="text-lg sm:text-xl font-bold text-green-600">
                    {t('checkout.total', 'Total: ₹{{total}}', { total: grandTotal })}
                </div>
            </div>

            {/* Scrollable Content */}
            <div className="flex-1 overflow-y-auto p-4 sm:p-6">
                <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6">
                    {/* Phone Number Field */}
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            {t('checkout.customerPhone', 'Phone Number')} <span className="text-red-500">*</span>
                        </label>
                        <div className="flex">
                            <span className="inline-flex items-center px-3 py-2 sm:py-2.5 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm sm:text-base">
                                +91
                            </span>
                            <input
                                type="tel"
                                className={`flex-1 px-3 py-2 sm:py-2.5 border ${phoneError ? 'border-red-500' : 'border-gray-300'} rounded-r-md focus:outline-none focus:ring-2 focus:ring-green-500 text-sm sm:text-base`}
                                value={customerPhone}
                                onChange={(e) => handlePhoneChangeEnhanced(e.target.value)}
                                placeholder={t('checkout.customerPhonePlaceholder', 'Enter 10-digit phone number')}
                                disabled={isProcessing}
                                maxLength={10}
                                autoFocus
                            />
                        </div>
                        {phoneError && <p className="text-red-500 text-xs mt-1">{phoneError}</p>}
                    </div>

                    {/* Customer Name Field */}
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            {t('checkout.customerName', 'Customer Name')}
                            {!isCustomerFacingMode && <span className="text-red-500 ml-1">*</span>}
                        </label>
                        <input
                            type="text"
                            className={`w-full px-3 py-2 sm:py-2.5 border ${nameError ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 text-sm sm:text-base`}
                            value={customerName}
                            onChange={(e) => handleNameChangeEnhanced(e.target.value)}
                            placeholder={t('checkout.customerNamePlaceholder', 'Enter customer name')}
                            disabled={isProcessing}
                        />
                        {nameError && <p className="text-red-500 text-xs mt-1">{nameError}</p>}
                    </div>

                    {/* Landmark Field */}
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            {t('checkout.landmark', 'Landmark')}
                        </label>
                        <input
                            type="text"
                            className="w-full px-3 py-2 sm:py-2.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 text-sm sm:text-base"
                            value={landmark}
                            onChange={(e) => handleLandmarkChangeEnhanced(e.target.value)}
                            placeholder={t('checkout.landmarkPlaceholder', 'Enter landmark (optional)')}
                            disabled={isProcessing}
                        />
                    </div>

                    {/* Location Field - Mobile Optimized */}
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            {t('checkout.location', 'Delivery Location')}
                        </label>
                        <div className="h-48 sm:h-64 rounded-lg overflow-hidden border border-gray-300">
                            <MapSelector
                                initialLocation={location}
                                onLocationSelect={handleLocationSelect}
                            />
                        </div>
                        {location && (
                            <p className="mt-2 text-xs text-gray-500">
                                {t('session.selectedCoordinates', 'Selected coordinates')}: {location.lat.toFixed(4)}, {location.lng.toFixed(4)}
                            </p>
                        )}
                    </div>

                    {/* Error Message */}
                    {checkoutError && (
                        <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                            <p className="text-red-600 text-sm">{checkoutError}</p>
                        </div>
                    )}
                </form>
            </div>

            {/* Fixed Footer */}
            <div className="flex-shrink-0 p-4 sm:p-6 border-t border-gray-100 bg-gray-50">
                <div className="flex flex-col-reverse sm:flex-row sm:justify-end space-y-reverse space-y-3 sm:space-y-0 sm:space-x-3">
                    <button
                        type="button"
                        className="w-full sm:w-auto px-4 py-2.5 sm:py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 text-sm sm:text-base font-medium"
                        onClick={onClose}
                        disabled={isProcessing}
                    >
                        {t('common.cancel', 'Cancel')}
                    </button>
                    <button
                        type="submit"
                        disabled={isProcessing}
                        onClick={handleSubmit}
                        className="w-full sm:w-auto px-6 py-2.5 sm:py-2 bg-green-600 hover:bg-green-700 text-white font-semibold rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-60 disabled:cursor-not-allowed transition-colors duration-150 flex items-center justify-center text-sm sm:text-base"
                    >
                        {isProcessing ? (
                            <>
                                <svg className="animate-spin -ml-1 mr-3 h-4 w-4 sm:h-5 sm:w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                {t('checkout.processing', 'Processing Order...')}
                            </>
                        ) : (
                            t('checkout.placeOrder', 'Place Order - ₹{{total}}', { total: grandTotal })
                        )}
                    </button>
                </div>
            </div>

            {/* Order Confirmation Modal */}
            <Modal
                isOpen={showConfirmDialog}
                onRequestClose={isProcessing ? undefined : handleCancelConfirmation}
                style={confirmationModalStyles}
                contentLabel={t('checkout.confirmOrder.title', 'Confirm Your Order')}
                shouldCloseOnOverlayClick={!isProcessing}
                shouldCloseOnEsc={!isProcessing}
            >
                {/* Header */}
                <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-gray-900">
                        {t('checkout.confirmOrder.title', 'Confirm Your Order')}
                    </h3>
                    <button
                        onClick={handleCancelConfirmation}
                        disabled={isProcessing}
                        className="text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-green-500 rounded disabled:opacity-50"
                    >
                        <span className="sr-only">{t('common.close', 'Close')}</span>
                        <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>

                {/* Content */}
                <div className="mb-6">
                    <p className="text-sm text-gray-600 mb-4">
                        {t('checkout.confirmOrder.message', 'Are you sure you want to place this order?')}
                    </p>

                    {/* Order Summary */}
                    <div className="bg-green-50 border border-green-200 rounded-md p-4">
                        <div className="flex">
                            <div className="flex-shrink-0">
                                <div className="h-8 w-8 bg-green-100 rounded-full flex items-center justify-center">
                                    <svg className="h-5 w-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                                    </svg>
                                </div>
                            </div>
                            <div className="ml-3 flex-1">
                                <div className="space-y-2 text-sm">
                                    <div className="flex justify-between">
                                        <span className="text-green-700 font-medium">{t('checkout.confirmOrder.phone', 'Phone:')}</span>
                                        <span className="text-green-800 font-semibold">+91 {customerPhone}</span>
                                    </div>
                                    {customerName.trim() && (
                                        <div className="flex justify-between">
                                            <span className="text-green-700 font-medium">{t('checkout.confirmOrder.name', 'Name:')}</span>
                                            <span className="text-green-800 font-semibold">{customerName.trim()}</span>
                                        </div>
                                    )}
                                    {landmark.trim() && (
                                        <div className="flex justify-between">
                                            <span className="text-green-700 font-medium">{t('checkout.confirmOrder.landmark', 'Landmark:')}</span>
                                            <span className="text-green-800 font-semibold">{landmark.trim()}</span>
                                        </div>
                                    )}
                                    <div className="flex justify-between">
                                        <span className="text-green-700 font-medium">{t('checkout.confirmOrder.location', 'Location:')}</span>
                                        <span className="text-green-800 font-medium text-xs">
                                            {location ? `${location.lat.toFixed(4)}, ${location.lng.toFixed(4)}` : 'Default'}
                                        </span>
                                    </div>
                                    <div className="flex justify-between pt-2 border-t border-green-300">
                                        <span className="text-green-900 font-bold">{t('checkout.confirmOrder.total', 'Total:')}</span>
                                        <span className="text-green-900 font-bold text-lg">₹{grandTotal}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Actions */}
                <div className="flex justify-end space-x-3">
                    <button
                        onClick={handleCancelConfirmation}
                        disabled={isProcessing}
                        className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 disabled:opacity-50"
                    >
                        {t('common.cancel', 'Cancel')}
                    </button>
                    <button
                        onClick={handleConfirmOrder}
                        disabled={isProcessing}
                        className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-green-500 flex items-center"
                    >
                        {isProcessing ? (
                            <>
                                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                {t('checkout.processing', 'Processing...')}
                            </>
                        ) : (
                            t('checkout.confirmOrder.confirm', 'Yes, Place Order')
                        )}
                    </button>
                </div>
            </Modal>
        </Modal>
    );
};

export default CheckoutModal; 
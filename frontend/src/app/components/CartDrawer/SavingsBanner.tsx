import React from 'react';
import { useTranslation } from 'react-i18next';

interface SavingsBannerProps {
    totalSavings: number;
}

const SavingsBanner: React.FC<SavingsBannerProps> = React.memo(({ totalSavings }) => {
    const { t } = useTranslation();

    if (totalSavings <= 0) {
        return null;
    }

    return (
        <div className="bg-blue-100 text-blue-700 p-3 rounded-lg flex justify-between items-center text-sm">
            <span>{t('cartDrawer.totalSavings', 'Your total savings')}</span>
            <span className="font-semibold">₹{totalSavings}</span>
        </div>
    );
});

SavingsBanner.displayName = 'SavingsBanner';
export default SavingsBanner; 
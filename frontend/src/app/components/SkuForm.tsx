'use client';

import React, { useState, useEffect, useCallback } from 'react';
import Image from 'next/image';
import { PlusIcon, TrashIcon, PhotoIcon } from '@heroicons/react/24/outline';
import { getOrderedCategories } from '@/app/services/categoryService';
import { getSkus } from '@/app/services/skuService';
import { SkuOperationsService } from '@/app/services/skuOperationsService';
import { logger } from '@/lib/logger';
import type { Category } from '@/app/types/category';
import type { LocalizedName } from '@/app/types/common';
import type { SKU, SKUVariant } from '@/app/types/sku';
import SkuSelectionModal from '@/app/components/SkuSelectionModal';
import { toast } from 'react-toastify';

interface SkuFormData {
    skuId: number;
    name: LocalizedName;
    description: LocalizedName;
    costPrice: number;
    sellingPrice: number;
    mrp: number;
    imageUrl: string;
    images: string[];
    categoryIds: number[];
    variants: SKUVariant[];
    type: 'parent' | 'child';
    isActive: number;
    parentId?: number;
    variantName?: LocalizedName;
}

interface SkuFormProps {
    mode: 'create' | 'edit';
    initialData?: SKU;
    onSave: (sku: SKU) => Promise<void>;
    onCancel: () => void;
}

const initialFormData: SkuFormData = {
    skuId: 0,
    name: { en: '', ta: '' },
    description: { en: '', ta: '' },
    costPrice: 0,
    sellingPrice: 0,
    mrp: 0,
    imageUrl: '',
    images: [],
    categoryIds: [],
    variants: [],
    type: 'parent', // Default to parent for better UX flow
    isActive: 0, // Default to inactive for safety
};

export default function SkuForm({ mode, initialData, onSave, onCancel }: SkuFormProps) {
    const [formData, setFormData] = useState<SkuFormData>(initialFormData);
    const [categories, setCategories] = useState<Category[]>([]);
    const [parentSkus, setParentSkus] = useState<SKU[]>([]);
    const [loading, setLoading] = useState(true);
    const [internalSaving, setInternalSaving] = useState(false);
    const [errors, setErrors] = useState<Record<string, string>>({});
    const [imagePreview, setImagePreview] = useState<string>('');

    // Use internal saving state
    const isSaving = internalSaving;

    // Modal states
    const [showParentModal, setShowParentModal] = useState(false);
    const [showVariantsModal, setShowVariantsModal] = useState(false);

    // Load initial data and dependencies
    useEffect(() => {
        const loadData = async () => {
            try {
                setLoading(true);

                // Load categories and parent SKUs in parallel
                const [categoriesList, allSkus] = await Promise.all([
                    getOrderedCategories({ includeInactive: true }),
                    getSkus({ forceRefresh: true, allowInactive: true })
                ]);

                setCategories(categoriesList);
                setParentSkus(allSkus.filter(sku => sku.type === 'parent'));

                // Set initial form data
                if (initialData) {
                    // Edit mode - populate with existing data
                    const skuCategories = categoriesList
                        .filter(cat => cat.skuIds.includes(initialData.skuId))
                        .map(cat => cat.id);

                    setFormData({
                        skuId: initialData.skuId,
                        name: initialData.name || { en: '', ta: '' },
                        description: initialData.description || { en: '', ta: '' },
                        costPrice: initialData.costPrice ?? 0,
                        sellingPrice: initialData.sellingPrice ?? 0,
                        mrp: initialData.mrp ?? 0,
                        imageUrl: initialData.imageUrl || '',
                        images: initialData.images || [],
                        categoryIds: skuCategories,
                        variants: initialData.variants ?? [],
                        type: initialData.type || 'child',
                        isActive: initialData.isActive ?? 1,
                        parentId: initialData.parentId,
                        variantName: initialData.variantName
                    });
                    setImagePreview(initialData.imageUrl || '');
                } else {
                    // Create mode - use defaults
                    setFormData(initialFormData);
                }

            } catch (error) {
                logger.error('SkuForm: Error loading data:', error);
                toast.error('Failed to load form data');
            } finally {
                setLoading(false);
            }
        };

        loadData();
    }, [initialData]);

    // Handle form field changes
    const handleFieldChange = useCallback((field: keyof SkuFormData, value: any) => {
        setFormData(prev => ({ ...prev, [field]: value }));
        // Clear error when user starts typing
        if (errors[field]) {
            setErrors(prev => ({ ...prev, [field]: '' }));
        }
    }, [errors]);

    // Handle localized name changes
    const handleNameChange = useCallback((field: 'name' | 'description' | 'variantName', lang: 'en' | 'ta', value: string) => {
        setFormData(prev => ({
            ...prev,
            [field]: { ...prev[field], [lang]: value }
        }));
        // Clear error when user starts typing
        const errorKey = `${field}.${lang}`;
        if (errors[errorKey]) {
            setErrors(prev => ({ ...prev, [errorKey]: '' }));
        }
    }, [errors]);

    // Handle category selection
    const handleCategoryChange = useCallback((categoryId: number, checked: boolean) => {
        setFormData(prev => ({
            ...prev,
            categoryIds: checked
                ? [...prev.categoryIds, categoryId]
                : prev.categoryIds.filter(id => id !== categoryId)
        }));
        // Clear category error when user makes a selection
        if (errors.categoryIds) {
            setErrors(prev => ({ ...prev, categoryIds: '' }));
        }
    }, [errors.categoryIds]);

    // Helper function to determine category requirement label
    const getCategoryRequirement = (): string => {
        if (formData.type === 'parent') {
            return '*'; // Required for parent SKUs
        }
        if (formData.type === 'child' && !formData.parentId) {
            return '*'; // Required for orphan child SKUs
        }
        return '(Optional)'; // Optional for child SKUs with parents
    };

    // Helper function to get contextual help text for categories
    const getCategoryHelpText = (): string => {
        if (formData.type === 'parent') {
            return 'Categories are required for parent SKUs to ensure they can be discovered by customers.';
        }
        if (formData.type === 'child' && formData.parentId) {
            return 'Categories are optional for child SKUs with parents as they inherit discoverability from their parent.';
        }
        if (formData.type === 'child' && !formData.parentId) {
            return 'Categories are required for child SKUs without parents to ensure they can be discovered by customers.';
        }
        return 'Select the categories where this SKU should appear.';
    };

    // Handle image URL change
    const handleImageUrlChange = useCallback((url: string) => {
        setFormData(prev => ({ ...prev, imageUrl: url }));
        setImagePreview(url);
    }, []);

    // Handle additional images
    const addImage = useCallback(() => {
        setFormData(prev => ({ ...prev, images: [...prev.images, ''] }));
    }, []);

    const removeImage = useCallback((index: number) => {
        setFormData(prev => ({
            ...prev,
            images: prev.images.filter((_, i) => i !== index)
        }));
    }, []);

    const updateImageUrl = useCallback((index: number, url: string) => {
        setFormData(prev => ({
            ...prev,
            images: prev.images.map((img, i) => i === index ? url : img)
        }));
    }, []);

    const setPrimaryImage = useCallback((url: string) => {
        setFormData(prev => ({ ...prev, imageUrl: url }));
        setImagePreview(url);
    }, []);

    // Handle status toggle
    const handleStatusToggle = useCallback(() => {
        setFormData(prev => ({ ...prev, isActive: prev.isActive === 1 ? 0 : 1 }));
    }, []);

    // Handle SKU type change
    const handleTypeChange = useCallback((newType: 'parent' | 'child') => {
        setFormData(prev => ({
            ...prev,
            type: newType,
            // Reset type-specific fields
            parentId: newType === 'parent' ? undefined : prev.parentId,
            variantName: newType === 'parent' ? undefined : prev.variantName,
            variants: newType === 'child' ? [] : prev.variants,
            // Reset pricing for parent SKUs
            costPrice: newType === 'parent' ? 0 : prev.costPrice,
            sellingPrice: newType === 'parent' ? 0 : prev.sellingPrice,
            mrp: newType === 'parent' ? 0 : prev.mrp,
        }));
    }, []);

    // Handle parent selection
    const handleParentSelect = useCallback((selectedSkus: SKU[]) => {
        if (selectedSkus.length > 0) {
            const parentSku = selectedSkus[0]; // Only allow single parent
            setFormData(prev => ({ ...prev, parentId: parentSku.skuId }));
        }
        setShowParentModal(false);
    }, []);

    // Handle variant selection for parent SKUs
    const handleVariantsSelect = useCallback((selectedSkus: SKU[]) => {
        const newVariants: SKUVariant[] = selectedSkus.map(sku => ({
            skuId: sku.skuId,
            name: sku.name,
            costPrice: sku.costPrice || 0,
            sellingPrice: sku.sellingPrice || 0,
            mrp: sku.mrp || 0,
            type: 'child',
            variantName: sku.variantName || sku.name,
            isActive: sku.isActive ?? 1, // Include child SKU's active status
        }));

        setFormData(prev => ({ ...prev, variants: [...prev.variants, ...newVariants] }));
        setShowVariantsModal(false);
    }, []);

    // Remove variant
    const removeVariant = useCallback((index: number) => {
        setFormData(prev => ({
            ...prev,
            variants: prev.variants.filter((_, i) => i !== index)
        }));
    }, []);

    // Basic validation - only essential checks as per user request
    const validateForm = (): boolean => {
        const newErrors: Record<string, string> = {};

        // SKU ID is required for creation
        if (mode === 'create' && (!formData.skuId || formData.skuId <= 0)) {
            newErrors.skuId = 'SKU ID is required and must be a positive number';
        }

        // Only essential validations
        if (!formData.name.en.trim()) {
            newErrors['name.en'] = 'English name is required';
        }

        // Variant name for child SKUs with parents
        if (formData.type === 'child' && formData.parentId && !formData.variantName?.en?.trim()) {
            newErrors['variantName.en'] = 'Variant name is required for child SKUs with parents';
        }

        // Category validation based on SKU type and parent status
        if (formData.type === 'parent' && formData.categoryIds.length === 0) {
            newErrors.categoryIds = 'Parent SKUs must be assigned to at least one category for discoverability';
        }
        if (formData.type === 'child' && !formData.parentId && formData.categoryIds.length === 0) {
            newErrors.categoryIds = 'Child SKUs without parents must be assigned to at least one category for discoverability';
        }

        // Pricing validation for child SKUs
        if (formData.type === 'child') {
            if (!formData.costPrice || formData.costPrice <= 0) {
                newErrors.costPrice = 'Cost price is required and must be greater than 0';
            }
            if (!formData.sellingPrice || formData.sellingPrice <= 0) {
                newErrors.sellingPrice = 'Selling price is required and must be greater than 0';
            }
            if (!formData.mrp || formData.mrp <= 0) {
                newErrors.mrp = 'MRP is required and must be greater than 0';
            }
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    // Handle form submission
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!validateForm()) {
            toast.error('Please fill in all required fields');
            return;
        }

        setInternalSaving(true);
        try {
            // Convert form data to SKU format
            const skuData: SKU = {
                skuId: formData.skuId, // Use user-provided SKU ID
                name: formData.name,
                description: formData.description,
                imageUrl: formData.imageUrl,
                images: formData.images,
                type: formData.type,
                isActive: formData.isActive,
                variants: formData.type === 'parent' ? formData.variants : undefined,
                costPrice: formData.type === 'child' ? formData.costPrice : undefined,
                sellingPrice: formData.type === 'child' ? formData.sellingPrice : undefined,
                mrp: formData.type === 'child' ? formData.mrp : undefined,
                parentId: formData.type === 'child' ? formData.parentId : undefined,
                variantName: formData.type === 'child' ? formData.variantName : undefined,
            };

            // Use the unified operations service for consistent data handling
            await SkuOperationsService.saveSingleSku(skuData, formData.categoryIds, initialData);
            await onSave(skuData);

        } catch (error) {
            logger.error('SkuForm: Error saving SKU:', error);
            toast.error('Failed to save SKU. Please try again.');
        } finally {
            setInternalSaving(false);
        }
    };

    if (loading) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
        );
    }

    const selectedParent = formData.parentId ? parentSkus.find(sku => sku.skuId === formData.parentId) : null;

    return (
        <form onSubmit={handleSubmit} className="space-y-6">
            {/* SKU Type Selection */}
            {mode === 'create' && (
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h2 className="text-lg font-medium text-gray-900 mb-4">SKU Type</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <label className={`cursor-pointer rounded-lg border-2 p-4 ${formData.type === 'parent' ? 'border-blue-500 bg-blue-50' : 'border-gray-200'}`}>
                            <input
                                type="radio"
                                name="skuType"
                                value="parent"
                                checked={formData.type === 'parent'}
                                onChange={() => handleTypeChange('parent')}
                                className="sr-only"
                            />
                            <div className="text-center">
                                <div className="text-lg font-medium text-gray-900">Parent SKU</div>
                                <div className="text-sm text-gray-500 mt-1">Container for variants, no direct pricing</div>
                            </div>
                        </label>
                        <label className={`cursor-pointer rounded-lg border-2 p-4 ${formData.type === 'child' ? 'border-blue-500 bg-blue-50' : 'border-gray-200'}`}>
                            <input
                                type="radio"
                                name="skuType"
                                value="child"
                                checked={formData.type === 'child'}
                                onChange={() => handleTypeChange('child')}
                                className="sr-only"
                            />
                            <div className="text-center">
                                <div className="text-lg font-medium text-gray-900">Child SKU</div>
                                <div className="text-sm text-gray-500 mt-1">Purchasable item with pricing</div>
                            </div>
                        </label>
                    </div>
                </div>
            )}

            {/* Type Indicator for Edit Mode */}
            {mode === 'edit' && (
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div className="flex items-center justify-between">
                        <h2 className="text-lg font-medium text-gray-900">SKU Type</h2>
                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${formData.type === 'parent'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-blue-100 text-blue-800'
                            }`}>
                            {formData.type === 'parent' ? 'Parent SKU' : 'Child SKU'}
                        </span>
                    </div>
                    <p className="mt-1 text-sm text-gray-600">
                        {formData.type === 'parent'
                            ? 'This is a parent SKU that contains variants'
                            : 'This is a child SKU with pricing information'
                        }
                    </p>
                </div>
            )}

            {/* Parent Selection for Child SKUs */}
            {formData.type === 'child' && (
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h2 className="text-lg font-medium text-gray-900 mb-4">Parent SKU (Optional)</h2>
                    <div className="space-y-4">
                        {selectedParent ? (
                            <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                                <div className="flex items-center space-x-3">
                                    <Image
                                        src={selectedParent.imageUrl || '/placeholder-product.png'}
                                        alt={selectedParent.name.en}
                                        width={40}
                                        height={40}
                                        className="h-10 w-10 rounded-lg object-cover"
                                    />
                                    <div>
                                        <div className="text-sm font-medium text-gray-900">#{selectedParent.skuId}</div>
                                        <div className="text-sm text-gray-500">{selectedParent.name.en}</div>
                                    </div>
                                </div>
                                <button
                                    type="button"
                                    onClick={() => setFormData(prev => ({ ...prev, parentId: undefined }))}
                                    className="text-red-600 hover:text-red-800 text-sm"
                                >
                                    Remove
                                </button>
                            </div>
                        ) : (
                            <button
                                type="button"
                                onClick={() => setShowParentModal(true)}
                                className="w-full p-4 border-2 border-dashed border-gray-300 rounded-lg text-gray-600 hover:border-gray-400 hover:text-gray-700"
                            >
                                <PlusIcon className="h-6 w-6 mx-auto mb-1" />
                                <span className="text-sm">Select Parent SKU</span>
                            </button>
                        )}
                    </div>
                </div>
            )}

            {/* Variant Name for Child SKUs */}
            {formData.type === 'child' && (
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h2 className="text-lg font-medium text-gray-900 mb-4">Variant Information</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Variant Name (English) *
                            </label>
                            <input
                                type="text"
                                value={formData.variantName?.en || ''}
                                onChange={(e) => handleNameChange('variantName', 'en', e.target.value)}
                                className={`w-full px-3 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${errors['variantName.en'] ? 'border-red-300' : 'border-gray-300'
                                    }`}
                                placeholder="e.g., 500g, Small, Red"
                            />
                            {errors['variantName.en'] && (
                                <p className="mt-1 text-sm text-red-600">{errors['variantName.en']}</p>
                            )}
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Variant Name (Tamil)
                            </label>
                            <input
                                type="text"
                                value={formData.variantName?.ta || ''}
                                onChange={(e) => handleNameChange('variantName', 'ta', e.target.value)}
                                className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                placeholder="Tamil variant name"
                            />
                        </div>
                    </div>
                </div>
            )}

            {/* Variants for Parent SKUs */}
            {formData.type === 'parent' && (
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div className="flex items-center justify-between mb-4">
                        <h2 className="text-lg font-medium text-gray-900">Variants</h2>
                        <button
                            type="button"
                            onClick={() => setShowVariantsModal(true)}
                            className="bg-blue-600 text-white px-3 py-2 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center space-x-2 text-sm"
                        >
                            <PlusIcon className="h-4 w-4" />
                            <span>Add Variants</span>
                        </button>
                    </div>

                    {formData.variants.length === 0 ? (
                        <div className="text-center py-8 text-gray-500 border-2 border-dashed border-gray-300 rounded-lg">
                            <PlusIcon className="mx-auto h-8 w-8 text-gray-400" />
                            <p className="mt-2">No variants added yet. Click "Add Variants" to select child SKUs.</p>
                        </div>
                    ) : (
                        <div className="overflow-x-auto">
                            <table className="min-w-full divide-y divide-gray-200">
                                <thead className="bg-gray-50">
                                    <tr>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            SKU
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Variant Name
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Price
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Actions
                                        </th>
                                    </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                    {formData.variants.map((variant, index) => (
                                        <tr key={variant.skuId}>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                #{variant.skuId}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="text-sm font-medium text-gray-900">
                                                    {variant.variantName?.en || variant.name.en}
                                                </div>
                                                {variant.variantName?.ta && (
                                                    <div className="text-sm text-gray-500">{variant.variantName.ta}</div>
                                                )}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                ₹{variant.sellingPrice}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                <button
                                                    type="button"
                                                    onClick={() => removeVariant(index)}
                                                    className="text-red-600 hover:text-red-800"
                                                >
                                                    <TrashIcon className="h-4 w-4" />
                                                </button>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    )}
                </div>
            )}

            {/* SKU ID Input */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h2 className="text-lg font-medium text-gray-900 mb-4">SKU Identification</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            SKU ID *
                        </label>
                        <input
                            type="number"
                            min="1"
                            step="1"
                            value={formData.skuId || ''}
                            onChange={(e) => handleFieldChange('skuId', parseInt(e.target.value) || 0)}
                            disabled={mode === 'edit'}
                            className={`w-full px-3 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${mode === 'edit'
                                ? 'bg-gray-100 text-gray-500 cursor-not-allowed'
                                : errors.skuId ? 'border-red-300' : 'border-gray-300'
                                }`}
                            placeholder="Enter unique SKU ID (numbers only)"
                        />
                        {errors.skuId && (
                            <p className="mt-1 text-sm text-red-600">{errors.skuId}</p>
                        )}
                        {mode === 'edit' && (
                            <p className="mt-1 text-sm text-gray-500">SKU ID cannot be changed after creation</p>
                        )}
                        {mode === 'create' && (
                            <p className="mt-1 text-sm text-gray-500">Enter a unique numeric identifier for this SKU</p>
                        )}
                    </div>
                    <div className="flex items-center justify-center">
                        <div className="text-center">
                            <div className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-medium ${formData.type === 'parent'
                                ? 'bg-yellow-100 text-yellow-800'
                                : 'bg-blue-100 text-blue-800'
                                }`}>
                                #{formData.skuId || '---'} • {formData.type === 'parent' ? 'Parent SKU' : 'Child SKU'}
                            </div>
                            <p className="mt-2 text-xs text-gray-500">
                                {formData.type === 'parent'
                                    ? 'This SKU will contain variants'
                                    : 'This SKU is a purchasable item'
                                }
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            {/* Basic Information */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h2 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* English Name */}
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Name (English) *
                        </label>
                        <input
                            type="text"
                            value={formData.name.en}
                            onChange={(e) => handleNameChange('name', 'en', e.target.value)}
                            className={`w-full px-3 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${errors['name.en'] ? 'border-red-300' : 'border-gray-300'
                                }`}
                            placeholder="Enter product name in English"
                        />
                        {errors['name.en'] && (
                            <p className="mt-1 text-sm text-red-600">{errors['name.en']}</p>
                        )}
                    </div>

                    {/* Tamil Name */}
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Name (Tamil)
                        </label>
                        <input
                            type="text"
                            value={formData.name.ta || ''}
                            onChange={(e) => handleNameChange('name', 'ta', e.target.value)}
                            className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="Enter product name in Tamil"
                        />
                    </div>

                    {/* English Description */}
                    <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Description (English)
                        </label>
                        <textarea
                            value={formData.description.en}
                            onChange={(e) => handleNameChange('description', 'en', e.target.value)}
                            rows={3}
                            className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="Enter product description in English"
                        />
                    </div>

                    {/* Tamil Description */}
                    <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Description (Tamil)
                        </label>
                        <textarea
                            value={formData.description.ta || ''}
                            onChange={(e) => handleNameChange('description', 'ta', e.target.value)}
                            rows={3}
                            className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="Enter product description in Tamil"
                        />
                    </div>
                </div>
            </div>

            {/* Status */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h2 className="text-lg font-medium text-gray-900 mb-4">Status</h2>
                <div className="flex items-center justify-between">
                    <div>
                        <h3 className="text-sm font-medium text-gray-900">SKU Status</h3>
                        <p className="text-sm text-gray-500">
                            {formData.isActive === 1
                                ? 'This SKU is currently active and visible to customers'
                                : 'This SKU is currently inactive and hidden from customers'
                            }
                        </p>
                        {mode === 'create' && formData.isActive === 0 && (
                            <p className="text-sm text-blue-600 mt-1">
                                💡 New SKUs are created as inactive by default for safety. Activate when ready to sell.
                            </p>
                        )}
                    </div>
                    <div className="flex items-center space-x-3">
                        <span className={`text-sm font-medium ${formData.isActive === 1 ? 'text-green-700' : 'text-red-700'
                            }`}>
                            {formData.isActive === 1 ? 'Active' : 'Inactive'}
                        </span>
                        <button
                            type="button"
                            onClick={handleStatusToggle}
                            className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${formData.isActive === 1 ? 'bg-green-600' : 'bg-gray-200'
                                }`}
                            role="switch"
                            aria-checked={formData.isActive === 1}
                        >
                            <span
                                aria-hidden="true"
                                className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${formData.isActive === 1 ? 'translate-x-5' : 'translate-x-0'
                                    }`}
                            />
                        </button>
                    </div>
                </div>
            </div>

            {/* Pricing - Only for Child SKUs */}
            {formData.type === 'child' && (
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h2 className="text-lg font-medium text-gray-900 mb-4">Pricing</h2>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        {/* Cost Price */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Cost Price (₹) *
                            </label>
                            <input
                                type="number"
                                min="0"
                                step="0.01"
                                value={formData.costPrice || ''}
                                onChange={(e) => handleFieldChange('costPrice', parseFloat(e.target.value) || 0)}
                                className={`w-full px-3 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${errors.costPrice ? 'border-red-300' : 'border-gray-300'}`}
                                placeholder="0.00"
                            />
                            {errors.costPrice && (
                                <p className="mt-1 text-sm text-red-600">{errors.costPrice}</p>
                            )}
                        </div>

                        {/* Selling Price */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Selling Price (₹) *
                            </label>
                            <input
                                type="number"
                                min="0"
                                step="0.01"
                                value={formData.sellingPrice || ''}
                                onChange={(e) => handleFieldChange('sellingPrice', parseFloat(e.target.value) || 0)}
                                className={`w-full px-3 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${errors.sellingPrice ? 'border-red-300' : 'border-gray-300'}`}
                                placeholder="0.00"
                            />
                            {errors.sellingPrice && (
                                <p className="mt-1 text-sm text-red-600">{errors.sellingPrice}</p>
                            )}
                        </div>

                        {/* MRP */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                MRP (₹) *
                            </label>
                            <input
                                type="number"
                                min="0"
                                step="0.01"
                                value={formData.mrp || ''}
                                onChange={(e) => handleFieldChange('mrp', parseFloat(e.target.value) || 0)}
                                className={`w-full px-3 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${errors.mrp ? 'border-red-300' : 'border-gray-300'}`}
                                placeholder="0.00"
                            />
                            {errors.mrp && (
                                <p className="mt-1 text-sm text-red-600">{errors.mrp}</p>
                            )}
                        </div>
                    </div>
                </div>
            )}

            {/* Images */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h2 className="text-lg font-medium text-gray-900 mb-4">Images</h2>

                {/* Primary Image */}
                <div className="mb-6">
                    <h3 className="text-md font-medium text-gray-900 mb-3">Primary Image</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Primary Image URL
                            </label>
                            <input
                                type="url"
                                value={formData.imageUrl}
                                onChange={(e) => handleImageUrlChange(e.target.value)}
                                className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                placeholder="https://example.com/image.jpg"
                            />
                            <p className="mt-1 text-sm text-gray-500">
                                This will be the main product image displayed in listings
                            </p>
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Primary Image Preview
                            </label>
                            <div className="w-full h-32 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center">
                                {imagePreview ? (
                                    <Image
                                        src={imagePreview}
                                        alt="Primary image preview"
                                        width={120}
                                        height={120}
                                        className="max-h-28 max-w-28 object-cover rounded"
                                        onError={() => setImagePreview('')}
                                    />
                                ) : (
                                    <div className="text-center">
                                        <PhotoIcon className="mx-auto h-8 w-8 text-gray-400" />
                                        <p className="mt-1 text-sm text-gray-500">No primary image</p>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>

                {/* Additional Images */}
                <div>
                    <div className="flex items-center justify-between mb-3">
                        <h3 className="text-md font-medium text-gray-900">Additional Images</h3>
                        <button
                            type="button"
                            onClick={addImage}
                            className="bg-blue-600 text-white px-3 py-2 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center space-x-2 text-sm"
                        >
                            <PlusIcon className="h-4 w-4" />
                            <span>Add Image</span>
                        </button>
                    </div>

                    {formData.images.length === 0 ? (
                        <div className="text-center py-8 text-gray-500 border-2 border-dashed border-gray-300 rounded-lg">
                            <PhotoIcon className="mx-auto h-8 w-8 text-gray-400" />
                            <p className="mt-2">No additional images. Click "Add Image" to add more product images.</p>
                        </div>
                    ) : (
                        <div className="space-y-4">
                            {formData.images.map((imageUrl, index) => (
                                <div key={index} className="border border-gray-200 rounded-lg p-4">
                                    <div className="flex items-center justify-between mb-3">
                                        <h4 className="text-sm font-medium text-gray-900">Image {index + 1}</h4>
                                        <div className="flex items-center space-x-2">
                                            {imageUrl && imageUrl !== formData.imageUrl && (
                                                <button
                                                    type="button"
                                                    onClick={() => setPrimaryImage(imageUrl)}
                                                    className="text-blue-600 hover:text-blue-800 text-xs font-medium"
                                                >
                                                    Set as Primary
                                                </button>
                                            )}
                                            <button
                                                type="button"
                                                onClick={() => removeImage(index)}
                                                className="text-red-600 hover:text-red-800 p-1"
                                            >
                                                <TrashIcon className="h-4 w-4" />
                                            </button>
                                        </div>
                                    </div>

                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <input
                                                type="url"
                                                value={imageUrl}
                                                onChange={(e) => updateImageUrl(index, e.target.value)}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                                placeholder="https://example.com/image.jpg"
                                            />
                                        </div>
                                        <div>
                                            <div className="w-full h-20 border border-gray-300 rounded-lg flex items-center justify-center">
                                                {imageUrl ? (
                                                    <Image
                                                        src={imageUrl}
                                                        alt={`Additional image ${index + 1}`}
                                                        width={80}
                                                        height={80}
                                                        className="max-h-16 max-w-16 object-cover rounded"
                                                        onError={() => updateImageUrl(index, '')}
                                                    />
                                                ) : (
                                                    <PhotoIcon className="h-6 w-6 text-gray-400" />
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            </div>

            {/* Categories */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="mb-4">
                    <h2 className="text-lg font-medium text-gray-900 mb-2">
                        Categories {getCategoryRequirement()}
                    </h2>
                    <p className="text-sm text-gray-600">
                        {getCategoryHelpText()}
                    </p>
                    {errors.categoryIds && (
                        <p className="mt-1 text-sm text-red-600">{errors.categoryIds}</p>
                    )}
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {categories.map((category) => (
                        <label key={category.id} className={`flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors ${errors.categoryIds ? 'border-red-300 bg-red-50' : 'border-gray-200'
                            }`}>
                            <input
                                type="checkbox"
                                checked={formData.categoryIds.includes(category.id)}
                                onChange={(e) => handleCategoryChange(category.id, e.target.checked)}
                                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                            />
                            <div className="flex-1">
                                <div className="text-sm font-medium text-gray-900">{category.name.en}</div>
                                {category.name.ta && (
                                    <div className="text-sm text-gray-500">{category.name.ta}</div>
                                )}
                            </div>
                        </label>
                    ))}
                </div>
                {formData.categoryIds.length > 0 && (
                    <div className="mt-3 text-sm text-green-600">
                        ✓ Selected {formData.categoryIds.length} category{formData.categoryIds.length > 1 ? 'ies' : 'y'}
                    </div>
                )}
            </div>

            {/* Form Actions */}
            <div className="flex items-center justify-end space-x-3">
                <button
                    type="button"
                    onClick={onCancel}
                    className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                >
                    Cancel
                </button>
                <button
                    type="submit"
                    disabled={isSaving}
                    className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 flex items-center space-x-2"
                >
                    {isSaving ? (
                        <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                            <span>Saving...</span>
                        </>
                    ) : (
                        <span>{mode === 'create' ? 'Create SKU' : 'Save Changes'}</span>
                    )}
                </button>
            </div>

            {/* Modals */}
            <SkuSelectionModal
                isOpen={showParentModal}
                onClose={() => setShowParentModal(false)}
                onSelect={handleParentSelect}
                title="Select Parent SKU"
                searchPlaceholder="Search parent SKUs..."
                filterType="parent"
                activeOnly={true}
                excludeSkuIds={initialData ? [initialData.skuId] : []}
            />

            <SkuSelectionModal
                isOpen={showVariantsModal}
                onClose={() => setShowVariantsModal(false)}
                onSelect={handleVariantsSelect}
                title="Select Child SKUs as Variants"
                searchPlaceholder="Search child SKUs..."
                filterType="child"
                activeOnly={false}
                excludeSkuIds={[
                    ...(initialData ? [initialData.skuId] : []),
                    ...formData.variants.map(v => v.skuId)
                ]}
            />
        </form>
    );
} 
'use client'

import { usePathname, useSearchParams } from "next/navigation"
import { useEffect, Suspense } from "react"
import Script from "next/script"

declare global {
    interface Window {
        gtag: (...args: any[]) => void;
        dataLayer: any[];
    }
}

export function GoogleAnalyticsProvider({ children }: { children: React.ReactNode }) {
    const trackingId = process.env.NEXT_PUBLIC_GA4_TRACKING_ID;

    // Only render GA4 if tracking ID is present
    if (!trackingId) {
        return <>{children}</>;
    }

    return (
        <>
            {/* Google Analytics Scripts */}
            <Script
                src={`https://www.googletagmanager.com/gtag/js?id=${trackingId}`}
                strategy="afterInteractive"
            />
            <Script id="google-analytics" strategy="afterInteractive">
                {`
                    window.dataLayer = window.dataLayer || [];
                    function gtag(){dataLayer.push(arguments);}
                    gtag('js', new Date());
                    gtag('config', '${trackingId}', {
                        page_title: document.title,
                        page_location: window.location.href,
                        send_page_view: false // We'll handle page views manually
                    });
                `}
            </Script>
            <SuspendedGoogleAnalyticsPageView trackingId={trackingId} />
            {children}
        </>
    );
}

function GoogleAnalyticsPageView({ trackingId }: { trackingId: string }) {
    const pathname = usePathname();
    const searchParams = useSearchParams();

    // Track pageviews manually
    useEffect(() => {
        if (pathname && typeof window !== 'undefined' && window.gtag) {
            let url = window.origin + pathname;
            if (searchParams.toString()) {
                url = url + "?" + searchParams.toString();
            }

            window.gtag('config', trackingId, {
                page_path: pathname,
                page_title: document.title,
                page_location: url,
            });

            // Send pageview event
            window.gtag('event', 'page_view', {
                page_title: document.title,
                page_location: url,
                page_path: pathname,
            });
        }
    }, [pathname, searchParams, trackingId]);

    return null;
}

// Wrap GoogleAnalyticsPageView in Suspense to avoid the useSearchParams usage above
// from de-opting the whole app into client-side rendering
function SuspendedGoogleAnalyticsPageView({ trackingId }: { trackingId: string }) {
    return (
        <Suspense fallback={null}>
            <GoogleAnalyticsPageView trackingId={trackingId} />
        </Suspense>
    );
} 
'use client';
import React from 'react';
import Link from 'next/link';
import { Card } from '@/components/ui/card';
import { useTranslation } from 'react-i18next';
import Breadcrumbs from '../components/Breadcrumbs';

const AccountPage: React.FC = () => {
    const { t } = useTranslation();

    const breadcrumbItems = [
        { name: t('account.breadcrumbs.home', 'Home'), href: '/' },
        { name: t('account.breadcrumbs.account', 'Account'), href: '/account' },
    ];

    return (
        <div className="container mx-auto py-6 px-4 sm:px-6">
            <Breadcrumbs items={breadcrumbItems} />

            <div className="mb-8">
                <h1 className="text-3xl font-bold text-gray-900 mb-2">{t('account.title', 'Account')}</h1>
                <p className="text-gray-600">{t('account.subtitle', 'Manage your account and view your orders')}</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <Card className="hover:shadow-lg transition-shadow">
                    <Link href="/account/orders" className="block p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <h3 className="font-semibold text-lg text-gray-900 mb-2">{t('account.menu.todaysOrder', "Today's Order")}</h3>
                                <p className="text-sm text-gray-600">{t('account.menu.todaysOrderDesc', 'View orders placed today')}</p>
                            </div>
                            <div className="text-gray-400">
                                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                </svg>
                            </div>
                        </div>
                    </Link>
                </Card>
                {/* Future menu items can be added here */}
            </div>
        </div>
    );
};

export default AccountPage; 
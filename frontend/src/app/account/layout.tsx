"use client";

import React from 'react';
import { useAuth } from '../context/AuthContext';
import { Header } from '../components/Header/Header';
import LoginModal from '../components/LoginModal';

export default function AccountLayout({ children }: { children: React.ReactNode }) {
    const { isUserLoggedIn, isLoading } = useAuth();

    // Show loading state while checking authentication
    if (isLoading) {
        return (
            <div className="min-h-screen flex items-center justify-center">
                <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
            </div>
        );
    }

    // If not logged in, show login modal
    if (!isUserLoggedIn) {
        return (
            <div className="min-h-screen flex flex-col">
                <Header />
                <main className="flex-1 bg-gray-50 flex items-center justify-center">
                    <div className="text-center">
                        <h1 className="text-2xl font-bold text-gray-900 mb-4"><PERSON><PERSON> Required</h1>
                        <p className="text-gray-600 mb-6">Please log in to access your account.</p>
                    </div>
                </main>
                <LoginModal isOpen={true} onClose={() => { }} dismissable={false} />
            </div>
        );
    }

    // Authenticated layout - simple header + content without sidebar spacing
    return (
        <div className="min-h-screen flex flex-col">
            <main className="flex-1 bg-gray-50">
                {children}
            </main>
        </div>
    );
} 
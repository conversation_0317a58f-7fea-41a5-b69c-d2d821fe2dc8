"use client";

import React, { useMemo, useEffect, useState, useCallback } from 'react';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { useQuery } from '@tanstack/react-query';
import Image from 'next/image';
import { getOrderById, calculateOrderTotal, formatDeliveryDate, parseDeliveryLocation } from '../../../services/orderService';
import { getSkuById } from '../../../services/skuService';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { useTranslation } from 'react-i18next';
import Breadcrumbs from '../../../components/Breadcrumbs';
import StatusBadge from '../../../admin/components/StatusBadge';
import { SKU } from '../../../types/sku';
import { logger } from '@/lib/logger';

// Helper functions for quantity display system
const getQuantityDisplayConfig = (status: string) => {
    const statusLower = status.toLowerCase();

    if (['new', 'confirmed', 'unreachable_1', 'unreachable_2'].includes(statusLower)) {
        return {
            showOrdered: true,
            showPicked: false,
            showDelivered: false,
            columns: ['product', 'unit', 'orderedQty', 'price', 'orderedTotal']
        };
    } else if (['picked'].includes(statusLower)) {
        return {
            showOrdered: true,
            showPicked: true,
            showDelivered: false,
            columns: ['product', 'unit', 'orderedQty', 'pickedQty', 'price', 'orderedTotal']
        };
    } else if (['delivered', 'partially_delivered'].includes(statusLower)) {
        return {
            showOrdered: true,
            showPicked: true,
            showDelivered: true,
            columns: ['product', 'unit', 'orderedQty', 'pickedQty', 'deliveredQty', 'price', 'orderedTotal']
        };
    }

    // Default fallback
    return {
        showOrdered: true,
        showPicked: false,
        showDelivered: false,
        columns: ['product', 'unit', 'orderedQty', 'price', 'orderedTotal']
    };
};

const getQuantityValue = (item: any, type: 'ordered' | 'picked' | 'delivered'): number | null => {
    switch (type) {
        case 'ordered':
            return item.quantity;
        case 'picked':
            return item.pickedQuantity ?? null;
        case 'delivered':
            return item.deliveredQuantity ?? null;
        default:
            return null;
    }
};

const getQuantityDisplayStyle = (item: any, type: 'picked' | 'delivered'): string => {
    const orderedQty = item.quantity;
    const currentQty = getQuantityValue(item, type);

    if (currentQty === null) {
        return 'text-gray-500'; // For dash display
    }

    // Only show highlighting when there's a difference
    if (currentQty === orderedQty) {
        return 'text-gray-900'; // No highlighting for perfect match
    } else if (currentQty < orderedQty) {
        return 'bg-orange-50 text-orange-800 border border-orange-200'; // Under-fulfillment
    } else {
        return 'bg-red-50 text-red-800 border border-red-200'; // Over-fulfillment
    }
};

const calculateTotal = (item: any, type: 'ordered' | 'picked' | 'delivered'): number => {
    const qty = getQuantityValue(item, type);
    return qty !== null ? qty * item.price : 0;
};

const safeRender = (value: unknown): string => {
    if (value === null || value === undefined) {
        return '';
    }
    if (typeof value === 'object') {
        logger.warn('Attempted to render object directly', { value });
        return JSON.stringify(value);
    }
    return String(value);
};

const AccountOrderDetailPage: React.FC = () => {
    const { t } = useTranslation();
    const params = useParams();
    const router = useRouter();
    const orderId = parseInt(params.orderId as string, 10);
    const [skuDetails, setSkuDetails] = useState<Record<number, SKU>>({});

    const breadcrumbItems = [
        { name: t('account.breadcrumbs.home', 'Home'), href: '/' },
        { name: t('account.breadcrumbs.account', 'Account'), href: '/account' },
        { name: t('account.breadcrumbs.orders', "Today's Orders"), href: '/account/orders' },
        { name: t('account.breadcrumbs.orderDetail', `Order #${orderId}`), href: `/account/order/${orderId}` },
    ];

    const {
        data: order,
        isLoading,
        isError,
        error,
        refetch
    } = useQuery({
        queryKey: ['account-order', orderId],
        queryFn: () => getOrderById(orderId),
        enabled: !isNaN(orderId),
        staleTime: 30000,
        gcTime: 300000,
    });

    // Function to fetch SKU details for missing SKUs
    const fetchMissingSkuDetails = useCallback(async (skuIds: number[]) => {
        const validSkuIds = skuIds.filter(skuId => skuId && skuId > 0);
        const missingSkuIds = validSkuIds.filter(skuId => !skuDetails[skuId]);

        if (missingSkuIds.length === 0) return;

        for (const skuId of missingSkuIds) {
            try {
                const skuDetail = await getSkuById(skuId, { allowInactive: true });
                if (skuDetail) {
                    setSkuDetails(prev => ({ ...prev, [skuId]: skuDetail }));
                }
            } catch (error) {
                logger.error(`Failed to fetch SKU details for ID ${skuId}`, error);
            }
        }
    }, [skuDetails]);

    // Fetch SKU details for order items
    useEffect(() => {
        if (order?.skuItems && order.skuItems.length > 0) {
            const skuIds = order.skuItems.map(item => item.skuID);
            fetchMissingSkuDetails(skuIds);
        }
    }, [order?.skuItems, fetchMissingSkuDetails]);

    // Parse delivery location string into coordinates
    const deliveryCoords = useMemo(() => {
        return parseDeliveryLocation(order?.deliveryLocation || null);
    }, [order?.deliveryLocation]);

    if (isNaN(orderId)) {
        return (
            <div className="container mx-auto py-6 px-4 sm:px-6 lg:px-8">
                <Breadcrumbs items={breadcrumbItems} />
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <h3 className="text-lg font-medium text-red-800 mb-2">{t('account.orderDetail.invalidId', 'Invalid order ID')}</h3>
                </div>
            </div>
        );
    }

    if (isLoading) {
        return (
            <div className="container mx-auto py-6 px-4 sm:px-6 lg:px-8">
                <Breadcrumbs items={breadcrumbItems} />
                <div className="space-y-6">
                    <Skeleton className="h-8 w-1/4" />
                    <Card><CardContent className="p-6"><Skeleton className="h-32 w-full" /></CardContent></Card>
                    <Card><CardContent className="p-6"><Skeleton className="h-48 w-full" /></CardContent></Card>
                </div>
            </div>
        );
    }

    if (isError || !order) {
        return (
            <div className="container mx-auto py-6 px-4 sm:px-6 lg:px-8">
                <Breadcrumbs items={breadcrumbItems} />
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <h3 className="text-lg font-medium text-red-800 mb-2">{t('account.orderDetail.error.title', 'Failed to load order')}</h3>
                    <p className="text-red-600 mb-4">{error?.message || t('account.orderDetail.error.notFound', 'Order not found.')}</p>
                    <button
                        onClick={() => refetch()}
                        className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
                    >
                        {t('account.orderDetail.error.retry', 'Retry')}
                    </button>
                </div>
            </div>
        );
    }

    const totalValue = calculateOrderTotal(order);
    const quantityConfig = getQuantityDisplayConfig(order.status);

    return (
        <div className="container mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <Breadcrumbs items={breadcrumbItems} />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">
                            {t('account.orderDetail.title', { id: order.id, defaultValue: `Order #${order.id}` })}
                        </h1>
                        <p className="text-gray-600 mt-1">
                            {t('account.orderDetail.subtitle', 'Order details and status')}
                        </p>
                    </div>
                </div>

                {/* Order Overview */}
                <Card>
                    <CardContent className="p-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-4">
                            {t('account.orderDetail.overview.title', 'Order Overview')}
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    {t('account.orderDetail.overview.status', 'Status')}
                                </label>
                                <StatusBadge status={order.status.toLowerCase()} />
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    {t('account.orderDetail.overview.total', 'Total Amount')}
                                </label>
                                <p className="text-lg font-semibold text-green-600">₹{totalValue.toFixed(2)}</p>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    {t('account.orderDetail.overview.items', 'Items')}
                                </label>
                                <p className="text-lg font-medium">{order.skuItems.length}</p>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    {t('account.orderDetail.overview.facility', 'Facility')}
                                </label>
                                <p className="text-gray-900">{order.facilityKey || '-'}</p>
                            </div>
                        </div>

                        {order.status.toLowerCase() === 'cancelled' && order.metadata && order.metadata.reason && (
                            <div className="bg-red-50 border border-red-200 rounded-md p-3 mt-4">
                                <p className="text-sm font-medium text-red-800 mb-1">
                                    {t('account.orderDetail.overview.cancellationReason', 'Cancellation Reason')}
                                </p>
                                <p className="text-sm text-red-700">
                                    {safeRender(order.metadata.reason)}
                                </p>
                            </div>
                        )}
                    </CardContent>
                </Card>

                {/* Customer Information */}
                <Card>
                    <CardContent className="p-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-4">
                            {t('account.orderDetail.customer.title', 'Customer Information')}
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    {t('account.orderDetail.customer.name', 'Customer Name')}
                                </label>
                                <p className="text-gray-900">{safeRender(order.endCustomerName) || t('account.orderDetail.customer.noName', 'Not provided')}</p>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    {t('account.orderDetail.customer.mobile', 'Mobile Number')}
                                </label>
                                <p className="text-gray-900">{safeRender(order.mobile) || t('account.orderDetail.customer.noMobile', 'Not provided')}</p>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    {t('account.orderDetail.customer.deliveryDate', 'Delivery Date')}
                                </label>
                                <p className="text-gray-900">{safeRender(formatDeliveryDate(order.deliveryDate))}</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Delivery Location */}
                <Card>
                    <CardContent className="p-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-4">
                            {t('account.orderDetail.location.title', 'Delivery Location')}
                        </h3>
                        <div className="grid grid-cols-1 gap-6">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    {t('account.orderDetail.location.address', 'Delivery Address')}
                                </label>
                                <p className="text-gray-900 px-3 py-2 border border-gray-200 rounded-md bg-gray-50">
                                    {safeRender(order.metadata?.deliveryAddress) || t('account.orderDetail.location.noAddress', 'No address provided')}
                                </p>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    {t('account.orderDetail.location.coordinates', 'Coordinates')}
                                </label>
                                <p className="text-gray-900 px-3 py-2 border border-gray-200 rounded-md bg-gray-50">
                                    {safeRender(order.deliveryLocation) || t('account.orderDetail.location.noCoordinates', 'No coordinates provided')}
                                </p>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Order Items */}
                <Card>
                    <CardContent className="p-0">
                        <div className="px-6 py-4 border-b border-gray-200">
                            <h3 className="text-lg font-medium text-gray-900">
                                {t('account.orderDetail.items.title', 'Order Items')} ({order.skuItems.length})
                            </h3>
                        </div>

                        <div className="overflow-x-auto">
                            {order.skuItems.length === 0 ? (
                                <div className="text-center py-12">
                                    <p className="text-gray-500">{t('account.orderDetail.items.empty', 'No items in this order')}</p>
                                </div>
                            ) : (
                                <table className="min-w-full divide-y divide-gray-200">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                {t('account.orderDetail.items.product', 'Product')}
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                {t('account.orderDetail.items.unit', 'Unit')}
                                            </th>
                                            {quantityConfig.showOrdered && (
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    {t('account.orderDetail.items.orderedQty', 'Ordered')}
                                                </th>
                                            )}
                                            {quantityConfig.showPicked && (
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    {t('account.orderDetail.items.pickedQty', 'Picked')}
                                                </th>
                                            )}
                                            {quantityConfig.showDelivered && (
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    {t('account.orderDetail.items.deliveredQty', 'Delivered')}
                                                </th>
                                            )}
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                {t('account.orderDetail.items.price', 'Price')}
                                            </th>
                                            {quantityConfig.showOrdered && (
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    {t('account.orderDetail.items.total', 'Total')}
                                                </th>
                                            )}
                                        </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                        {order.skuItems.map((item, index) => {
                                            const skuDetail = skuDetails[item.skuID];
                                            const isInvalidSku = !item.skuID || item.skuID <= 0;

                                            return (
                                                <tr key={index} className="hover:bg-gray-50">
                                                    <td className="px-6 py-4">
                                                        <div className="flex items-center">
                                                            <div className="h-10 w-10 flex-shrink-0">
                                                                <Image
                                                                    src={skuDetail?.imageUrl || '/placeholder-product.png'}
                                                                    alt={skuDetail?.name?.en || `SKU ${item.skuID}`}
                                                                    width={40}
                                                                    height={40}
                                                                    className="h-10 w-10 rounded-lg object-cover"
                                                                />
                                                            </div>
                                                            <div className="ml-4">
                                                                <div className={`text-sm font-medium ${isInvalidSku ? 'text-red-600' : 'text-gray-900'}`}>
                                                                    #{item.skuID}
                                                                </div>
                                                                {skuDetail ? (
                                                                    <>
                                                                        <div className="text-sm font-medium text-gray-900">{skuDetail.name.en}</div>
                                                                        {skuDetail.name.ta && (
                                                                            <div className="text-sm text-gray-500">{skuDetail.name.ta}</div>
                                                                        )}
                                                                    </>
                                                                ) : !isInvalidSku ? (
                                                                    <div className="text-sm text-gray-500 italic">{t('account.orderDetail.items.loadingDetails', 'Loading...')}</div>
                                                                ) : (
                                                                    <div className="text-sm text-red-500 italic">{t('account.orderDetail.items.unavailable', 'Details unavailable')}</div>
                                                                )}
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                            {item.unit}
                                                        </span>
                                                    </td>
                                                    {quantityConfig.showOrdered && (
                                                        <td className="px-6 py-4 whitespace-nowrap">
                                                            <span className="text-sm font-medium text-gray-900">{item.quantity}</span>
                                                        </td>
                                                    )}
                                                    {quantityConfig.showPicked && (
                                                        <td className="px-6 py-4 whitespace-nowrap">
                                                            <span className={`text-sm font-medium px-2 py-1 rounded ${getQuantityDisplayStyle(item, 'picked')}`}>
                                                                {getQuantityValue(item, 'picked') !== null ? getQuantityValue(item, 'picked') : '-'}
                                                            </span>
                                                        </td>
                                                    )}
                                                    {quantityConfig.showDelivered && (
                                                        <td className="px-6 py-4 whitespace-nowrap">
                                                            <span className={`text-sm font-medium px-2 py-1 rounded ${getQuantityDisplayStyle(item, 'delivered')}`}>
                                                                {getQuantityValue(item, 'delivered') !== null ? getQuantityValue(item, 'delivered') : '-'}
                                                            </span>
                                                        </td>
                                                    )}
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <div className="text-sm text-gray-900">
                                                            <div className="font-medium">₹{item.price.toFixed(2)}</div>
                                                        </div>
                                                    </td>
                                                    {quantityConfig.showOrdered && (
                                                        <td className="px-6 py-4 whitespace-nowrap">
                                                            <span className="text-sm font-semibold text-green-600">
                                                                ₹{calculateTotal(item, 'ordered').toFixed(2)}
                                                            </span>
                                                        </td>
                                                    )}
                                                </tr>
                                            );
                                        })}
                                    </tbody>
                                    <tfoot className="bg-gray-50">
                                        <tr>
                                            <td colSpan={2} className="px-6 py-4 text-right text-sm font-medium text-gray-900">
                                                {t('account.orderDetail.items.orderTotal', 'Order Total')}
                                            </td>
                                            {quantityConfig.showOrdered && (
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <span className="text-sm font-bold text-green-600">
                                                        ₹{order.skuItems.reduce((total, item) => total + calculateTotal(item, 'ordered'), 0).toFixed(2)}
                                                    </span>
                                                </td>
                                            )}
                                            {quantityConfig.showPicked && (
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <span className="text-sm font-bold text-blue-600">
                                                        ₹{order.skuItems.reduce((total, item) => total + calculateTotal(item, 'picked'), 0).toFixed(2)}
                                                    </span>
                                                </td>
                                            )}
                                            {quantityConfig.showDelivered && (
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <span className="text-sm font-bold text-purple-600">
                                                        ₹{order.skuItems.reduce((total, item) => total + calculateTotal(item, 'delivered'), 0).toFixed(2)}
                                                    </span>
                                                </td>
                                            )}
                                            <td className="px-6 py-4 text-center text-xs text-gray-500">
                                                {t('account.orderDetail.items.price', 'Price')}
                                            </td>
                                            {quantityConfig.showOrdered && (
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <span className="text-lg font-bold text-green-600">
                                                        ₹{order.skuItems.reduce((total, item) => total + calculateTotal(item, 'ordered'), 0).toFixed(2)}
                                                    </span>
                                                </td>
                                            )}
                                        </tr>
                                    </tfoot>
                                </table>
                            )}
                        </div>
                    </CardContent>
                </Card>

                {/* Additional Information */}
                <Card>
                    <CardContent className="p-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-4">
                            {t('account.orderDetail.additional.title', 'Additional Information')}
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    {t('account.orderDetail.additional.deliveryInstructions', 'Delivery Instructions')}
                                </label>
                                <div className="min-h-[76px] px-3 py-2 border border-gray-200 rounded-md bg-gray-50">
                                    {order.metadata?.deliveryInstruction ? (
                                        <p className="text-sm text-gray-900">{order.metadata.deliveryInstruction}</p>
                                    ) : (
                                        <p className="text-sm text-gray-500 italic">
                                            {t('account.orderDetail.additional.noInstructions', 'No delivery instructions provided')}
                                        </p>
                                    )}
                                </div>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    {t('account.orderDetail.additional.orderComments', 'Order Comments')}
                                </label>
                                <div className="min-h-[76px] px-3 py-2 border border-gray-200 rounded-md bg-gray-50">
                                    {order.metadata?.orderComments ? (
                                        <p className="text-sm text-gray-900">{safeRender(order.metadata.orderComments)}</p>
                                    ) : (
                                        <p className="text-sm text-gray-500 italic">
                                            {t('account.orderDetail.additional.noComments', 'No order comments provided')}
                                        </p>
                                    )}
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </div>
    );
};

export default AccountOrderDetailPage; 
"use client";

import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { fetchMyOrders, getTodayDateRange, calculateOrderTotal } from '../../services/orderService';
import { useAuth } from '../../context/AuthContext';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { useTranslation } from 'react-i18next';
import Breadcrumbs from '../../components/Breadcrumbs';

const AccountOrdersPage: React.FC = () => {
    const { t } = useTranslation();
    const router = useRouter();
    const { userDetails } = useAuth();
    const { dateFrom, dateTo } = getTodayDateRange();

    const userId = userDetails?.id;

    const breadcrumbItems = [
        { name: t('account.breadcrumbs.home', 'Home'), href: '/' },
        { name: t('account.breadcrumbs.account', 'Account'), href: '/account' },
        { name: t('account.breadcrumbs.orders', "Today's Orders"), href: '/account/orders' },
    ];

    const {
        data: orders = [],
        isLoading,
        isError,
        error,
        refetch
    } = useQuery({
        queryKey: ['account-orders', dateFrom, dateTo, userId],
        queryFn: () => userId ? fetchMyOrders(dateFrom, dateTo, userId) : Promise.resolve([]),
        enabled: !!userId,
        staleTime: 30000,
        gcTime: 300000,
    });

    const handleOrderClick = (orderId: number) => {
        router.push(`/account/orders/${orderId}`);
    };

    const formatOrderTime = (createdAt: number | null) => {
        if (!createdAt) return t('account.orders.noTime', 'Time not available');
        const date = new Date(createdAt * 1000);
        return date.toLocaleTimeString('en-IN', { hour: '2-digit', minute: '2-digit' });
    };

    const OrderCard = ({ order }: { order: any }) => (
        <Card
            className="hover:shadow-lg transition-shadow cursor-pointer border-l-4 border-l-blue-500"
            onClick={() => handleOrderClick(order.id)}
        >
            <div className="p-4">
                <div className="flex items-center justify-between mb-2">
                    <span className="text-lg font-bold text-gray-900">#{order.id}</span>
                    <span className="text-sm font-medium text-blue-600">{formatOrderTime(order.createdAt)}</span>
                </div>

                <div className="flex items-center justify-between mb-2">
                    <Badge variant="outline" className="capitalize">{order.status.toLowerCase()}</Badge>
                    <span className="text-lg font-bold text-green-600">₹{calculateOrderTotal(order).toFixed(2)}</span>
                </div>

                <div className="text-sm text-gray-600 space-y-1">
                    <div>{order.endCustomerName || 'No customer name'} • {order.mobile || 'No mobile'}</div>
                    <div>{order.skuItems.length} items</div>
                </div>
            </div>
        </Card>
    );

    if (isLoading) {
        return (
            <div className="container mx-auto py-6 px-4 sm:px-6 lg:px-8">
                <Breadcrumbs items={breadcrumbItems} />
                <Skeleton className="h-10 w-full mb-4" />
                <Skeleton className="h-20 w-full mb-4" />
                <Skeleton className="h-20 w-full mb-4" />
            </div>
        );
    }

    if (isError) {
        return (
            <div className="container mx-auto py-6 px-4 sm:px-6 lg:px-8">
                <Breadcrumbs items={breadcrumbItems} />
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <h3 className="text-lg font-medium text-red-800 mb-2">
                        {t('account.orders.error.title', 'Failed to load orders')}
                    </h3>
                    <p className="text-red-600 mb-4">{error?.message || t('account.orders.error.generic', 'Something went wrong.')}</p>
                    <button
                        onClick={() => refetch()}
                        className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
                    >
                        {t('account.orders.error.retry', 'Retry')}
                    </button>
                </div>
            </div>
        );
    }

    return (
        <div className="container mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <Breadcrumbs items={breadcrumbItems} />

            <div className="mb-8">
                <h1 className="text-3xl font-bold text-gray-900 mb-2">{t('account.orders.title', "Today's Orders")}</h1>
                <p className="text-gray-600">{t('account.orders.subtitle', 'Orders you placed today')}</p>
            </div>

            {orders.length === 0 ? (
                <div className="text-center py-12">
                    <div className="mx-auto h-24 w-24 text-gray-400 mb-4">
                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                        </svg>
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">{t('account.orders.empty', 'No orders placed today')}</h3>
                    <p className="text-gray-600">{t('account.orders.emptyDesc', 'When you place orders, they will appear here.')}</p>
                </div>
            ) : (
                <div className="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
                    {orders.map((order) => (
                        <OrderCard key={order.id} order={order} />
                    ))}
                </div>
            )}
        </div>
    );
};

export default AccountOrdersPage; 
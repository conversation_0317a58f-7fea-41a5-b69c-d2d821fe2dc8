'use client';
import CategorySection from '@/app/components/CategorySection';
import { useQuery } from '@tanstack/react-query';
import { getOrderedCategories } from '@/app/services/categoryService';
import { Category } from '@/app/types/category';
import HomepageSkeleton from '@/app/components/common/HomepageSkeleton';
import { useTranslation } from 'react-i18next';
import i18n from '@/i18n';

export default function Home() {
  const { t } = useTranslation();

  const {
    data: categories,
    isLoading: isLoadingCategories,
    isError: isErrorCategories,
    error: categoriesError,
  } = useQuery<Category[], Error>({ // Specify types for data and error
    queryKey: ['categories'],
    queryFn: async () => {
      try {
        // Direct service call that fetches and returns sorted categories
        return await getOrderedCategories(); // Service method handles fetching and sorting (excludes inactive by default)
      } catch (error) {
        console.error('Error loading categories:', error);
        return [];
      }
    },
  });

  if (isLoadingCategories) {
    return <HomepageSkeleton />;
  }

  if (isErrorCategories) {
    return (
      <div className="py-6 px-4 text-center text-red-600">
        <p>{t('homepage.error.loadingCategories')} {categoriesError?.message || t('common.unknownError')}</p>
      </div>
    );
  }

  return (
    <div className="">
      {/* Product Sections */}
      <div className="mt-2 mb-20">
        {categories?.map((category, index) => (
          <CategorySection
            key={category.id}
            title={category.name[i18n.language] || category.name.en || ''}
            categoryId={String(category.id)}
            isFirstCategory={index === 0} // Preload first category
            maxSkusToShow={20} // Limit to 10 SKUs on homepage
            backgroundColor={category.background} // Pass background color if available
          />
        ))}
        {!categories || categories.length === 0 && (
          <p className="text-center text-gray-500">{t('homepage.noCategories')}</p>
        )}
      </div>
    </div>
  );
}
"use client";

import React, { useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';
import { AdminGuard } from '../../components/common/AdminGuard';
import DataTable, { Column } from '../components/DataTable';
import StatusBadge from '../components/StatusBadge';
import {
    getOrdersForAdmin,
    calculateOrderTotal,
    formatDeliveryDate
} from '../../services/orderService';
import { Order } from '../../types/order';
import { maskMobile } from '../../../lib/utils';
import { usePickerFilters } from '../../hooks/usePickerFilters';
import { useTranslation } from 'react-i18next';
import { logger } from '@/lib/logger';
import DatePicker from '../../components/common/DatePicker';
import { ClockIcon, ShoppingBagIcon, DevicePhoneMobileIcon, BuildingStorefrontIcon, ArrowPathIcon } from '@heroicons/react/24/outline';

const PickerPage: React.FC = () => {
    const { t } = useTranslation();

    // Use the picker filters hook for URL query parameter synchronization
    const { dateFrom, dateTo, updateDateFilter } = usePickerFilters();

    // Fetch orders based on date range
    const {
        data: orders = [],
        isLoading,
        isError,
        error,
        refetch,
        isFetching
    } = useQuery<Order[], Error>({
        queryKey: ['picker-orders', dateFrom, dateTo],
        queryFn: () => getOrdersForAdmin(dateFrom, dateTo),
        staleTime: 30000, // 30 seconds
        gcTime: 300000, // 5 minutes
        refetchOnMount: true,
        refetchOnWindowFocus: false,
    });

    // Filter to only show CONFIRMED orders and only with deliveryDate of today
    const confirmedOrders = useMemo(() => {
        const today = new Date();
        const todayDate = today.toISOString().split('T')[0];
        const ordersOfToday = orders.filter(order => {
            if (order.status !== 'CONFIRMED') return false;
            const orderDate = new Date(order.deliveryDate);
            const orderDateString = orderDate.toISOString().split('T')[0];
            return orderDateString === todayDate;
        });
        return ordersOfToday;
    }, [orders]);

    // Handle refresh button click
    const handleRefresh = () => {
        logger.debug('Picker: Manual refresh triggered', { dateFrom, dateTo });
        refetch();
    };

    // Handle row click - navigate to picker order details with returnFilters
    const handleRowClick = (order: Order) => {
        logger.debug('Picker order clicked', { orderId: order.id, dateFrom, dateTo });

        // Create returnFilters parameter to preserve current date filters
        const currentParams = new URLSearchParams();
        if (dateFrom) currentParams.set('dateFrom', dateFrom);
        if (dateTo) currentParams.set('dateTo', dateTo);

        const returnFilters = encodeURIComponent(currentParams.toString());
        window.location.href = `/admin/picker/${order.id}?returnFilters=${returnFilters}`;
    };

    // Define table columns with mobile masking
    const columns: Column<Order>[] = [
        {
            key: 'id',
            header: t('admin.orders.table.orderId'),
            width: '100px',
            render: (order) => `#${order.id}`
        },
        {
            key: 'status',
            header: t('admin.orders.table.status'),
            width: '120px',
            render: (order) => <StatusBadge status={order.status.toLowerCase()} />
        },
        {
            key: 'mobile',
            header: t('admin.orders.table.mobile'),
            width: '140px',
            render: (order) => maskMobile(order.mobile)
        },
        {
            key: 'itemsCount',
            header: t('admin.orders.table.itemsCount'),
            width: '100px',
            render: (order) => order.skuItems.length.toString()
        },
        {
            key: 'totalValue',
            header: t('admin.orders.table.totalValue'),
            width: '120px',
            render: (order) => `₹${calculateOrderTotal(order).toFixed(2)}`
        },
        {
            key: 'deliveryDate',
            header: t('admin.orders.table.deliveryDate'),
            width: '180px',
            render: (order) => formatDeliveryDate(order.deliveryDate)
        },
        {
            key: 'facilityKey',
            header: t('admin.orders.table.facility'),
            width: '120px',
            render: (order) => order.facilityKey || t('admin.orders.table.noFacility')
        }
    ];

    if (isError) {
        return (
            <AdminGuard requiredPermission="pickOrder">
                <div className="min-h-screen bg-gray-50 px-0 py-4 sm:px-0 lg:px-0">
                    <div className="max-w-7xl mx-auto">
                        <div className="flex flex-col items-center justify-center min-h-[50vh] text-center">
                            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                                <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                </svg>
                            </div>
                            <h3 className="text-lg font-medium text-gray-900 mb-2">
                                {t('admin.orders.error.title')}
                            </h3>
                            <p className="text-sm text-gray-600 mb-6">
                                {error?.message || t('admin.orders.error.generic')}
                            </p>
                            <button
                                onClick={() => refetch()}
                                className="inline-flex items-center justify-center px-4 py-3 sm:py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 min-h-[44px] touch-manipulation active:scale-95 transform transition-transform"
                            >
                                {t('admin.orders.error.retry')}
                            </button>
                        </div>
                    </div>
                </div>
            </AdminGuard>
        );
    }

    return (
        <AdminGuard requiredPermission="pickOrder">
            <div className="min-h-screen bg-gray-50 px-2 py-2 sm:px-2 lg:px-2">
                <div className="max-w-7xl mx-auto space-y-4 sm:space-y-6">
                    {/* Page Header - Mobile Optimized */}
                    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6">
                        <div>
                            <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900">
                                {t('admin.picker.title')}
                            </h1>
                            <p className="text-sm sm:text-base text-gray-600 mt-1">
                                {t('admin.picker.subtitle')}
                            </p>
                        </div>
                    </div>

                    {/* Date Range Filters - Mobile Optimized */}
                    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6">
                        <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-4">
                            {t('admin.picker.filters.title')}
                        </h3>

                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                            {/* Date From */}
                            <div>
                                <DatePicker
                                    label={t('admin.orders.filters.dateFrom')}
                                    value={dateFrom}
                                    onChange={(value) => updateDateFilter('dateFrom', value)}
                                    placeholder="dd/MM/yyyy"
                                />
                            </div>

                            {/* Date To */}
                            <div>
                                <DatePicker
                                    label={t('admin.orders.filters.dateTo')}
                                    value={dateTo}
                                    onChange={(value) => updateDateFilter('dateTo', value)}
                                    placeholder="dd/MM/yyyy"
                                />
                            </div>

                            {/* Refresh Button */}
                            <div className="flex items-end">
                                <button
                                    onClick={handleRefresh}
                                    disabled={isFetching}
                                    className="inline-flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed min-h-[42px]"
                                    title={t('common.refresh')}
                                >
                                    <ArrowPathIcon className={`h-4 w-4 mr-2 ${isFetching ? 'animate-spin' : ''}`} />
                                    {t('common.refresh')}
                                </button>
                            </div>
                        </div>

                        {/* Filter Summary */}
                        <div className="mt-4 text-sm text-gray-600">
                            {t('admin.picker.filters.summary', { count: confirmedOrders.length })}
                        </div>
                    </div>

                    {/* Orders Section - Dual Layout */}
                    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                        {isLoading ? (
                            <div className="p-4 sm:p-6">
                                <div className="text-center py-8 sm:py-12">
                                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                                    <p className="text-sm text-gray-600">{t('common.loading')}</p>
                                </div>
                            </div>
                        ) : confirmedOrders.length === 0 ? (
                            <div className="p-4 sm:p-6">
                                <div className="text-center py-8 sm:py-12">
                                    <svg className="w-12 h-12 mx-auto text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                    </svg>
                                    <p className="text-gray-500">{t('admin.picker.table.empty')}</p>
                                </div>
                            </div>
                        ) : (
                            <>
                                {/* Mobile Cards Layout */}
                                <div className="lg:hidden">
                                    <div className="px-4 py-3 border-b border-gray-200 bg-gray-50">
                                        <h3 className="text-sm font-medium text-gray-900">
                                            {t('admin.picker.table.confirmedOrders', { count: confirmedOrders.length })}
                                        </h3>
                                    </div>
                                    <div className="p-4 space-y-3">
                                        {confirmedOrders.map((order) => (
                                            <div
                                                key={order.id}
                                                onClick={() => handleRowClick(order)}
                                                className="bg-white border border-gray-200 rounded-lg p-4 cursor-pointer hover:bg-gray-50 touch-manipulation active:scale-95 transform transition-transform"
                                            >
                                                <div className="space-y-3">
                                                    {/* Order Header */}
                                                    <div className="flex items-center justify-between">
                                                        <div className="flex items-center space-x-2">
                                                            <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800">
                                                                #{order.id}
                                                            </span>
                                                            <StatusBadge status={order.status.toLowerCase()} />
                                                        </div>
                                                        <div className="text-right">
                                                            <div className="text-lg font-semibold text-gray-900">
                                                                ₹{calculateOrderTotal(order).toFixed(2)}
                                                            </div>
                                                        </div>
                                                    </div>

                                                    {/* Order Details */}
                                                    <div className="grid grid-cols-2 gap-3 text-sm">
                                                        <div className="flex items-center space-x-2">
                                                            <DevicePhoneMobileIcon className="h-4 w-4 text-gray-400 flex-shrink-0" />
                                                            <span className="text-gray-600 truncate">{maskMobile(order.mobile)}</span>
                                                        </div>
                                                        <div className="flex items-center space-x-2">
                                                            <ShoppingBagIcon className="h-4 w-4 text-gray-400 flex-shrink-0" />
                                                            <span className="text-gray-600">{order.skuItems.length} {t('admin.picker.table.itemsCount')}</span>
                                                        </div>
                                                        <div className="flex items-center space-x-2">
                                                            <ClockIcon className="h-4 w-4 text-gray-400 flex-shrink-0" />
                                                            <span className="text-gray-600 text-xs">{formatDeliveryDate(order.deliveryDate)}</span>
                                                        </div>
                                                        <div className="flex items-center space-x-2">
                                                            <BuildingStorefrontIcon className="h-4 w-4 text-gray-400 flex-shrink-0" />
                                                            <span className="text-gray-600 text-xs truncate">
                                                                {order.facilityKey || t('admin.orders.table.noFacility')}
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>

                                {/* Desktop Table Layout */}
                                <div className="hidden lg:block">
                                    <DataTable
                                        data={confirmedOrders}
                                        columns={columns}
                                        loading={isLoading}
                                        emptyMessage={t('admin.picker.table.empty')}
                                        onRowClick={handleRowClick}
                                        getItemId={(order) => order.id}
                                    />
                                </div>
                            </>
                        )}
                    </div>
                </div>
            </div>
        </AdminGuard>
    );
};

export default PickerPage; 
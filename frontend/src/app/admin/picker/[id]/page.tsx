"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { use<PERSON>ara<PERSON>, useRouter, useSearchParams } from 'next/navigation';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import Image from 'next/image';
import Modal from 'react-modal';
import { AdminGuard } from '../../../components/common/AdminGuard';
import { getOrderById, updateOrder } from '../../../services/orderService';
import { getSkuById } from '../../../services/skuService';
import { Order, UpdateOrderRequest, OrderItem } from '../../../types/order';
import { SKU } from '../../../types/sku';
import { maskMobile } from '../../../../lib/utils';
import { logger } from '@/lib/logger';
import { useTranslation } from 'react-i18next';
import {
    ArrowLeftIcon,
    CheckIcon,
    DevicePhoneMobileIcon,
    CalendarDaysIcon,
    BuildingStorefrontIcon,
    MinusIcon,
    PlusIcon
} from '@heroicons/react/24/outline';
import StatusBadge from '../../components/StatusBadge';

const PickerOrderDetailPage: React.FC = () => {
    const { t } = useTranslation();
    const params = useParams();
    const router = useRouter();
    const searchParams = useSearchParams();
    const queryClient = useQueryClient();
    const orderId = params.id as string;

    // Get return filters for navigation back
    const returnFilters = searchParams.get('returnFilters');

    // Helper function to navigate back to picker list with preserved filters
    const navigateBackToPicker = useCallback(() => {
        if (returnFilters) {
            const decodedFilters = decodeURIComponent(returnFilters);
            logger.debug('Navigating back to picker with filters', { decodedFilters });
            router.push(`/admin/picker?${decodedFilters}`);
        } else {
            router.push('/admin/picker');
        }
    }, [returnFilters, router]);

    const [isMarkingPicked, setIsMarkingPicked] = useState(false);
    const [skuDetails, setSkuDetails] = useState<Record<number, SKU>>({});
    const [showMarkPickedModal, setShowMarkPickedModal] = useState(false);
    const [pickedQuantities, setPickedQuantities] = useState<Record<number, number>>({});

    const getPickedQuantity = useCallback((item: OrderItem | undefined, index: number): number => {
        if (pickedQuantities[index] !== undefined) {
            return pickedQuantities[index];
        }
        if (item?.pickedQuantity !== undefined) {
            return item.pickedQuantity;
        }
        return item?.quantity || 0;
    }, [pickedQuantities]);

    const handlePickedQuantityChange = useCallback((index: number, value: string, maxQuantity: number) => {
        const numValue = parseFloat(value);
        // Only allow non-negative numbers that don't exceed the ordered quantity
        if (!isNaN(numValue) && numValue >= 0 && numValue <= maxQuantity) {
            setPickedQuantities(prev => ({
                ...prev,
                [index]: numValue
            }));
        } else if (value === '' || value === '.') {
            // Allow empty string for clearing the field or decimal point
            setPickedQuantities(prev => ({
                ...prev,
                [index]: 0
            }));
        }
        // If numValue exceeds maxQuantity, set it to maxQuantity
        else if (!isNaN(numValue) && numValue > maxQuantity) {
            setPickedQuantities(prev => ({
                ...prev,
                [index]: maxQuantity
            }));
        }
    }, []);

    // Fetch order details
    const {
        data: order,
        isLoading,
        isError,
        error
    } = useQuery<Order, Error>({
        queryKey: ['order', orderId],
        queryFn: () => getOrderById(parseInt(orderId)),
        staleTime: 30000, // 30 seconds
        gcTime: 300000, // 5 minutes
    });

    // Helper for mobile quantity adjustment
    const adjustQuantity = useCallback((index: number, change: number, maxQuantity: number) => {
        const currentQty = getPickedQuantity(order?.skuItems[index], index);
        const newQty = Math.max(0, Math.min(maxQuantity, currentQty + change));
        setPickedQuantities(prev => ({
            ...prev,
            [index]: newQty
        }));
    }, [getPickedQuantity, order?.skuItems]);

    // Helper function to construct updated SKU items with picked quantities
    const constructUpdatedSkuItems = useCallback((): OrderItem[] => {
        if (!order?.skuItems) return [];

        return order.skuItems.map((item, index) => ({
            ...item,
            pickedQuantity: getPickedQuantity(item, index)
        }));
    }, [order?.skuItems, getPickedQuantity]);

    // Mutation to mark order as picked
    const markPickedMutation = useMutation({
        mutationFn: () => {
            const updateData: UpdateOrderRequest = {
                id: parseInt(orderId),
                status: 'PICKED',
                skuItems: constructUpdatedSkuItems()
            };
            return updateOrder(updateData);
        },
        onMutate: () => {
            setIsMarkingPicked(true);
        },
        onSuccess: () => {
            // Update the cache
            queryClient.invalidateQueries({ queryKey: ['order', orderId] });
            queryClient.invalidateQueries({ queryKey: ['picker-orders'] });

            // Navigate back to picker list with preserved filters
            navigateBackToPicker();
        },
        onError: (_error) => {
            // Error is already handled by the UI state
            setIsMarkingPicked(false);
        },
        onSettled: () => {
            setIsMarkingPicked(false);
        }
    });

    // Function to fetch SKU details for missing SKUs
    const fetchMissingSkuDetails = useCallback(async (skuIds: number[]) => {
        // Filter out invalid SKU IDs (0, negative numbers, etc.)
        const validSkuIds = skuIds.filter(skuId => skuId && skuId > 0);
        const missingSkuIds = validSkuIds.filter(skuId => !skuDetails[skuId]);

        if (missingSkuIds.length === 0) return;

        for (const skuId of missingSkuIds) {
            try {
                const skuDetail = await getSkuById(skuId);
                if (skuDetail) {
                    setSkuDetails(prev => ({ ...prev, [skuId]: skuDetail }));
                }
            } catch {
                // Silently continue on error, the UI will show a loading state
                continue;
            }
        }
    }, [skuDetails]);

    // Fetch SKU details for initial order items
    useEffect(() => {
        if (order?.skuItems && order.skuItems.length > 0) {
            const skuIds = order.skuItems.map(item => item.skuID);
            fetchMissingSkuDetails(skuIds);
        }
    }, [order?.skuItems]);

    // Initialize picked quantities when order loads
    useEffect(() => {
        if (order?.skuItems && order.skuItems.length > 0) {
            const initialPickedQuantities: Record<number, number> = {};
            order.skuItems.forEach((item, index) => {
                // Use existing pickedQuantity if available, otherwise default to ordered quantity
                initialPickedQuantities[index] = item.pickedQuantity ?? item.quantity;
            });
            setPickedQuantities(initialPickedQuantities);
        }
    }, [order?.skuItems]);

    const handleMarkPicked = () => {
        setShowMarkPickedModal(true);
    };

    const handleConfirmMarkPicked = () => {
        markPickedMutation.mutate();
        setShowMarkPickedModal(false);
    };

    if (isLoading) {
        return (
            <AdminGuard requiredPermission="pickOrder">
                <div className="min-h-screen bg-gray-50 px-4 py-4 sm:px-6 lg:px-8">
                    <div className="max-w-7xl mx-auto">
                        <div className="animate-pulse space-y-4">
                            <div className="h-8 bg-gray-200 rounded w-1/4"></div>
                            <div className="h-32 bg-gray-200 rounded"></div>
                            <div className="h-64 bg-gray-200 rounded"></div>
                        </div>
                    </div>
                </div>
            </AdminGuard>
        );
    }

    if (isError || !order) {
        return (
            <AdminGuard requiredPermission="pickOrder">
                <div className="min-h-screen bg-gray-50 px-4 py-4 sm:px-6 lg:px-8">
                    <div className="max-w-7xl mx-auto">
                        <div className="flex flex-col items-center justify-center min-h-[50vh] text-center">
                            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                                <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                </svg>
                            </div>
                            <h3 className="text-lg font-medium text-gray-900 mb-2">
                                {t('admin.picker.detail.error.title')}
                            </h3>
                            <p className="text-sm text-gray-600 mb-6">
                                {error?.message || t('admin.picker.detail.error.message')}
                            </p>
                            <button
                                onClick={navigateBackToPicker}
                                className="inline-flex items-center justify-center px-4 py-3 sm:py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 min-h-[44px] touch-manipulation active:scale-95 transform transition-transform"
                            >
                                {t('admin.picker.detail.backToPicker')}
                            </button>
                        </div>
                    </div>
                </div>
            </AdminGuard>
        );
    }

    return (
        <AdminGuard requiredPermission="pickOrder">
            <div className="min-h-screen bg-gray-50 px-4 py-4 sm:px-6 lg:px-8">
                <div className="max-w-7xl mx-auto space-y-4 sm:space-y-6">
                    {/* Header with back button - Mobile Optimized */}
                    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6">
                        <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
                            <div className="flex items-center space-x-4">
                                <button
                                    onClick={navigateBackToPicker}
                                    className="flex items-center text-gray-600 hover:text-gray-900 transition-colors p-2 rounded-lg hover:bg-gray-100 touch-manipulation active:scale-95 transform"
                                >
                                    <ArrowLeftIcon className="h-5 w-5 mr-2" />
                                    <span className="text-sm">Back</span>
                                </button>
                                <div>
                                    <h1 className="text-lg sm:text-xl lg:text-2xl font-bold text-gray-900">
                                        {t('admin.picker.detail.title', { id: order.id })}
                                    </h1>
                                    <p className="text-sm text-gray-600 mt-1">{t('admin.picker.detail.subtitle')}</p>
                                </div>
                            </div>

                            {/* Pick Order Button - Mobile Priority */}
                            {order.status === 'CONFIRMED' && (
                                <button
                                    onClick={handleMarkPicked}
                                    disabled={isMarkingPicked}
                                    className="flex items-center justify-center px-4 py-3 sm:px-6 sm:py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors w-full sm:w-auto min-h-[44px] touch-manipulation active:scale-95 transform"
                                >
                                    <CheckIcon className="h-5 w-5 mr-2" />
                                    {isMarkingPicked ? t('admin.picker.detail.markPicked.processing') : t('admin.picker.detail.markPicked.button')}
                                </button>
                            )}
                        </div>
                    </div>

                    {/* Order Information - Mobile Responsive */}
                    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6">
                        <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-4">{t('admin.picker.detail.orderInfo.title')}</h3>

                        {/* Mobile: Stack vertically, Desktop: Grid */}
                        <div className="space-y-4 sm:grid sm:grid-cols-2 lg:grid-cols-4 sm:gap-4 sm:space-y-0">
                            <div className="flex items-center space-x-3 sm:block sm:space-x-0">
                                <div className="flex items-center space-x-2 sm:mb-2">
                                    <span className="text-sm font-medium text-gray-700">{t('admin.picker.detail.orderInfo.status')}</span>
                                </div>
                                <StatusBadge status={order.status.toLowerCase()} />
                            </div>

                            <div className="flex items-center space-x-3 sm:block sm:space-x-0">
                                <DevicePhoneMobileIcon className="h-4 w-4 text-gray-400 flex-shrink-0 sm:hidden" />
                                <div className="sm:mb-2">
                                    <span className="text-sm font-medium text-gray-700">{t('admin.picker.detail.orderInfo.mobile')}</span>
                                </div>
                                <p className="text-sm text-gray-900">{maskMobile(order.mobile)}</p>
                            </div>

                            <div className="flex items-center space-x-3 sm:block sm:space-x-0">
                                <CalendarDaysIcon className="h-4 w-4 text-gray-400 flex-shrink-0 sm:hidden" />
                                <div className="sm:mb-2">
                                    <span className="text-sm font-medium text-gray-700">{t('admin.picker.detail.orderInfo.deliveryDate')}</span>
                                </div>
                                <p className="text-sm text-gray-900">
                                    {order.deliveryDate ? new Date(order.deliveryDate).toLocaleDateString() : t('admin.picker.detail.orderInfo.notSet')}
                                </p>
                            </div>

                            <div className="flex items-center space-x-3 sm:block sm:space-x-0">
                                <BuildingStorefrontIcon className="h-4 w-4 text-gray-400 flex-shrink-0 sm:hidden" />
                                <div className="sm:mb-2">
                                    <span className="text-sm font-medium text-gray-700">{t('admin.picker.detail.orderInfo.facility')}</span>
                                </div>
                                <p className="text-sm text-gray-900">{order.facilityKey || t('admin.picker.detail.orderInfo.noFacility')}</p>
                            </div>
                        </div>
                    </div>

                    {/* Picking Summary - Mobile Responsive */}
                    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6">
                        <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-4">{t('admin.picker.detail.summary.title')}</h3>
                        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-6">
                            <div className="text-center bg-blue-50 rounded-lg p-4">
                                <div className="text-xl sm:text-2xl font-bold text-blue-600">
                                    {order.skuItems.reduce((total, item) => total + item.quantity, 0)}
                                </div>
                                <div className="text-xs sm:text-sm text-gray-600 mt-1">{t('admin.picker.detail.summary.totalOrdered')}</div>
                            </div>
                            <div className="text-center bg-green-50 rounded-lg p-4">
                                <div className="text-xl sm:text-2xl font-bold text-green-600">
                                    {order.skuItems.reduce((total, item, index) => total + getPickedQuantity(item, index), 0)}
                                </div>
                                <div className="text-xs sm:text-sm text-gray-600 mt-1">{t('admin.picker.detail.summary.totalPicked')}</div>
                            </div>
                            <div className="text-center bg-orange-50 rounded-lg p-4">
                                <div className={`text-xl sm:text-2xl font-bold ${order.skuItems.reduce((total, item, index) => total + getPickedQuantity(item, index), 0) ===
                                    order.skuItems.reduce((total, item) => total + item.quantity, 0)
                                    ? 'text-green-600' : 'text-orange-600'
                                    }`}>
                                    {Math.abs(order.skuItems.reduce((total, item, index) => total + getPickedQuantity(item, index), 0) -
                                        order.skuItems.reduce((total, item) => total + item.quantity, 0))}
                                </div>
                                <div className="text-xs sm:text-sm text-gray-600 mt-1">{t('admin.picker.detail.summary.difference')}</div>
                            </div>
                        </div>
                    </div>

                    {/* Order Items - Dual Layout */}
                    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                        <div className="px-4 py-4 sm:px-6 border-b border-gray-200">
                            <div>
                                <h3 className="text-base sm:text-lg font-medium text-gray-900">
                                    {t('admin.picker.detail.items.title')} ({order.skuItems.length})
                                </h3>
                                <p className="text-sm text-gray-500 mt-1">
                                    {t('admin.picker.detail.items.subtitle')}
                                </p>
                            </div>
                        </div>

                        {order.skuItems.length === 0 ? (
                            <div className="p-4 sm:p-6">
                                <div className="text-center py-8 sm:py-12">
                                    <div className="h-12 w-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                        <svg className="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                                        </svg>
                                    </div>
                                    <p className="text-gray-500 mb-2">{t('admin.picker.detail.items.empty')}</p>
                                </div>
                            </div>
                        ) : (
                            <>
                                {/* Mobile Cards Layout */}
                                <div className="lg:hidden">
                                    <div className="p-4 space-y-4">
                                        {order.skuItems.map((item, index) => {
                                            const skuDetail = skuDetails[item.skuID];
                                            const isInvalidSku = !item.skuID || item.skuID <= 0;
                                            const pickedQty = getPickedQuantity(item, index);
                                            const isDifferent = pickedQty !== item.quantity;

                                            return (
                                                <div key={index} className={`border border-gray-200 rounded-lg p-4 ${isDifferent ? 'bg-yellow-50 border-yellow-200' : 'bg-white'}`}>
                                                    <div className="space-y-3">
                                                        {/* Product Info */}
                                                        <div className="flex items-start space-x-3">
                                                            <Image
                                                                src={skuDetail?.imageUrl || '/placeholder-product.png'}
                                                                alt={skuDetail?.name?.en || `SKU ${item.skuID}`}
                                                                width={60}
                                                                height={60}
                                                                className="h-15 w-15 rounded-lg object-cover flex-shrink-0"
                                                            />
                                                            <div className="flex-1 min-w-0">
                                                                <div className="flex items-center space-x-2 mb-1">
                                                                    <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800">
                                                                        #{item.skuID}
                                                                    </span>
                                                                    {isInvalidSku && (
                                                                        <span className="inline-flex items-center px-2 py-1 rounded text-xs bg-red-100 text-red-800">
                                                                            {t('admin.picker.detail.items.invalidSku')}
                                                                        </span>
                                                                    )}
                                                                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                                        {item.unit}
                                                                    </span>
                                                                </div>
                                                                {skuDetail ? (
                                                                    <>
                                                                        <h4 className="text-sm font-medium text-gray-900 mb-1">{skuDetail.name.en}</h4>
                                                                        {skuDetail.name.ta && (
                                                                            <p className="text-sm text-gray-500">{skuDetail.name.ta}</p>
                                                                        )}
                                                                    </>
                                                                ) : !isInvalidSku ? (
                                                                    <p className="text-sm text-gray-500 italic">{t('common.loading')}</p>
                                                                ) : (
                                                                    <p className="text-sm text-red-500 italic">{t('admin.picker.detail.items.skuUnavailable')}</p>
                                                                )}
                                                            </div>
                                                        </div>

                                                        {/* Quantity Controls - Mobile Optimized */}
                                                        <div className="grid grid-cols-2 gap-4">
                                                            <div className="bg-gray-50 rounded-lg p-3">
                                                                <div className="text-xs text-gray-600 mb-1">{t('admin.picker.detail.items.orderedQty')}</div>
                                                                <div className="text-lg font-semibold text-gray-900">{item.quantity}</div>
                                                            </div>
                                                            <div className="bg-blue-50 rounded-lg p-3">
                                                                <div className="text-xs text-gray-600 mb-1">{t('admin.picker.detail.items.pickedQty')}</div>
                                                                <div className="flex items-center space-x-2">
                                                                    <button
                                                                        onClick={() => adjustQuantity(index, -1, item.quantity)}
                                                                        className="w-8 h-8 rounded-full bg-white border border-gray-300 flex items-center justify-center hover:bg-gray-50 touch-manipulation active:scale-95 transform transition-transform"
                                                                        disabled={pickedQty <= 0}
                                                                    >
                                                                        <MinusIcon className="h-4 w-4 text-gray-600" />
                                                                    </button>
                                                                    <input
                                                                        type="number"
                                                                        min="0"
                                                                        max={item.quantity}
                                                                        value={Math.floor(pickedQty)}
                                                                        onChange={(e) => handlePickedQuantityChange(index, e.target.value, item.quantity)}
                                                                        className="w-16 px-2 py-1 text-center text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                                                    />
                                                                    <button
                                                                        onClick={() => adjustQuantity(index, 1, item.quantity)}
                                                                        className="w-8 h-8 rounded-full bg-white border border-gray-300 flex items-center justify-center hover:bg-gray-50 touch-manipulation active:scale-95 transform transition-transform"
                                                                        disabled={pickedQty >= item.quantity}
                                                                    >
                                                                        <PlusIcon className="h-4 w-4 text-gray-600" />
                                                                    </button>
                                                                </div>
                                                                {pickedQty > item.quantity && (
                                                                    <div className="text-xs text-red-600 mt-1">
                                                                        Max: {item.quantity}
                                                                    </div>
                                                                )}
                                                            </div>
                                                        </div>

                                                        {/* Price Information */}
                                                        <div className="flex justify-between items-center pt-2 border-t border-gray-200">
                                                            <div className="text-sm text-gray-600">
                                                                {t('admin.picker.detail.items.unitPrice')}: ₹{item.price.toFixed(2)}
                                                            </div>
                                                            <div className="text-lg font-semibold text-green-600">
                                                                ₹{(item.price * pickedQty).toFixed(2)}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            );
                                        })}

                                        {/* Mobile Total */}
                                        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                                            <div className="flex justify-between items-center">
                                                <span className="text-sm font-medium text-gray-900">
                                                    {t('admin.picker.detail.items.orderTotal')}
                                                </span>
                                                <span className="text-lg font-bold text-green-600">
                                                    ₹{order.skuItems.reduce((total, item, index) => {
                                                        const pickedQty = getPickedQuantity(item, index);
                                                        return total + (pickedQty * item.price);
                                                    }, 0).toFixed(2)}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {/* Desktop Table Layout */}
                                <div className="hidden lg:block overflow-x-auto">
                                    <table className="min-w-full divide-y divide-gray-200">
                                        <thead className="bg-gray-50">
                                            <tr>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    {t('admin.picker.detail.items.product')}
                                                </th>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    {t('admin.picker.detail.items.unit')}
                                                </th>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    {t('admin.picker.detail.items.orderedQty')}
                                                </th>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    {t('admin.picker.detail.items.pickedQty')}
                                                </th>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    {t('admin.picker.detail.items.price')}
                                                </th>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    {t('admin.picker.detail.items.total')}
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody className="bg-white divide-y divide-gray-200">
                                            {order.skuItems.map((item, index) => {
                                                const skuDetail = skuDetails[item.skuID];
                                                const isInvalidSku = !item.skuID || item.skuID <= 0;
                                                const pickedQty = getPickedQuantity(item, index);
                                                const isDifferent = pickedQty !== item.quantity;

                                                return (
                                                    <tr key={index} className={`hover:bg-gray-50 ${isDifferent ? 'bg-yellow-50' : ''}`}>
                                                        <td className="px-6 py-4">
                                                            <div className="flex items-center">
                                                                <div className="h-10 w-10 flex-shrink-0">
                                                                    <Image
                                                                        src={skuDetail?.imageUrl || '/placeholder-product.png'}
                                                                        alt={skuDetail?.name?.en || `SKU ${item.skuID}`}
                                                                        width={40}
                                                                        height={40}
                                                                        className="h-10 w-10 rounded-lg object-cover"
                                                                    />
                                                                </div>
                                                                <div className="ml-4">
                                                                    <div className={`text-sm font-medium ${isInvalidSku ? 'text-red-600' : 'text-gray-900'}`}>
                                                                        #{item.skuID}
                                                                        {isInvalidSku && (
                                                                            <span className="ml-2 text-xs bg-red-100 text-red-800 px-2 py-1 rounded">
                                                                                Invalid SKU
                                                                            </span>
                                                                        )}
                                                                    </div>
                                                                    {skuDetail ? (
                                                                        <>
                                                                            <div className="text-sm font-medium text-gray-900">{skuDetail.name.en}</div>
                                                                            {skuDetail.name.ta && (
                                                                                <div className="text-sm text-gray-500">{skuDetail.name.ta}</div>
                                                                            )}
                                                                        </>
                                                                    ) : !isInvalidSku ? (
                                                                        <div className="text-sm text-gray-500 italic">Loading SKU details...</div>
                                                                    ) : (
                                                                        <div className="text-sm text-red-500 italic">SKU details unavailable</div>
                                                                    )}
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td className="px-6 py-4 whitespace-nowrap">
                                                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                                {item.unit}
                                                            </span>
                                                        </td>
                                                        <td className="px-6 py-4 whitespace-nowrap">
                                                            <span className="text-sm font-medium text-gray-900">{item.quantity}</span>
                                                        </td>
                                                        <td className="px-6 py-4 whitespace-nowrap">
                                                            <div className="flex flex-col">
                                                                <input
                                                                    type="number"
                                                                    min="0"
                                                                    max={item.quantity}
                                                                    step="1"
                                                                    value={Math.floor(pickedQty)}
                                                                    onKeyDown={(e) => {
                                                                        if (e.key === '.' || e.key === '-' || e.key === 'e') {
                                                                            e.preventDefault();
                                                                        }
                                                                    }}
                                                                    onChange={(e) => handlePickedQuantityChange(index, String(Math.floor(Number(e.target.value))), item.quantity)}
                                                                    className={`w-20 px-2 py-1 text-sm border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${pickedQty > item.quantity
                                                                        ? 'border-red-300 bg-red-50'
                                                                        : pickedQty === item.quantity
                                                                            ? 'border-green-300 bg-green-50'
                                                                            : 'border-gray-300'
                                                                        }`}
                                                                    aria-label={`Picked quantity for ${skuDetail?.name?.en || `SKU ${item.skuID}`}`}
                                                                />
                                                                {pickedQty > item.quantity && (
                                                                    <span className="text-xs text-red-600 mt-1">
                                                                        Max: {item.quantity}
                                                                    </span>
                                                                )}
                                                            </div>
                                                        </td>
                                                        <td className="px-6 py-4 whitespace-nowrap">
                                                            <div className="text-sm text-gray-900">
                                                                <div className="font-medium">₹{item.price.toFixed(2)}</div>
                                                            </div>
                                                        </td>
                                                        <td className="px-6 py-4 whitespace-nowrap">
                                                            <span className="text-sm font-semibold text-green-600">
                                                                ₹{(item.price * pickedQty).toFixed(2)}
                                                            </span>
                                                        </td>
                                                    </tr>
                                                );
                                            })}
                                        </tbody>
                                        <tfoot className="bg-gray-50">
                                            <tr>
                                                <td colSpan={5} className="px-6 py-4 text-right text-sm font-medium text-gray-900">
                                                    {t('admin.picker.detail.items.orderTotal')}
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <span className="text-lg font-bold text-green-600">
                                                        ₹{order.skuItems.reduce((total, item, index) => {
                                                            const pickedQty = getPickedQuantity(item, index);
                                                            return total + (pickedQty * item.price);
                                                        }, 0).toFixed(2)}
                                                    </span>
                                                </td>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </>
                        )}
                    </div>

                    {/* Picking Instructions - Mobile Optimized */}
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 sm:p-6">
                        <h4 className="font-medium text-blue-900 mb-3">{t('admin.picker.detail.instructions.title')}</h4>
                        <ul className="text-sm text-blue-800 space-y-2">
                            <li className="flex items-start space-x-2">
                                <span className="flex-shrink-0 w-1.5 h-1.5 bg-blue-600 rounded-full mt-2"></span>
                                <span>{t('admin.picker.detail.instructions.verifyItems')}</span>
                            </li>
                            <li className="flex items-start space-x-2">
                                <span className="flex-shrink-0 w-1.5 h-1.5 bg-blue-600 rounded-full mt-2"></span>
                                <span>{t('admin.picker.detail.instructions.checkQuantity')}</span>
                            </li>
                            <li className="flex items-start space-x-2">
                                <span className="flex-shrink-0 w-1.5 h-1.5 bg-blue-600 rounded-full mt-2"></span>
                                <span>{t('admin.picker.detail.instructions.checkCondition')}</span>
                            </li>
                            <li className="flex items-start space-x-2">
                                <span className="flex-shrink-0 w-1.5 h-1.5 bg-blue-600 rounded-full mt-2"></span>
                                <span>{t('admin.picker.detail.instructions.markWhenComplete')}</span>
                            </li>
                        </ul>
                    </div>

                    {/* Mark Picked Confirmation Modal - Mobile Responsive */}
                    <Modal
                        isOpen={showMarkPickedModal}
                        onRequestClose={() => setShowMarkPickedModal(false)}
                        contentLabel={t('admin.picker.detail.markPicked.modalTitle')}
                        className="relative top-4 sm:top-20 mx-4 sm:mx-auto p-4 sm:p-6 border w-auto sm:w-11/12 max-w-md shadow-lg rounded-lg bg-white focus:outline-none max-h-[90vh] overflow-hidden flex flex-col"
                        shouldCloseOnOverlayClick={true}
                        shouldCloseOnEsc={true}
                    >
                        <div className="flex items-center justify-between mb-4">
                            <h3 className="text-lg font-medium text-gray-900">
                                {t('admin.picker.detail.markPicked.modalTitle')}
                            </h3>
                            <button
                                onClick={() => setShowMarkPickedModal(false)}
                                className="text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded p-1 touch-manipulation"
                            >
                                <span className="sr-only">{t('common.close')}</span>
                                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>

                        <div className="mb-6">
                            <p className="text-sm text-gray-600 mb-3">
                                {t('admin.picker.detail.markPicked.modalMessage')}
                            </p>
                            <div className="bg-green-50 border border-green-200 rounded-md p-3">
                                <div className="flex">
                                    <div className="flex-shrink-0">
                                        <CheckIcon className="h-5 w-5 text-green-400" />
                                    </div>
                                    <div className="ml-3">
                                        <p className="text-sm text-green-700">
                                            {t('admin.picker.detail.markPicked.modalNote')}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Mobile-optimized modal actions */}
                        <div className="flex flex-col space-y-3 sm:flex-row sm:justify-end sm:space-y-0 sm:space-x-3">
                            <button
                                onClick={() => setShowMarkPickedModal(false)}
                                disabled={markPickedMutation.isPending}
                                className="px-4 py-3 sm:py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 min-h-[44px] sm:min-h-0 touch-manipulation active:scale-95 transform transition-transform"
                            >
                                {t('admin.actions.cancel')}
                            </button>
                            <button
                                onClick={handleConfirmMarkPicked}
                                disabled={markPickedMutation.isPending}
                                className="px-4 py-3 sm:py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-green-500 min-h-[44px] sm:min-h-0 touch-manipulation active:scale-95 transform transition-transform"
                            >
                                {markPickedMutation.isPending ? t('admin.picker.detail.markPicked.processing') : t('admin.picker.detail.markPicked.confirmButton')}
                            </button>
                        </div>
                    </Modal>
                </div>
            </div>
        </AdminGuard>
    );
};

export default PickerOrderDetailPage;
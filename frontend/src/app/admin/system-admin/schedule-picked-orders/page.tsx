"use client";

import React, { useMemo, useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useQuery } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';
import { toast } from 'react-toastify';
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";
import { AdminGuard } from "../../../components/common/AdminGuard";
import DataTable, { Column } from "../../components/DataTable";
import StatusBadge from "../../components/StatusBadge";
import { ChevronRightIcon, HomeIcon, ArrowPathIcon, CalendarDaysIcon, CheckCircleIcon } from '@heroicons/react/24/outline';
import {
    getOrdersForAdmin,
    getYesterdayDateRange,
    calculateOrderTotal,
    formatDeliveryDate,
    scheduleOrdersForDelivery
} from "../../../services/orderService";
import { Order } from "../../../types/order";
import { logger } from '@/lib/logger';
import { cn } from "@/lib/utils";

const SchedulePickedOrdersPage: React.FC = () => {
    const { t } = useTranslation();
    const router = useRouter();
    const [isScheduling, setIsScheduling] = useState(false);

    // Get yesterday's date range
    const yesterdayRange = useMemo(() => getYesterdayDateRange(), []);

    // Fetch orders for yesterday
    const {
        data: allOrders = [],
        isLoading,
        isError,
        error,
        refetch,
        isFetching
    } = useQuery<Order[], Error>({
        queryKey: ['admin-picked-orders', yesterdayRange.dateFrom, yesterdayRange.dateTo],
        queryFn: () => getOrdersForAdmin(yesterdayRange.dateFrom, yesterdayRange.dateTo),
        staleTime: 30000, // 30 seconds
        gcTime: 300000, // 5 minutes
        refetchOnMount: true,
        refetchOnWindowFocus: false,
    });

    // Filter orders to show only "PICKED" status
    const pickedOrders = useMemo(() => {
        const today = new Date();
        const todayDateString = today.toISOString().split('T')[0];
        return allOrders.filter(order => order.status === 'PICKED' && order.deliveryDate?.split('T')[0] === todayDateString);
    }, [allOrders]);



    // Handle refresh
    const handleRefresh = () => {
        logger.debug('Schedule Picked Orders: Manual refresh triggered');
        refetch();
    };

    // Handle schedule orders
    const handleScheduleOrders = async () => {
        setIsScheduling(true);
        try {
            logger.debug('Schedule Picked Orders: Scheduling orders', { count: pickedOrders.length });

            const successMessage = await scheduleOrdersForDelivery();

            // Show success toast
            toast.success(successMessage, {
                position: "top-right",
                autoClose: 3000,
            });

            // Refresh the query to get latest data
            refetch();

        } catch (error) {
            // Show error toast
            const errorMessage = typeof error === 'string' ? error :
                (error as any)?.message || 'Failed to schedule orders';
            toast.error(errorMessage, {
                position: "top-right",
                autoClose: 5000,
            });
        } finally {
            setIsScheduling(false);
        }
    };

    // Handle row click - navigate to order details
    const handleRowClick = (order: Order) => {
        logger.debug('Schedule Picked Orders: Order clicked', { orderId: order.id });
        router.push(`/admin/orders/${order.id}`);
    };

    // Define table columns
    const columns: Column<Order>[] = useMemo(() => [
        {
            key: 'id',
            header: t('admin.systemAdmin.schedulePickedOrders.table.orderId', 'Order ID'),
            width: '100px',
            render: (order) => `#${order.id}`,
            sortable: true,
            getSortValue: (order) => order.id
        },
        {
            key: 'status',
            header: t('admin.systemAdmin.schedulePickedOrders.table.status', 'Status'),
            width: '120px',
            render: (order) => <StatusBadge status={order.status.toLowerCase()} />,
            sortable: true,
            getSortValue: (order) => order.status
        },
        {
            key: 'mobile',
            header: t('admin.systemAdmin.schedulePickedOrders.table.mobile', 'Mobile'),
            width: '140px',
            render: (order) => order.mobile || t('admin.systemAdmin.schedulePickedOrders.table.noMobile', 'No Mobile'),
            sortable: true,
            getSortValue: (order) => order.mobile || ''
        },
        {
            key: 'endCustomerName',
            header: t('admin.systemAdmin.schedulePickedOrders.table.endCustomerName', 'Customer Name'),
            width: '180px',
            render: (order) => order.endCustomerName || t('admin.systemAdmin.schedulePickedOrders.table.noCustomerName', 'No Customer Name'),
            sortable: true,
            getSortValue: (order) => order.endCustomerName || ''
        },
        {
            key: 'itemsCount',
            header: t('admin.systemAdmin.schedulePickedOrders.table.itemsCount', 'Items'),
            width: '100px',
            render: (order) => order.skuItems.length.toString(),
            sortable: true,
            getSortValue: (order) => order.skuItems.length
        },
        {
            key: 'totalValue',
            header: t('admin.systemAdmin.schedulePickedOrders.table.totalValue', 'Total Value'),
            width: '120px',
            render: (order) => `₹${calculateOrderTotal(order).toFixed(2)}`,
            sortable: true,
            getSortValue: (order) => calculateOrderTotal(order)
        },
        {
            key: 'deliveryDate',
            header: t('admin.systemAdmin.schedulePickedOrders.table.deliveryDate', 'Delivery Date'),
            width: '180px',
            render: (order) => formatDeliveryDate(order.deliveryDate),
            sortable: true,
            getSortValue: (order) => order.deliveryDate ? new Date(order.deliveryDate).getTime() : 0
        },
        {
            key: 'facilityKey',
            header: t('admin.systemAdmin.schedulePickedOrders.table.facility', 'Facility'),
            width: '120px',
            render: (order) => order.facilityKey || t('admin.systemAdmin.schedulePickedOrders.table.noFacility', 'No Facility'),
            sortable: true,
            getSortValue: (order) => order.facilityKey || ''
        }
    ], [t]);

    return (
        <AdminGuard requiredPermission="systemAdmin">
            <div className="space-y-6">
                {/* Breadcrumb Navigation */}
                <nav className="flex" aria-label="Breadcrumb">
                    <ol className="flex items-center space-x-4">
                        <li>
                            <Link href="/admin" className="text-gray-400 hover:text-gray-500">
                                <HomeIcon className="flex-shrink-0 h-5 w-5" />
                                <span className="sr-only">{t('admin.systemAdmin.breadcrumbs.home', 'Admin')}</span>
                            </Link>
                        </li>
                        <li>
                            <div className="flex items-center">
                                <ChevronRightIcon className="flex-shrink-0 h-5 w-5 text-gray-400" />
                                <Link
                                    href="/admin/system-admin"
                                    className="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700"
                                >
                                    {t('admin.systemAdmin.breadcrumbs.systemAdmin', 'System Administration')}
                                </Link>
                            </div>
                        </li>
                        <li>
                            <div className="flex items-center">
                                <ChevronRightIcon className="flex-shrink-0 h-5 w-5 text-gray-400" />
                                <span className="ml-4 text-sm font-medium text-gray-900">
                                    {t('admin.systemAdmin.schedulePickedOrders.title', 'Schedule Picked Orders')}
                                </span>
                            </div>
                        </li>
                    </ol>
                </nav>

                {/* Page Header with subtle warning indicator */}
                <div className="border-l-4 border-amber-200 pl-4">
                    <h1 className="text-3xl font-bold tracking-tight text-foreground">
                        {t('admin.systemAdmin.schedulePickedOrders.title', 'Schedule Picked Orders')}
                    </h1>
                    <p className="text-muted-foreground mt-2">
                        {t('admin.systemAdmin.schedulePickedOrders.subtitle', 'Automate and schedule picked order workflows')}
                    </p>
                </div>

                {/* Date Filter Info */}
                <Alert>
                    <CalendarDaysIcon className="h-4 w-4" />
                    <AlertDescription>
                        {t('admin.systemAdmin.schedulePickedOrders.description', 'View and schedule all orders that have been picked yesterday')}
                        {' '}
                        <Badge variant="outline" className="ml-2">
                            {t('admin.systemAdmin.schedulePickedOrders.filters.yesterday', 'Created Yesterday')}: {yesterdayRange.dateFrom}
                        </Badge>
                    </AlertDescription>
                </Alert>

                {/* Actions Bar */}
                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-4">
                                <Badge variant="secondary" className="text-sm">
                                    {pickedOrders.length} {t('admin.systemAdmin.schedulePickedOrders.filters.pickedStatus', 'Picked Orders')}
                                </Badge>
                                {isLoading && (
                                    <Badge variant="outline" className="text-sm">
                                        {t('admin.systemAdmin.schedulePickedOrders.table.loading', 'Loading...')}
                                    </Badge>
                                )}
                            </div>

                            <div className="flex items-center space-x-2">
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={handleRefresh}
                                    disabled={isFetching}
                                    className="min-w-[100px]"
                                >
                                    <ArrowPathIcon className={cn("h-4 w-4 mr-2", isFetching && "animate-spin")} />
                                    {isFetching ? t('admin.systemAdmin.schedulePickedOrders.buttons.refreshing', 'Refreshing...') : t('admin.systemAdmin.schedulePickedOrders.buttons.refresh', 'Refresh')}
                                </Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Orders Table */}
                {isLoading ? (
                    <Card>
                        <CardContent className="p-6">
                            <div className="space-y-4">
                                <Skeleton className="h-8 w-full" />
                                <Skeleton className="h-8 w-full" />
                                <Skeleton className="h-8 w-full" />
                                <Skeleton className="h-8 w-full" />
                                <Skeleton className="h-8 w-full" />
                            </div>
                        </CardContent>
                    </Card>
                ) : isError ? (
                    <Card>
                        <CardContent className="p-6">
                            <Alert>
                                <AlertDescription>
                                    {t('admin.orders.error.generic', 'Failed to load orders. Please try again.')}
                                    <br />
                                    {error?.message}
                                </AlertDescription>
                            </Alert>
                            <Button onClick={handleRefresh} className="mt-4">
                                {t('admin.orders.error.retry', 'Retry')}
                            </Button>
                        </CardContent>
                    </Card>
                ) : (
                    <DataTable
                        data={pickedOrders}
                        columns={columns}
                        loading={isLoading}
                        emptyMessage={t('admin.systemAdmin.schedulePickedOrders.table.empty', 'No picked orders found for yesterday')}
                        getItemId={(order) => order.id}
                        onRowClick={handleRowClick}
                    />
                )}

                {/* Schedule Orders Button - Bottom Placement */}
                {!isLoading && !isError && pickedOrders.length > 0 && (
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex flex-col items-center space-y-4">
                                <div className="text-center">
                                    <h3 className="text-lg font-medium text-foreground">
                                        Ready to Schedule
                                    </h3>
                                    <p className="text-sm text-muted-foreground">
                                        {pickedOrders.length} orders are ready for delivery scheduling
                                    </p>
                                </div>
                                <Button
                                    onClick={handleScheduleOrders}
                                    disabled={isScheduling}
                                    size="lg"
                                    className="min-w-[200px]"
                                >
                                    {isScheduling ? (
                                        <>
                                            <ArrowPathIcon className="h-5 w-5 mr-2 animate-spin" />
                                            Scheduling...
                                        </>
                                    ) : (
                                        <>
                                            <CheckCircleIcon className="h-5 w-5 mr-2" />
                                            {t('admin.systemAdmin.schedulePickedOrders.buttons.scheduleOrders', 'Schedule Orders for Delivery')}
                                        </>
                                    )}
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                )}
            </div>
        </AdminGuard>
    );
};

export default SchedulePickedOrdersPage; 
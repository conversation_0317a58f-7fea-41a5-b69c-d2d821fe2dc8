"use client";

import React from 'react';
import Link from 'next/link';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { AdminGuard } from "../../components/common/AdminGuard";

const SystemAdminPage: React.FC = () => {
    const { t } = useTranslation();

    return (
        <AdminGuard requiredPermission="systemAdmin">
            <div className="space-y-6">
                {/* Page Header with subtle warning indicator */}
                <div className="border-l-4 border-amber-200 pl-4">
                    <h1 className="text-3xl font-bold tracking-tight text-foreground">
                        {t('admin.systemAdmin.title', 'System Administration')}
                    </h1>
                    <p className="text-muted-foreground mt-2">
                        {t('admin.systemAdmin.description', 'High-privilege administrative functions')}
                    </p>
                </div>

                {/* Order Management Section */}
                <div className="space-y-4">
                    <h2 className="text-xl font-semibold text-foreground">
                        {t('admin.systemAdmin.orderManagement.title', 'Order Management')}
                    </h2>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {/* Schedule Picked Orders Card */}
                        <Link href="/admin/system-admin/schedule-picked-orders">
                            <Card className="border-amber-100 hover:border-amber-200 transition-colors cursor-pointer h-full">
                                <CardHeader>
                                    <CardTitle className="text-lg">
                                        {t('admin.systemAdmin.orderManagement.schedulePickedOrders', 'Schedule Picked Orders')}
                                    </CardTitle>
                                </CardHeader>
                            </Card>
                        </Link>

                        {/* Reassign Order Facilities Card */}
                        <Link href="/admin/system-admin/reassign-order-facilities">
                            <Card className="border-amber-100 hover:border-amber-200 transition-colors cursor-pointer h-full">
                                <CardHeader>
                                    <CardTitle className="text-lg">
                                        {t('admin.systemAdmin.orderManagement.reassignOrderFacilities', 'Reassign Order Facilities')}
                                    </CardTitle>
                                </CardHeader>
                            </Card>
                        </Link>

                        {/* Change Order Delivery Date Card */}
                        <Link href="/admin/system-admin/change-delivery-date">
                            <Card className="border-amber-100 hover:border-amber-200 transition-colors cursor-pointer h-full">
                                <CardHeader>
                                    <CardTitle className="text-lg">
                                        {t('admin.systemAdmin.orderManagement.changeOrderDeliveryDate', 'Change Order Delivery Date')}
                                    </CardTitle>
                                </CardHeader>
                            </Card>
                        </Link>
                    </div>
                </div>
            </div>
        </AdminGuard>
    );
};

export default SystemAdminPage; 
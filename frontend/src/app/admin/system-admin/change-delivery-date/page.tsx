"use client";

import React, { use<PERSON><PERSON>back, useMemo, useState } from 'react';
import Link from 'next/link';
import { useQuery } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';
import { format } from 'date-fns';
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { AdminGuard } from "../../../components/common/AdminGuard";
import DataTable, { Column } from "../../components/DataTable";
import { ChevronRightIcon, HomeIcon, ArrowPathIcon, CalendarDaysIcon, CalendarIcon } from '@heroicons/react/24/outline';
import {
    getOrdersForAdmin,
    getDefaultDateRange,
    formatDeliveryDate,
    updateOrder,
    formatDateForInput,
} from "../../../services/orderService";
import { Order } from "../../../types/order";
import { logger } from '@/lib/logger';
import { cn } from "@/lib/utils";
import { toast } from 'react-toastify';

const ChangeDeliveryDatePage: React.FC = () => {
    const { t } = useTranslation();

    // Date picker states
    const [dateFromOpen, setDateFromOpen] = useState(false);
    const [dateToOpen, setDateToOpen] = useState(false);

    // Row edit state management
    const [editingRowId, setEditingRowId] = useState<number | null>(null);
    const [editingDeliveryDate, setEditingDeliveryDate] = useState<string>('');
    const [isUpdating, setIsUpdating] = useState(false);
    const [deliveryDatePickerOpen, setDeliveryDatePickerOpen] = useState(false);

    // Get default date range (yesterday to today)
    const defaultDateRange = useMemo(() => getDefaultDateRange(), []);
    const [dateFrom, setDateFrom] = useState(defaultDateRange.dateFrom);
    const [dateTo, setDateTo] = useState(defaultDateRange.dateTo);

    // Parse dates for calendar (convert from YYYY-MM-DD to Date object)
    const parseDate = (dateString: string): Date | undefined => {
        if (!dateString) return undefined;
        const [year, month, day] = dateString.split('-').map(Number);
        return new Date(year, month - 1, day);
    };

    // Format date for display (DD/MM/YYYY)
    const formatDateForDisplay = (date: Date | undefined): string => {
        if (!date) return '';
        return format(date, 'dd/MM/yyyy');
    };

    // Format date for API (YYYY-MM-DD)
    const formatDateForAPI = (date: Date | undefined): string => {
        if (!date) return '';
        return format(date, 'yyyy-MM-dd');
    };

    // Handle date range changes
    const handleDateRangeChange = (field: 'dateFrom' | 'dateTo', date: Date | undefined) => {
        const formattedDate = formatDateForAPI(date);
        if (field === 'dateFrom') {
            setDateFrom(formattedDate);
        } else {
            setDateTo(formattedDate);
        }
    };

    // Handle delivery date selection for editing
    const handleDeliveryDateSelect = useCallback((date: Date | undefined) => {
        if (date) {
            const formattedDate = formatDateForInput(date.toISOString());
            setEditingDeliveryDate(formattedDate);
            setDeliveryDatePickerOpen(false);
        }
    }, []);

    // Check if date is in the past (before today)
    const isDateInPast = (date: Date): boolean => {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const compareDate = new Date(date);
        compareDate.setHours(0, 0, 0, 0);
        return compareDate < today;
    };

    // Fetch orders based on date range
    const {
        data: allOrders = [],
        isLoading,
        isError,
        error,
        refetch,
        isFetching
    } = useQuery<Order[], Error>({
        queryKey: ['admin-change-delivery-date', dateFrom, dateTo],
        queryFn: () => getOrdersForAdmin(dateFrom, dateTo),
        staleTime: 30000, // 30 seconds
        gcTime: 300000, // 5 minutes
        refetchOnMount: true,
        refetchOnWindowFocus: false,
    });

    // Filter orders to show only allowed statuses: NEW, CONFIRMED, PICKED, SCHEDULED, READY_TO_SHIP
    const allowedStatuses = useMemo(() => ['NEW', 'CONFIRMED', 'PICKED', 'SCHEDULED', 'READY_TO_SHIP'], []);
    const eligibleOrders = useMemo(() => {
        return allOrders.filter(order => allowedStatuses.includes(order.status));
    }, [allOrders, allowedStatuses]);

    // Handle refresh
    const handleRefresh = () => {
        logger.debug('Change Delivery Date: Manual refresh triggered');
        refetch();
    };

    // Handle entering edit mode for a row
    const handleEditRow = React.useCallback((order: Order) => {
        if (isUpdating) return; // Prevent editing if another row is being updated
        setEditingRowId(order.id);
        // Extract date part from existing delivery date for editing
        const currentDate = formatDateForInput(order.deliveryDate);
        setEditingDeliveryDate(currentDate);
        logger.debug('Change Delivery Date: Entering edit mode', { orderId: order.id, currentDeliveryDate: order.deliveryDate });
    }, [isUpdating]);

    // Handle canceling edit mode
    const handleCancelEdit = React.useCallback(() => {
        setEditingRowId(null);
        setEditingDeliveryDate('');
        setDeliveryDatePickerOpen(false);
        logger.debug('Change Delivery Date: Cancelled edit mode');
    }, []);

    // Handle saving delivery date change
    const handleSaveDeliveryDate = React.useCallback(async (order: Order) => {
        if (!editingDeliveryDate.trim()) return;

        setIsUpdating(true);
        try {
            logger.debug('Change Delivery Date: Saving delivery date change', {
                orderId: order.id,
                oldDeliveryDate: order.deliveryDate,
                newDeliveryDate: editingDeliveryDate
            });

            // Split existing delivery date to preserve time component
            const existingDateTime = order.deliveryDate || new Date().toISOString();
            const timePart = existingDateTime.includes('T') ? existingDateTime.split('T')[1] : '00:00:00.000Z';
            const newDeliveryDate = editingDeliveryDate + 'T' + timePart;

            // Update the order with new delivery date (send all values for consistency)
            await updateOrder({
                id: order.id,
                status: order.status,
                mobile: order.mobile,
                facilityKey: order.facilityKey,
                deliveryLocation: order.deliveryLocation,
                deliveryDate: newDeliveryDate, // This is the only field we're changing
                skuItems: order.skuItems,
                metadata: order.metadata
            });

            // Show success toast
            toast.success(
                t('admin.systemAdmin.changeOrderDeliveryDate.messages.deliveryDateUpdatedSuccess',
                    'Delivery date updated successfully'),
                { autoClose: 3000 }
            );

            // Exit edit mode
            setEditingRowId(null);
            setEditingDeliveryDate('');
            setDeliveryDatePickerOpen(false);

            // Refresh the orders data
            refetch();

        } catch (error) {
            logger.error('Change Delivery Date: Failed to update delivery date', {
                error,
                orderId: order.id,
                newDeliveryDate: editingDeliveryDate
            });

            // Show error toast
            toast.error(
                t('admin.systemAdmin.changeOrderDeliveryDate.messages.deliveryDateUpdateError',
                    'Failed to update delivery date. Please try again.'),
                { autoClose: 5000 }
            );
        } finally {
            setIsUpdating(false);
        }
    }, [editingDeliveryDate, refetch, t]);

    // Define table columns
    const columns: Column<Order>[] = useMemo(() => [
        {
            key: 'id',
            header: t('admin.systemAdmin.changeOrderDeliveryDate.table.orderId', 'Order ID'),
            width: '100px',
            render: (order) => `#${order.id}`,
            sortable: true,
            getSortValue: (order) => order.id
        },
        {
            key: 'endCustomerName',
            header: t('admin.systemAdmin.changeOrderDeliveryDate.table.customerName', 'Customer Name'),
            width: '180px',
            render: (order) => order.endCustomerName || t('admin.systemAdmin.changeOrderDeliveryDate.table.noCustomerName', 'No Customer Name'),
            sortable: true,
            getSortValue: (order) => order.endCustomerName || ''
        },
        {
            key: 'mobile',
            header: t('admin.systemAdmin.changeOrderDeliveryDate.table.customerPhone', 'Customer Phone'),
            width: '140px',
            render: (order) => order.mobile || t('admin.systemAdmin.changeOrderDeliveryDate.table.noCustomerPhone', 'No Customer Phone'),
            sortable: true,
            getSortValue: (order) => order.mobile || ''
        },
        {
            key: 'deliveryDate',
            header: t('admin.systemAdmin.changeOrderDeliveryDate.table.deliveryDate', 'Delivery Date'),
            width: '200px',
            render: (order) => {
                const isEditing = editingRowId === order.id;

                if (isEditing) {
                    // Edit mode: Show date picker
                    return (
                        <Popover open={deliveryDatePickerOpen} onOpenChange={setDeliveryDatePickerOpen}>
                            <PopoverTrigger asChild>
                                <Button
                                    variant="outline"
                                    className={cn(
                                        "w-full justify-start text-left font-normal h-8",
                                        !editingDeliveryDate && "text-muted-foreground"
                                    )}
                                    disabled={isUpdating}
                                >
                                    <CalendarIcon className="mr-2 h-4 w-4" />
                                    {editingDeliveryDate ? formatDateForDisplay(parseDate(editingDeliveryDate)) : "Select date"}
                                </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0" align="start">
                                <Calendar
                                    mode="single"
                                    selected={parseDate(editingDeliveryDate)}
                                    onSelect={handleDeliveryDateSelect}
                                    disabled={(date) => isDateInPast(date)}
                                    initialFocus
                                />
                            </PopoverContent>
                        </Popover>
                    );
                } else {
                    // View mode: Show delivery date as text
                    return (
                        <span className="text-sm">
                            {formatDeliveryDate(order.deliveryDate)}
                        </span>
                    );
                }
            },
            sortable: true,
            getSortValue: (order) => order.deliveryDate || ''
        },
        {
            key: 'actions',
            header: t('admin.systemAdmin.changeOrderDeliveryDate.table.actions', 'Actions'),
            width: '160px',
            render: (order) => {
                const isEditing = editingRowId === order.id;

                if (isEditing) {
                    // Edit mode: Show Save and Cancel buttons
                    return (
                        <div className="flex items-center space-x-2">
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleSaveDeliveryDate(order)}
                                disabled={isUpdating || !editingDeliveryDate.trim()}
                                className="h-8 px-3 text-green-600 border-green-600 hover:bg-green-50"
                            >
                                {isUpdating ? t('admin.systemAdmin.changeOrderDeliveryDate.buttons.saving', 'Saving...') : t('admin.systemAdmin.changeOrderDeliveryDate.buttons.save', 'Save')}
                            </Button>
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={handleCancelEdit}
                                disabled={isUpdating}
                                className="h-8 px-3 text-gray-600 border-gray-600 hover:bg-gray-50"
                            >
                                {t('admin.systemAdmin.changeOrderDeliveryDate.buttons.cancel', 'Cancel')}
                            </Button>
                        </div>
                    );
                } else {
                    // View mode: Show Edit button
                    return (
                        <div className="flex items-center space-x-2">
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleEditRow(order)}
                                disabled={editingRowId !== null} // Disable if any row is being edited
                                className="h-8 px-3"
                            >
                                {t('admin.systemAdmin.changeOrderDeliveryDate.buttons.edit', 'Edit')}
                            </Button>
                        </div>
                    );
                }
            },
            sortable: false
        }
    ], [
        t,
        editingRowId,
        editingDeliveryDate,
        isUpdating,
        deliveryDatePickerOpen,
        handleEditRow,
        handleCancelEdit,
        handleSaveDeliveryDate,
        handleDeliveryDateSelect
    ]);

    return (
        <AdminGuard requiredPermission="systemAdmin">
            <div className="space-y-6">
                {/* Breadcrumb Navigation */}
                <nav className="flex" aria-label="Breadcrumb">
                    <ol className="flex items-center space-x-4">
                        <li>
                            <Link href="/admin" className="text-gray-400 hover:text-gray-500">
                                <HomeIcon className="flex-shrink-0 h-5 w-5" />
                                <span className="sr-only">{t('admin.systemAdmin.breadcrumbs.home', 'Admin')}</span>
                            </Link>
                        </li>
                        <li>
                            <div className="flex items-center">
                                <ChevronRightIcon className="flex-shrink-0 h-5 w-5 text-gray-400" />
                                <Link
                                    href="/admin/system-admin"
                                    className="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700"
                                >
                                    {t('admin.systemAdmin.breadcrumbs.systemAdmin', 'System Administration')}
                                </Link>
                            </div>
                        </li>
                        <li>
                            <div className="flex items-center">
                                <ChevronRightIcon className="flex-shrink-0 h-5 w-5 text-gray-400" />
                                <span className="ml-4 text-sm font-medium text-gray-900">
                                    {t('admin.systemAdmin.changeOrderDeliveryDate.title', 'Change Order Delivery Date')}
                                </span>
                            </div>
                        </li>
                    </ol>
                </nav>

                {/* Page Header */}
                <div className="border-l-4 border-blue-200 pl-4">
                    <h1 className="text-3xl font-bold tracking-tight text-foreground">
                        {t('admin.systemAdmin.changeOrderDeliveryDate.title', 'Change Order Delivery Date')}
                    </h1>
                    <p className="text-muted-foreground mt-2">
                        {t('admin.systemAdmin.changeOrderDeliveryDate.subtitle', 'Update delivery dates for eligible orders')}
                    </p>
                </div>

                {/* Date Range Filters */}
                <Card>
                    <CardContent className="p-4">
                        <div className="flex flex-col md:flex-row md:items-end gap-3 md:gap-4">
                            {/* Date From */}
                            <div className="space-y-1 md:space-y-0 md:min-w-[180px]">
                                <label className="text-xs font-medium text-muted-foreground">
                                    {t('admin.systemAdmin.changeOrderDeliveryDate.filters.dateFrom', 'From Date')}
                                </label>
                                <Popover open={dateFromOpen} onOpenChange={setDateFromOpen}>
                                    <PopoverTrigger asChild>
                                        <Button
                                            variant="outline"
                                            className={cn(
                                                "w-full justify-start text-left font-normal h-9",
                                                !dateFrom && "text-muted-foreground"
                                            )}
                                        >
                                            <CalendarIcon className="mr-2 h-4 w-4" />
                                            {dateFrom ? formatDateForDisplay(parseDate(dateFrom)) : "dd/mm/yyyy"}
                                        </Button>
                                    </PopoverTrigger>
                                    <PopoverContent className="w-auto p-0" align="start">
                                        <Calendar
                                            mode="single"
                                            selected={parseDate(dateFrom)}
                                            onSelect={(date) => {
                                                handleDateRangeChange('dateFrom', date);
                                                setDateFromOpen(false);
                                            }}
                                            initialFocus
                                        />
                                    </PopoverContent>
                                </Popover>
                            </div>

                            {/* Date To */}
                            <div className="space-y-1 md:space-y-0 md:min-w-[180px]">
                                <label className="text-xs font-medium text-muted-foreground">
                                    {t('admin.systemAdmin.changeOrderDeliveryDate.filters.dateTo', 'To Date')}
                                </label>
                                <Popover open={dateToOpen} onOpenChange={setDateToOpen}>
                                    <PopoverTrigger asChild>
                                        <Button
                                            variant="outline"
                                            className={cn(
                                                "w-full justify-start text-left font-normal h-9",
                                                !dateTo && "text-muted-foreground"
                                            )}
                                        >
                                            <CalendarIcon className="mr-2 h-4 w-4" />
                                            {dateTo ? formatDateForDisplay(parseDate(dateTo)) : "dd/mm/yyyy"}
                                        </Button>
                                    </PopoverTrigger>
                                    <PopoverContent className="w-auto p-0" align="start">
                                        <Calendar
                                            mode="single"
                                            selected={parseDate(dateTo)}
                                            onSelect={(date) => {
                                                handleDateRangeChange('dateTo', date);
                                                setDateToOpen(false);
                                            }}
                                            initialFocus
                                        />
                                    </PopoverContent>
                                </Popover>
                            </div>

                            {/* Spacer */}
                            <div className="flex-1"></div>

                            {/* Info and Actions */}
                            <div className="flex items-center space-x-4">
                                <Badge variant="secondary" className="text-sm">
                                    {eligibleOrders.length} {t('admin.systemAdmin.changeOrderDeliveryDate.filters.eligibleOrders', 'Eligible Orders')}
                                </Badge>

                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={handleRefresh}
                                    disabled={isFetching}
                                    className="min-w-[100px]"
                                >
                                    <ArrowPathIcon className={cn("h-4 w-4 mr-2", isFetching && "animate-spin")} />
                                    {isFetching ? t('admin.systemAdmin.changeOrderDeliveryDate.buttons.refreshing', 'Refreshing...') : t('admin.systemAdmin.changeOrderDeliveryDate.buttons.refresh', 'Refresh')}
                                </Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Date Filter Info */}
                <Alert>
                    <CalendarDaysIcon className="h-4 w-4" />
                    <AlertDescription>
                        {t('admin.systemAdmin.changeOrderDeliveryDate.description', 'Showing orders eligible for delivery date changes (NEW, CONFIRMED, PICKED, SCHEDULED, READY_TO_SHIP)')}
                        {' '}
                        <Badge variant="outline" className="ml-2">
                            {t('admin.systemAdmin.changeOrderDeliveryDate.filters.dateRange', 'Date Range')}: {dateFrom} to {dateTo}
                        </Badge>
                    </AlertDescription>
                </Alert>

                {/* Orders Table */}
                {isLoading ? (
                    <Card>
                        <CardContent className="p-6">
                            <div className="space-y-4">
                                <Skeleton className="h-8 w-full" />
                                <Skeleton className="h-8 w-full" />
                                <Skeleton className="h-8 w-full" />
                                <Skeleton className="h-8 w-full" />
                                <Skeleton className="h-8 w-full" />
                            </div>
                        </CardContent>
                    </Card>
                ) : isError ? (
                    <Card>
                        <CardContent className="p-6">
                            <Alert>
                                <AlertDescription>
                                    {t('admin.orders.error.generic', 'Failed to load orders. Please try again.')}
                                    <br />
                                    {error?.message}
                                </AlertDescription>
                            </Alert>
                            <Button onClick={handleRefresh} className="mt-4">
                                {t('admin.orders.error.retry', 'Retry')}
                            </Button>
                        </CardContent>
                    </Card>
                ) : (
                    <DataTable
                        data={eligibleOrders}
                        columns={columns}
                        loading={isLoading}
                        emptyMessage={t('admin.systemAdmin.changeOrderDeliveryDate.table.empty', 'No eligible orders found for the selected date range')}
                        getItemId={(order) => order.id}
                    />
                )}
            </div>
        </AdminGuard>
    );
};

export default ChangeDeliveryDatePage; 
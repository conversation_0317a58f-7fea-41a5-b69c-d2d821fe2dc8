"use client";

import React, { useMemo, useState } from 'react';
import Link from 'next/link';
import { useQuery } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';
import { format } from 'date-fns';
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { AdminGuard } from "../../../components/common/AdminGuard";
import DataTable, { Column } from "../../components/DataTable";
import { ChevronRightIcon, HomeIcon, ArrowPathIcon, CalendarDaysIcon, CalendarIcon } from '@heroicons/react/24/outline';
import {
    getOrdersForAdmin,
    getDefaultDateRange,
    updateOrder
} from "../../../services/orderService";
import { Order } from "../../../types/order";
import { logger } from '@/lib/logger';
import { cn } from "@/lib/utils";
import { getAllTerminalPolygons } from '@/lib/polygonUtils';
import { toast } from 'react-toastify';

const ReassignOrderFacilitiesPage: React.FC = () => {
    const { t } = useTranslation();

    // Date picker states
    const [dateFromOpen, setDateFromOpen] = useState(false);
    const [dateToOpen, setDateToOpen] = useState(false);

    // Row edit state management
    const [editingRowId, setEditingRowId] = useState<number | null>(null);
    const [editingFacilityKey, setEditingFacilityKey] = useState<string>('');
    const [isUpdating, setIsUpdating] = useState(false);

    // Get default date range (yesterday to today)
    const defaultDateRange = useMemo(() => getDefaultDateRange(), []);
    const [dateFrom, setDateFrom] = useState(defaultDateRange.dateFrom);
    const [dateTo, setDateTo] = useState(defaultDateRange.dateTo);

    // Parse dates for calendar (convert from YYYY-MM-DD to Date object)
    const parseDate = (dateString: string): Date | undefined => {
        if (!dateString) return undefined;
        const [year, month, day] = dateString.split('-').map(Number);
        return new Date(year, month - 1, day);
    };

    // Format date for display (DD/MM/YYYY)
    const formatDateForDisplay = (date: Date | undefined): string => {
        if (!date) return '';
        return format(date, 'dd/MM/yyyy');
    };

    // Format date for API (YYYY-MM-DD)
    const formatDateForAPI = (date: Date | undefined): string => {
        if (!date) return '';
        return format(date, 'yyyy-MM-dd');
    };

    // Handle date range changes
    const handleDateRangeChange = (field: 'dateFrom' | 'dateTo', date: Date | undefined) => {
        const formattedDate = formatDateForAPI(date);
        if (field === 'dateFrom') {
            setDateFrom(formattedDate);
        } else {
            setDateTo(formattedDate);
        }
    };

    // Fetch orders based on date range
    const {
        data: allOrders = [],
        isLoading,
        isError,
        error,
        refetch,
        isFetching
    } = useQuery<Order[], Error>({
        queryKey: ['admin-reassign-orders', dateFrom, dateTo],
        queryFn: () => getOrdersForAdmin(dateFrom, dateTo),
        staleTime: 30000, // 30 seconds
        gcTime: 300000, // 5 minutes
        refetchOnMount: true,
        refetchOnWindowFocus: false,
    });

    // Fetch facilities for dropdown
    const {
        data: facilities = [],
        isLoading: facilitiesLoading,
        isError: facilitiesError
    } = useQuery({
        queryKey: ['admin-facilities'],
        queryFn: getAllTerminalPolygons,
        staleTime: 300000, // 5 minutes - facilities don't change often
        gcTime: 600000, // 10 minutes
        refetchOnMount: false,
        refetchOnWindowFocus: false,
    });

    // Filter orders to show only "SCHEDULED" status
    const scheduledOrders = useMemo(() => {
        return allOrders.filter(order => order.status === 'SCHEDULED');
    }, [allOrders]);

    // Handle refresh
    const handleRefresh = () => {
        logger.debug('Reassign Order Facilities: Manual refresh triggered');
        refetch();
    };

    // Handle entering edit mode for a row
    const handleEditRow = React.useCallback((order: Order) => {
        if (isUpdating) return; // Prevent editing if another row is being updated
        setEditingRowId(order.id);
        setEditingFacilityKey(order.facilityKey || '');
        logger.debug('Reassign Order Facilities: Entering edit mode', { orderId: order.id, currentFacility: order.facilityKey });
    }, [isUpdating]);

    // Handle canceling edit mode
    const handleCancelEdit = React.useCallback(() => {
        setEditingRowId(null);
        setEditingFacilityKey('');
        logger.debug('Reassign Order Facilities: Cancelled edit mode');
    }, []);

    // Handle saving facility change
    const handleSaveFacility = React.useCallback(async (order: Order) => {
        if (!editingFacilityKey.trim()) return;

        setIsUpdating(true);
        try {
            logger.debug('Reassign Order Facilities: Saving facility change', {
                orderId: order.id,
                oldFacility: order.facilityKey,
                newFacility: editingFacilityKey
            });

            // Update the order with new facility key (send all values for consistency)
            await updateOrder({
                id: order.id,
                status: order.status,
                mobile: order.mobile,
                facilityKey: editingFacilityKey, // This is the only field we're changing
                deliveryLocation: order.deliveryLocation,
                deliveryDate: order.deliveryDate,
                skuItems: order.skuItems,
                metadata: order.metadata
            });

            // Show success toast
            toast.success(
                t('admin.systemAdmin.reassignOrderFacilities.messages.facilityUpdatedSuccess',
                    'Facility updated successfully'),
                { autoClose: 3000 }
            );

            // Exit edit mode
            setEditingRowId(null);
            setEditingFacilityKey('');

            // Refresh the orders data
            refetch();

        } catch (error) {
            logger.error('Reassign Order Facilities: Failed to update facility', {
                error,
                orderId: order.id,
                newFacility: editingFacilityKey
            });

            // Show error toast
            toast.error(
                t('admin.systemAdmin.reassignOrderFacilities.messages.facilityUpdateError',
                    'Failed to update facility. Please try again.'),
                { autoClose: 5000 }
            );
        } finally {
            setIsUpdating(false);
        }
    }, [editingFacilityKey, refetch, t]);

    // Define table columns
    const columns: Column<Order>[] = useMemo(() => [
        {
            key: 'id',
            header: t('admin.systemAdmin.reassignOrderFacilities.table.orderId', 'Order ID'),
            width: '100px',
            render: (order) => `#${order.id}`,
            sortable: true,
            getSortValue: (order) => order.id
        },
        {
            key: 'endCustomerName',
            header: t('admin.systemAdmin.reassignOrderFacilities.table.customerName', 'Customer Name'),
            width: '180px',
            render: (order) => order.endCustomerName || t('admin.systemAdmin.reassignOrderFacilities.table.noCustomerName', 'No Customer Name'),
            sortable: true,
            getSortValue: (order) => order.endCustomerName || ''
        },
        {
            key: 'mobile',
            header: t('admin.systemAdmin.reassignOrderFacilities.table.customerPhone', 'Customer Phone'),
            width: '140px',
            render: (order) => order.mobile || t('admin.systemAdmin.reassignOrderFacilities.table.noCustomerPhone', 'No Customer Phone'),
            sortable: true,
            getSortValue: (order) => order.mobile || ''
        },
        {
            key: 'facilityKey',
            header: t('admin.systemAdmin.reassignOrderFacilities.table.facilityKey', 'Facility'),
            width: '200px',
            render: (order) => {
                const isEditing = editingRowId === order.id;

                if (isEditing) {
                    // Edit mode: Show facility dropdown
                    return (
                        <Select
                            value={editingFacilityKey}
                            onValueChange={setEditingFacilityKey}
                            disabled={isUpdating || facilitiesLoading}
                        >
                            <SelectTrigger className="w-full h-8">
                                <SelectValue placeholder={
                                    facilitiesLoading
                                        ? t('admin.systemAdmin.reassignOrderFacilities.table.loadingFacilities', 'Loading facilities...')
                                        : t('admin.systemAdmin.reassignOrderFacilities.table.selectFacility', 'Select facility')
                                } />
                            </SelectTrigger>
                            <SelectContent>
                                {facilities.map((facility) => (
                                    <SelectItem key={facility.id} value={facility.id}>
                                        {facility.id} - {facility.name}
                                    </SelectItem>
                                ))}
                                {facilities.length === 0 && !facilitiesLoading && (
                                    <SelectItem value="" disabled>
                                        {facilitiesError
                                            ? t('admin.systemAdmin.reassignOrderFacilities.table.facilitiesError', 'Error loading facilities')
                                            : t('admin.systemAdmin.reassignOrderFacilities.table.noFacilities', 'No facilities available')
                                        }
                                    </SelectItem>
                                )}
                            </SelectContent>
                        </Select>
                    );
                } else {
                    // View mode: Show facility key as text
                    return (
                        <span className="text-sm">
                            {order.facilityKey || t('admin.systemAdmin.reassignOrderFacilities.table.noFacility', 'No Facility')}
                        </span>
                    );
                }
            },
            sortable: true,
            getSortValue: (order) => order.facilityKey || ''
        },
        {
            key: 'actions',
            header: t('admin.systemAdmin.reassignOrderFacilities.table.actions', 'Actions'),
            width: '160px',
            render: (order) => {
                const isEditing = editingRowId === order.id;

                if (isEditing) {
                    // Edit mode: Show Save and Cancel buttons
                    return (
                        <div className="flex items-center space-x-2">
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleSaveFacility(order)}
                                disabled={isUpdating || !editingFacilityKey.trim()}
                                className="h-8 px-3 text-green-600 border-green-600 hover:bg-green-50"
                            >
                                {isUpdating ? t('admin.systemAdmin.reassignOrderFacilities.buttons.saving', 'Saving...') : t('admin.systemAdmin.reassignOrderFacilities.buttons.save', 'Save')}
                            </Button>
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={handleCancelEdit}
                                disabled={isUpdating}
                                className="h-8 px-3 text-gray-600 border-gray-600 hover:bg-gray-50"
                            >
                                {t('admin.systemAdmin.reassignOrderFacilities.buttons.cancel', 'Cancel')}
                            </Button>
                        </div>
                    );
                } else {
                    // View mode: Show Edit button
                    return (
                        <div className="flex items-center space-x-2">
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleEditRow(order)}
                                disabled={editingRowId !== null} // Disable if any row is being edited
                                className="h-8 px-3"
                            >
                                {t('admin.systemAdmin.reassignOrderFacilities.buttons.edit', 'Edit')}
                            </Button>
                        </div>
                    );
                }
            },
            sortable: false
        }
    ], [
        t,
        editingRowId,
        editingFacilityKey,
        isUpdating,
        facilitiesLoading,
        facilitiesError,
        facilities,
        handleEditRow,
        handleCancelEdit,
        handleSaveFacility
    ]);

    return (
        <AdminGuard requiredPermission="systemAdmin">
            <div className="space-y-6">
                {/* Breadcrumb Navigation */}
                <nav className="flex" aria-label="Breadcrumb">
                    <ol className="flex items-center space-x-4">
                        <li>
                            <Link href="/admin" className="text-gray-400 hover:text-gray-500">
                                <HomeIcon className="flex-shrink-0 h-5 w-5" />
                                <span className="sr-only">{t('admin.systemAdmin.breadcrumbs.home', 'Admin')}</span>
                            </Link>
                        </li>
                        <li>
                            <div className="flex items-center">
                                <ChevronRightIcon className="flex-shrink-0 h-5 w-5 text-gray-400" />
                                <Link
                                    href="/admin/system-admin"
                                    className="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700"
                                >
                                    {t('admin.systemAdmin.breadcrumbs.systemAdmin', 'System Administration')}
                                </Link>
                            </div>
                        </li>
                        <li>
                            <div className="flex items-center">
                                <ChevronRightIcon className="flex-shrink-0 h-5 w-5 text-gray-400" />
                                <span className="ml-4 text-sm font-medium text-gray-900">
                                    {t('admin.systemAdmin.reassignOrderFacilities.title', 'Reassign Order Facilities')}
                                </span>
                            </div>
                        </li>
                    </ol>
                </nav>

                {/* Page Header */}
                <div className="border-l-4 border-amber-200 pl-4">
                    <h1 className="text-3xl font-bold tracking-tight text-foreground">
                        {t('admin.systemAdmin.reassignOrderFacilities.title', 'Reassign Order Facilities')}
                    </h1>
                    <p className="text-muted-foreground mt-2">
                        {t('admin.systemAdmin.reassignOrderFacilities.subtitle', 'Update facility assignments for scheduled orders')}
                    </p>
                </div>

                {/* Date Range Filters */}
                <Card>
                    <CardContent className="p-4">
                        <div className="flex flex-col md:flex-row md:items-end gap-3 md:gap-4">
                            {/* Date From */}
                            <div className="space-y-1 md:space-y-0 md:min-w-[180px]">
                                <label className="text-xs font-medium text-muted-foreground">
                                    {t('admin.systemAdmin.reassignOrderFacilities.filters.dateFrom', 'From Date')}
                                </label>
                                <Popover open={dateFromOpen} onOpenChange={setDateFromOpen}>
                                    <PopoverTrigger asChild>
                                        <Button
                                            variant="outline"
                                            className={cn(
                                                "w-full justify-start text-left font-normal h-9",
                                                !dateFrom && "text-muted-foreground"
                                            )}
                                        >
                                            <CalendarIcon className="mr-2 h-4 w-4" />
                                            {dateFrom ? formatDateForDisplay(parseDate(dateFrom)) : "dd/mm/yyyy"}
                                        </Button>
                                    </PopoverTrigger>
                                    <PopoverContent className="w-auto p-0" align="start">
                                        <Calendar
                                            mode="single"
                                            selected={parseDate(dateFrom)}
                                            onSelect={(date) => {
                                                handleDateRangeChange('dateFrom', date);
                                                setDateFromOpen(false);
                                            }}
                                            initialFocus
                                        />
                                    </PopoverContent>
                                </Popover>
                            </div>

                            {/* Date To */}
                            <div className="space-y-1 md:space-y-0 md:min-w-[180px]">
                                <label className="text-xs font-medium text-muted-foreground">
                                    {t('admin.systemAdmin.reassignOrderFacilities.filters.dateTo', 'To Date')}
                                </label>
                                <Popover open={dateToOpen} onOpenChange={setDateToOpen}>
                                    <PopoverTrigger asChild>
                                        <Button
                                            variant="outline"
                                            className={cn(
                                                "w-full justify-start text-left font-normal h-9",
                                                !dateTo && "text-muted-foreground"
                                            )}
                                        >
                                            <CalendarIcon className="mr-2 h-4 w-4" />
                                            {dateTo ? formatDateForDisplay(parseDate(dateTo)) : "dd/mm/yyyy"}
                                        </Button>
                                    </PopoverTrigger>
                                    <PopoverContent className="w-auto p-0" align="start">
                                        <Calendar
                                            mode="single"
                                            selected={parseDate(dateTo)}
                                            onSelect={(date) => {
                                                handleDateRangeChange('dateTo', date);
                                                setDateToOpen(false);
                                            }}
                                            initialFocus
                                        />
                                    </PopoverContent>
                                </Popover>
                            </div>

                            {/* Spacer */}
                            <div className="flex-1"></div>

                            {/* Info and Actions */}
                            <div className="flex items-center space-x-4">
                                <Badge variant="secondary" className="text-sm">
                                    {scheduledOrders.length} {t('admin.systemAdmin.reassignOrderFacilities.filters.scheduledOrders', 'Scheduled Orders')}
                                </Badge>

                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={handleRefresh}
                                    disabled={isFetching}
                                    className="min-w-[100px]"
                                >
                                    <ArrowPathIcon className={cn("h-4 w-4 mr-2", isFetching && "animate-spin")} />
                                    {isFetching ? t('admin.systemAdmin.reassignOrderFacilities.buttons.refreshing', 'Refreshing...') : t('admin.systemAdmin.reassignOrderFacilities.buttons.refresh', 'Refresh')}
                                </Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Date Filter Info */}
                <Alert>
                    <CalendarDaysIcon className="h-4 w-4" />
                    <AlertDescription>
                        {t('admin.systemAdmin.reassignOrderFacilities.description', 'Showing scheduled orders that need facility reassignment')}
                        {' '}
                        <Badge variant="outline" className="ml-2">
                            {t('admin.systemAdmin.reassignOrderFacilities.filters.dateRange', 'Date Range')}: {dateFrom} to {dateTo}
                        </Badge>
                    </AlertDescription>
                </Alert>

                {/* Orders Table */}
                {isLoading ? (
                    <Card>
                        <CardContent className="p-6">
                            <div className="space-y-4">
                                <Skeleton className="h-8 w-full" />
                                <Skeleton className="h-8 w-full" />
                                <Skeleton className="h-8 w-full" />
                                <Skeleton className="h-8 w-full" />
                                <Skeleton className="h-8 w-full" />
                            </div>
                        </CardContent>
                    </Card>
                ) : isError ? (
                    <Card>
                        <CardContent className="p-6">
                            <Alert>
                                <AlertDescription>
                                    {t('admin.orders.error.generic', 'Failed to load orders. Please try again.')}
                                    <br />
                                    {error?.message}
                                </AlertDescription>
                            </Alert>
                            <Button onClick={handleRefresh} className="mt-4">
                                {t('admin.orders.error.retry', 'Retry')}
                            </Button>
                        </CardContent>
                    </Card>
                ) : (
                    <DataTable
                        data={scheduledOrders}
                        columns={columns}
                        loading={isLoading}
                        emptyMessage={t('admin.systemAdmin.reassignOrderFacilities.table.empty', 'No scheduled orders found for the selected date range')}
                        getItemId={(order) => order.id}
                    />
                )}
            </div>
        </AdminGuard>
    );
};

export default ReassignOrderFacilitiesPage; 
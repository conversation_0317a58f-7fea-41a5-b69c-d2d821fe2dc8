'use client';

import { useState, useEffect, useCallback } from 'react';
import { useParams } from 'next/navigation';
import { getCategoryById, upsertCategories } from '@/app/services/categoryService';
import { getSkusByCategory } from '@/app/services/skuService';
import type { Category } from '@/app/types/category';
import type { SKU } from '@/app/types/sku';
import { ChevronLeftIcon, PlusIcon, TrashIcon, Bars3Icon } from '@heroicons/react/24/outline';
import Link from 'next/link';
import Image from 'next/image';
import { AdminGuard } from '@/app/components/common/AdminGuard';
import { toast } from 'react-toastify';
import { useTranslation } from 'react-i18next';
import SkuSelectionModal from '@/app/components/SkuSelectionModal';
import {
    DndContext,
    closestCenter,
    KeyboardSensor,
    PointerSensor,
    useSensor,
    useSensors,
    DragEndEvent,
} from '@dnd-kit/core';
import {
    arrayMove,
    SortableContext,
    sortableKeyboardCoordinates,
    verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
    useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { v4 as uuidv4 } from 'uuid';

// Sortable SKU Row Component
interface SortableSkuRowProps {
    sku: SKU;
    isSelected: boolean;
    onSelect: (skuId: string, checked: boolean) => void;
    t: (key: string) => string;
}

const SortableSkuRow: React.FC<SortableSkuRowProps> = ({ sku, isSelected, onSelect, t }) => {
    const {
        attributes,
        listeners,
        setNodeRef,
        transform,
        transition,
        isDragging,
    } = useSortable({ id: sku.skuId.toString() });

    const style = {
        transform: CSS.Transform.toString(transform),
        transition,
        opacity: isDragging ? 0.5 : 1,
    };

    return (
        <tr
            ref={setNodeRef}
            style={style}
            className={`hover:bg-gray-50 ${isDragging ? 'bg-blue-50' : ''}`}
        >
            <td className="px-6 py-4 whitespace-nowrap">
                <input
                    type="checkbox"
                    checked={isSelected}
                    onChange={(e) => onSelect(sku.skuId.toString(), e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
            </td>
            <td className="px-2 py-4 whitespace-nowrap">
                <button
                    className="cursor-grab active:cursor-grabbing p-1 text-gray-400 hover:text-gray-600"
                    {...attributes}
                    {...listeners}
                >
                    <Bars3Icon className="h-5 w-5" />
                </button>
            </td>
            <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center">
                    <div className="h-10 w-10 flex-shrink-0">
                        <Image
                            src={sku.imageUrl || '/placeholder-product.png'}
                            alt={sku.name.en}
                            width={40}
                            height={40}
                            className="h-10 w-10 rounded-lg object-cover"
                        />
                    </div>
                    <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">#{sku.skuId}</div>
                    </div>
                </div>
            </td>
            <td className="px-6 py-4">
                <div className="text-sm font-medium text-gray-900">{sku.name.en}</div>
                {sku.name.ta && (
                    <div className="text-sm text-gray-500">{sku.name.ta}</div>
                )}
            </td>
            <td className="px-6 py-4 whitespace-nowrap">
                <div className="text-sm text-gray-900">₹{sku.sellingPrice}</div>
                <div className="text-sm text-gray-500">{t('admin.categories.manageSkus.table.mrpLabel')} ₹{sku.mrp}</div>
            </td>
            <td className="px-6 py-4 whitespace-nowrap">
                {(() => {
                    const mrp = sku.mrp || 0;
                    const sellingPrice = sku.sellingPrice || 0;
                    const discountPercentage = mrp > 0 ? Math.round(((mrp - sellingPrice) / mrp) * 100) : 0;
                    return (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            {discountPercentage}%
                        </span>
                    );
                })()}
            </td>
            <td className="px-6 py-4 whitespace-nowrap">
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    {sku?.variants?.length} {t('admin.categories.manageSkus.table.variantsCount')}
                </span>
            </td>
        </tr>
    );
};

export default function ManageSkusPage() {
    const { t } = useTranslation();
    const params = useParams();
    const categoryId = params.id as string;

    // State management
    const [category, setCategory] = useState<Category | null>(null);
    const [categorySkus, setCategorySkus] = useState<SKU[]>([]);
    const [reorderedSkus, setReorderedSkus] = useState<SKU[]>([]);
    const [selectedSkuIds, setSelectedSkuIds] = useState<Set<string>>(new Set());
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [showAddSkus, setShowAddSkus] = useState(false);
    const [saving, setSaving] = useState(false);
    const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

    // Load category and SKU data
    const loadCategoryData = useCallback(async () => {
        try {
            setLoading(true);
            setError(null);

            // Fetch category details
            const categoryData = await getCategoryById(categoryId);
            if (!categoryData) {
                setError(t('admin.categories.manageSkus.messages.categoryNotFound'));
                return;
            }
            setCategory(categoryData);

            // Fetch SKUs for this category (admin can see inactive SKUs)
            const categorySkusArray = await getSkusByCategory(categoryId, { allowInactive: true });
            if (categorySkusArray && categorySkusArray.length > 0) {
                // Order SKUs according to the category's skuIds array order
                const orderedCategorySkus: SKU[] = [];
                const skuMap = new Map<number, SKU>();

                // Create a map for fast lookup
                categorySkusArray.forEach(sku => {
                    skuMap.set(sku.skuId, sku);
                });

                // Order SKUs according to category.skuIds array
                categoryData.skuIds.forEach(skuId => {
                    const sku = skuMap.get(skuId);
                    if (sku) {
                        orderedCategorySkus.push(sku);
                    }
                });

                // Add any remaining SKUs that might not be in the skuIds array (edge case)
                categorySkusArray.forEach(sku => {
                    if (!categoryData.skuIds.includes(sku.skuId)) {
                        orderedCategorySkus.push(sku);
                    }
                });

                setCategorySkus(orderedCategorySkus);
                setReorderedSkus(orderedCategorySkus);
            } else {
                setCategorySkus([]);
                setReorderedSkus([]);
            }

            // No need to fetch available SKUs here as the modal will handle it

        } catch (err) {
            console.error('Error loading category data:', err);
            setError(t('admin.categories.manageSkus.messages.loadError'));
        } finally {
            setLoading(false);
        }
    }, [categoryId]);

    useEffect(() => {
        loadCategoryData();
    }, [categoryId]);

    // Drag and drop sensors
    const sensors = useSensors(
        useSensor(PointerSensor),
        useSensor(KeyboardSensor, {
            coordinateGetter: sortableKeyboardCoordinates,
        })
    );

    // Handle drag end for reordering SKUs
    const handleDragEnd = (event: DragEndEvent) => {
        const { active, over } = event;

        if (over && active.id !== over.id) {
            const oldIndex = reorderedSkus.findIndex(sku => sku.skuId.toString() === active.id);
            const newIndex = reorderedSkus.findIndex(sku => sku.skuId.toString() === over.id);

            const newOrder = arrayMove(reorderedSkus, oldIndex, newIndex);
            setReorderedSkus(newOrder);
            setHasUnsavedChanges(true);
        }
    };

    // Save SKU changes (order and additions/removals)
    const handleSaveOrder = async () => {
        setSaving(true);
        try {

            if (!category) {
                setError(t('admin.categories.manageSkus.messages.categoryNotFound'));
                return;
            }

            await upsertCategories([
                {
                    id: category.id,
                    name: category.name,
                    icon: category.icon,
                    background: category.background,
                    isActive: category.isActive,
                    orderNo: category.orderNo,
                    skuIds: reorderedSkus.map(sku => sku.skuId),
                    versionUuid: uuidv4()
                }
            ]);

            toast.success(t('admin.categories.manageSkus.messages.updateSuccess'), {
                position: "top-right",
                autoClose: 3000,
                hideProgressBar: false,
                closeOnClick: true,
                pauseOnHover: true,
                draggable: true,
            });

            // Update the base category SKUs to match reordered SKUs
            setCategorySkus(reorderedSkus);
            setHasUnsavedChanges(false);
            // TODO: Show success message
        } catch (error) {
            console.error('Error saving SKU changes:', error);
            toast.error(t('admin.categories.manageSkus.messages.updateError'), {
                position: "top-right",
                autoClose: 3000,
                hideProgressBar: false,
                closeOnClick: true,
                pauseOnHover: true,
                draggable: true,
            });
        } finally {
            setSaving(false);
        }
    };

    // Discard changes
    const handleDiscardChanges = () => {
        setReorderedSkus(categorySkus);
        setHasUnsavedChanges(false);
    };

    // Handle SKU selection for removal
    const handleSkuSelection = (skuId: string, checked: boolean) => {
        const newSelection = new Set(selectedSkuIds);
        if (checked) {
            newSelection.add(skuId);
        } else {
            newSelection.delete(skuId);
        }
        setSelectedSkuIds(newSelection);
    };

    // Remove selected SKUs from category (triggers unsaved changes)
    const handleRemoveSkus = () => {
        if (selectedSkuIds.size === 0) return;

        // Update local state immediately (no API call yet)
        const updatedCategorySkus = reorderedSkus.filter(sku => !selectedSkuIds.has(sku.skuId.toString()));
        setReorderedSkus(updatedCategorySkus);

        // Mark as having unsaved changes
        setHasUnsavedChanges(true);

        // Clear selection
        setSelectedSkuIds(new Set());
    };

    // Handle SKU selection from the modal
    const handleSkuModalSelection = (selectedSkus: SKU[]) => {
        // Use the real SKU data with complete pricing information

        // Add to reordered SKUs with real data
        const updatedSkus = [...reorderedSkus, ...selectedSkus];
        setReorderedSkus(updatedSkus);

        // Mark as having unsaved changes
        setHasUnsavedChanges(true);

        // Close modal
        setShowAddSkus(false);
    };

    if (loading) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="flex flex-col items-center justify-center min-h-screen">
                <div className="text-red-600 text-lg mb-4">{error}</div>
                <Link
                    href="/admin/categories"
                    className="text-blue-600 hover:text-blue-800 underline"
                >
                    {t('admin.categories.backToCategories')}
                </Link>
            </div>
        );
    }

    return (
        <AdminGuard requiredPermission="manageSku">
            <div className="space-y-6">
                {/* Header */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div className="space-y-4">
                        <Link
                            href="/admin/categories"
                            className="inline-flex items-center text-sm text-gray-600 hover:text-gray-900 transition-colors"
                        >
                            <ChevronLeftIcon className="h-4 w-4 mr-1" />
                            {t('admin.categories.backToCategories')}
                        </Link>
                        <div>
                            <h1 className="text-3xl font-bold text-gray-900">{t('admin.categories.manageSkus.title')}</h1>
                            <div className="mt-2 flex items-center space-x-2">
                                <span className="text-gray-500">{t('admin.categories.manageSkus.categoryLabel')}</span>
                                <span className="font-medium text-gray-900">{category?.name.en}</span>
                                {category?.name.ta && (
                                    <span className="text-gray-500">({category.name.ta})</span>
                                )}
                            </div>
                            <p className="mt-1 text-sm text-gray-600">
                                {t('admin.categories.manageSkus.subtitle')}
                            </p>
                        </div>
                    </div>
                </div>

                {/* Save/Discard Changes Bar */}
                {hasUnsavedChanges && (
                    <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center">
                                <svg className="w-5 h-5 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                </svg>
                                <span className="text-sm font-medium text-yellow-900">
                                    {t('admin.categories.manageSkus.unsavedChanges')}
                                </span>
                            </div>
                            <div className="flex items-center space-x-2">
                                <button
                                    onClick={handleDiscardChanges}
                                    className="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                                    disabled={saving}
                                >
                                    {t('admin.actions.discard')}
                                </button>
                                <button
                                    onClick={handleSaveOrder}
                                    className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
                                    disabled={saving}
                                >
                                    {saving ? (
                                        <>
                                            <svg className="animate-spin -ml-1 mr-1 h-3 w-3 text-white" fill="none" viewBox="0 0 24 24">
                                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                            </svg>
                                            {t('admin.actions.saving')}
                                        </>
                                    ) : (
                                        t('admin.actions.save')
                                    )}
                                </button>
                            </div>
                        </div>
                    </div>
                )}

                {/* Current SKUs Section */}
                <div className="bg-white rounded-lg shadow">
                    <div className="px-6 py-4 border-b border-gray-200">
                        <div className="flex items-center justify-between">
                            <div>
                                <h2 className="text-lg font-medium text-gray-900">
                                    {t('admin.categories.manageSkus.currentSkus')} ({reorderedSkus.length})
                                </h2>
                                <p className="text-sm text-gray-500">
                                    {t('admin.categories.manageSkus.dragHelpText')}
                                </p>
                            </div>
                            <div className="flex items-center space-x-2">
                                {selectedSkuIds.size > 0 && (
                                    <button
                                        onClick={handleRemoveSkus}
                                        className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 flex items-center space-x-2 transition-colors"
                                    >
                                        <TrashIcon className="h-4 w-4" />
                                        <span>{t('admin.categories.manageSkus.removeSelected')} ({selectedSkuIds.size})</span>
                                    </button>
                                )}
                                <button
                                    onClick={() => setShowAddSkus(true)}
                                    className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center space-x-2 transition-colors"
                                >
                                    <PlusIcon className="h-5 w-5" />
                                    <span>{t('admin.categories.manageSkus.addSkus')}</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div className="overflow-x-auto">
                        {reorderedSkus.length === 0 ? (
                            <div className="text-center py-12">
                                <p className="text-gray-500">{t('admin.categories.manageSkus.emptyState')}</p>
                                <button
                                    onClick={() => setShowAddSkus(true)}
                                    className="mt-2 text-blue-600 hover:text-blue-800 underline"
                                >
                                    {t('admin.categories.manageSkus.addSomeSkus')}
                                </button>
                            </div>
                        ) : (
                            <DndContext
                                sensors={sensors}
                                collisionDetection={closestCenter}
                                onDragEnd={handleDragEnd}
                            >
                                <table className="min-w-full divide-y divide-gray-200">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                <input
                                                    type="checkbox"
                                                    checked={selectedSkuIds.size === reorderedSkus.length && reorderedSkus.length > 0}
                                                    onChange={(e) => {
                                                        if (e.target.checked) {
                                                            setSelectedSkuIds(new Set(reorderedSkus.map(sku => sku.skuId.toString())));
                                                        } else {
                                                            setSelectedSkuIds(new Set());
                                                        }
                                                    }}
                                                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                                />
                                            </th>
                                            <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                {t('admin.categories.manageSkus.table.order')}
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                {t('admin.categories.manageSkus.table.sku')}
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                {t('admin.categories.manageSkus.table.name')}
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                {t('admin.categories.manageSkus.table.price')}
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                {t('admin.categories.manageSkus.table.discount')}
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                {t('admin.categories.manageSkus.table.variants')}
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                        <SortableContext
                                            items={reorderedSkus.map(sku => sku.skuId.toString())}
                                            strategy={verticalListSortingStrategy}
                                        >
                                            {reorderedSkus.map((sku) => (
                                                <SortableSkuRow
                                                    key={sku.skuId}
                                                    sku={sku}
                                                    isSelected={selectedSkuIds.has(sku.skuId.toString())}
                                                    onSelect={handleSkuSelection}
                                                    t={t}
                                                />
                                            ))}
                                        </SortableContext>
                                    </tbody>
                                </table>
                            </DndContext>
                        )}
                    </div>
                </div>

                {/* Add SKUs Modal */}
                <SkuSelectionModal
                    isOpen={showAddSkus}
                    onClose={() => setShowAddSkus(false)}
                    onSelect={handleSkuModalSelection}
                    title={t('admin.categories.manageSkus.addSkusModal.title')}
                    searchPlaceholder={t('admin.categories.manageSkus.addSkusModal.searchPlaceholder')}
                    filterType="all"
                    excludeSkuIds={category?.skuIds || []}
                />
            </div>
        </AdminGuard>
    );
}
"use client";

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { Category } from '../../../../types/category';
import { getCategoryById } from '../../../../services/categoryService';
import CategoryForm from '../../../components/forms/CategoryForm';
import { upsertCategories } from '@/app/services/categoryService';
import { toast } from 'react-toastify';
import { useTranslation } from 'react-i18next';
import { v4 as uuidv4 } from 'uuid';

interface CategoryFormData {
    nameEn: string;
    nameTa: string;
    icon: string;
    background: string;
}

const EditCategoryPage: React.FC = () => {
    const { t } = useTranslation();
    const router = useRouter();
    const params = useParams();
    const categoryId = params.id as string;

    const [category, setCategory] = useState<Category | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        const fetchCategory = async () => {
            try {
                setIsLoading(true);
                setError(null);

                // Use the actual category service to fetch the category
                const category = await getCategoryById(categoryId);

                if (category) {
                    setCategory(category);
                } else {
                    setError(t('admin.categories.edit.notFound'));
                }
            } catch (err) {
                console.error('Error fetching category:', err);
                setError(t('admin.categories.edit.loadError'));
            } finally {
                setIsLoading(false);
            }
        };

        if (categoryId) {
            fetchCategory();
        }
    }, [categoryId]);

    const handleSubmit = async (formData: CategoryFormData) => {
        try {
            setIsSubmitting(true);
            setError(null);

            // Prepare the updated category data
            const updatedCategory: Partial<Category> = {
                name: {
                    en: formData.nameEn,
                    ta: formData.nameTa
                },
                icon: formData.icon,
                background: formData.background || undefined
            };

            await upsertCategories([
                {
                    id: parseInt(categoryId, 0),
                    name: updatedCategory.name || category?.name || { en: '', ta: '' },
                    icon: updatedCategory.icon || category?.icon || '',
                    background: updatedCategory.background || category?.background || '',
                    isActive: category?.isActive || 1,
                    orderNo: category?.orderNo || 100,
                    skuIds: category?.skuIds || [],
                    versionUuid: uuidv4()
                }
            ]);

            toast.success(t('admin.categories.edit.success'), {
                position: "top-right",
                autoClose: 3000,
                hideProgressBar: false,
                closeOnClick: true,
                pauseOnHover: true,
                draggable: true,
            });


            // Redirect back to categories list
            router.push('/admin/categories');
        } catch (err) {
            toast.error(t('admin.categories.edit.error'), {
                position: "top-right",
                autoClose: 3000,
                hideProgressBar: false,
                closeOnClick: true,
                pauseOnHover: true,
                draggable: true,
            });
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleCancel = () => {
        router.push('/admin/categories');
    };

    if (isLoading) {
        return (
            <div className="min-h-screen bg-gray-50 py-8">
                <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="bg-white shadow rounded-lg">
                        <div className="px-4 py-5 sm:p-6">
                            <div className="animate-pulse">
                                <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
                                <div className="space-y-4">
                                    <div className="h-4 bg-gray-200 rounded w-1/6"></div>
                                    <div className="h-10 bg-gray-200 rounded"></div>
                                    <div className="h-4 bg-gray-200 rounded w-1/6"></div>
                                    <div className="h-10 bg-gray-200 rounded"></div>
                                    <div className="h-4 bg-gray-200 rounded w-1/6"></div>
                                    <div className="h-10 bg-gray-200 rounded"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    if (error || !category) {
        return (
            <div className="min-h-screen bg-gray-50 py-8">
                <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="bg-white shadow rounded-lg">
                        <div className="px-4 py-5 sm:p-6">
                            <div className="text-center">
                                <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                                    <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                    </svg>
                                </div>
                                <h3 className="mt-2 text-sm font-medium text-gray-900">
                                    {error || t('admin.categories.edit.notFound')}
                                </h3>
                                <p className="mt-1 text-sm text-gray-500">
                                    {t('admin.categories.edit.notFoundDescription')}
                                </p>
                                <div className="mt-6">
                                    <button
                                        type="button"
                                        onClick={() => router.push('/admin/categories')}
                                        className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                    >
                                        {t('admin.categories.backToCategories')}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50 py-8">
            <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
                {/* Header */}
                <div className="mb-8">
                    <nav className="flex" aria-label="Breadcrumb">
                        <ol className="flex items-center space-x-4">
                            <li>
                                <div>
                                    <button
                                        onClick={() => router.push('/admin')}
                                        className="text-gray-400 hover:text-gray-500"
                                    >
                                        <svg className="flex-shrink-0 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                                        </svg>
                                        <span className="sr-only">Home</span>
                                    </button>
                                </div>
                            </li>
                            <li>
                                <div className="flex items-center">
                                    <svg className="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                                    </svg>
                                    <button
                                        onClick={() => router.push('/admin/categories')}
                                        className="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700"
                                    >
                                        {t('admin.categories.title')}
                                    </button>
                                </div>
                            </li>
                            <li>
                                <div className="flex items-center">
                                    <svg className="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                                    </svg>
                                    <span className="ml-4 text-sm font-medium text-gray-500">
                                        Edit {category.name.en}
                                    </span>
                                </div>
                            </li>
                        </ol>
                    </nav>
                    <h1 className="mt-2 text-3xl font-bold leading-tight text-gray-900">
                        {t('admin.categories.edit.title')}
                    </h1>
                    <p className="mt-2 text-sm text-gray-600">
                        {t('admin.categories.edit.subtitle')}
                    </p>
                </div>

                {/* Form */}
                <div className="bg-white shadow rounded-lg">
                    <div className="px-4 py-5 sm:p-6">
                        {error && (
                            <div className="mb-6 rounded-md bg-red-50 p-4">
                                <div className="flex">
                                    <div className="flex-shrink-0">
                                        <svg className="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                                        </svg>
                                    </div>
                                    <div className="ml-3">
                                        <h3 className="text-sm font-medium text-red-800">
                                            {t('admin.categories.edit.errorTitle')}
                                        </h3>
                                        <div className="mt-2 text-sm text-red-700">
                                            <p>{error}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}

                        <CategoryForm
                            category={category}
                            onSubmit={handleSubmit}
                            onCancel={handleCancel}
                            isLoading={isSubmitting}
                            submitLabel={t('admin.categories.edit.submitLabel')}
                        />
                    </div>
                </div>
            </div>
        </div>
    );
};

export default EditCategoryPage;
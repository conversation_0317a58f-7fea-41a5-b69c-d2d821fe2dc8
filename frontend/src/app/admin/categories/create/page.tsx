"use client";

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { AdminGuard } from '../../../components/common/AdminGuard';
import CategoryForm from '../../components/forms/CategoryForm';
import { upsertCategories } from '@/app/services/categoryService';
import { toast } from 'react-toastify';
import { useTranslation } from 'react-i18next';
import { v4 as uuidv4 } from 'uuid';

interface CategoryFormData {
    nameEn: string;
    nameTa: string;
    icon: string;
    background: string;
}

const CreateCategoryPage: React.FC = () => {
    const { t } = useTranslation();
    const router = useRouter();
    const [isLoading, setIsLoading] = useState(false);

    const handleSubmit = async (data: CategoryFormData) => {
        setIsLoading(true);

        try {
            await upsertCategories([
                {
                    id: -1,
                    name: {
                        en: data.nameEn,
                        ta: data.nameTa
                    },
                    icon: data.icon,
                    skuIds: [],
                    isActive: 0,
                    orderNo: 100,
                    background: data.background,
                    versionUuid: uuidv4()
                }
            ]);

            toast.success(t('admin.categories.create.success'), {
                position: "top-right",
                autoClose: 3000,
                hideProgressBar: false,
                closeOnClick: true,
                pauseOnHover: true,
                draggable: true,
            });

            router.push('/admin/categories');
        } catch (error) {
            console.error('Error creating category:', error);
            toast.error(t('admin.categories.create.error'), {
                position: "top-right",
                autoClose: 3000,
                hideProgressBar: false,
                closeOnClick: true,
                pauseOnHover: true,
                draggable: true,
            });
        } finally {
            setIsLoading(false);
        }
    };

    const handleCancel = () => {
        router.push('/admin/categories');
    };

    return (
        <AdminGuard requiredPermission="manageCategories">
            <div className="max-w-2xl mx-auto">
                {/* Header */}
                <div className="mb-8">
                    <div className="flex items-center space-x-2 text-sm text-gray-500 mb-2">
                        <button
                            onClick={() => router.push('/admin/categories')}
                            className="hover:text-gray-700"
                        >
                            {t('admin.categories.title')}
                        </button>
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                        <span>{t('admin.categories.create.breadcrumb')}</span>
                    </div>
                    <h1 className="text-2xl font-semibold text-gray-900">{t('admin.categories.create.title')}</h1>
                    <p className="mt-1 text-sm text-gray-600">
                        {t('admin.categories.create.subtitle')}
                    </p>
                </div>

                {/* Form */}
                <div className="bg-white shadow rounded-lg p-6">
                    <CategoryForm
                        onSubmit={handleSubmit}
                        onCancel={handleCancel}
                        isLoading={isLoading}
                        submitLabel={t('admin.categories.create.submitLabel')}
                    />
                </div>
            </div>
        </AdminGuard>
    );
};

export default CreateCategoryPage; 
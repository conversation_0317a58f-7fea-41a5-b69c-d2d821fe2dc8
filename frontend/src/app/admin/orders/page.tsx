"use client";

import React, { useMemo, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { AdminGuard } from '../../components/common/AdminGuard';
import DataTable, { Column } from '../components/DataTable';
import StatusBadge from '../components/StatusBadge';
import {
    getOrdersForAdmin,
    calculateOrderTotal,
    formatDeliveryDate,
    getUniqueFacilityKeys,
    formatDateIST
} from '../../services/orderService';
import { Order, ORDER_STATUSES } from '../../types/order';
import { useOrderFilters } from '../../hooks/useOrderFilters';
import { useTranslation } from 'react-i18next';
import { logger } from '@/lib/logger';
import { templateService } from '../../services/templateService';
import { excelService } from '../../services/excelService';
import { getSkus } from '../../services/skuService';
import { toast } from 'react-toastify';
import { format } from 'date-fns';
import { CalendarIcon, ArrowPathIcon, FunnelIcon, XMarkIcon } from '@heroicons/react/24/outline';

// shadcn/ui components
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import ExportButton from '../components/OrderExportButton';
import { useDebounce } from "@uidotdev/usehooks";
import { Input } from "@/components/ui/input";

const OrdersPage: React.FC = () => {
    const { t } = useTranslation();
    const router = useRouter();
    const { filters, updateFilter, clearStatusAndFacility } = useOrderFilters();
    const [isExporting, setIsExporting] = useState(false);

    // Date picker states
    const [dateFromOpen, setDateFromOpen] = useState(false);
    const [dateToOpen, setDateToOpen] = useState(false);

    // Mobile filter expansion state
    const [filtersExpanded, setFiltersExpanded] = useState(true);

    // Mobile filter state (controlled input for debounce)
    const [mobileInput, setMobileInput] = useState(filters.mobile || "");
    const debouncedMobile = useDebounce(mobileInput, 300);

    // Sync debounced value to filter (and URL)
    React.useEffect(() => {
        if (debouncedMobile !== filters.mobile) {
            updateFilter("mobile", debouncedMobile);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [debouncedMobile]);

    // Keep input in sync with URL (e.g., on back/forward navigation)
    React.useEffect(() => {
        if (filters.mobile !== mobileInput) {
            setMobileInput(filters.mobile || "");
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [filters.mobile]);

    // Parse dates for calendar (convert from YYYY-MM-DD to Date object)
    const parseDate = (dateString: string): Date | undefined => {
        if (!dateString) return undefined;
        const [year, month, day] = dateString.split('-').map(Number);
        return new Date(year, month - 1, day);
    };

    // Format date for display (DD/MM/YYYY)
    const formatDateForDisplay = (date: Date | undefined): string => {
        if (!date) return '';
        return format(date, 'dd/MM/yyyy');
    };

    // Format date for API (YYYY-MM-DD)
    const formatDateForAPI = (date: Date | undefined): string => {
        if (!date) return '';
        return format(date, 'yyyy-MM-dd');
    };

    // Fetch orders based on date range with all filters in query key
    const {
        data: orders = [],
        isLoading,
        isError,
        error,
        refetch,
        isFetching
    } = useQuery<Order[], Error>({
        queryKey: ['admin-orders', filters.dateFrom, filters.dateTo],
        queryFn: () => getOrdersForAdmin(filters.dateFrom, filters.dateTo),
        staleTime: 30000, // 30 seconds
        gcTime: 300000, // 5 minutes
        refetchOnMount: true,
        refetchOnWindowFocus: false,
    });

    // Get unique facility keys for filter dropdown
    const facilityKeys = useMemo(() => getUniqueFacilityKeys(orders), [orders]);

    // Filter orders based on status, facility, and mobile
    const filteredOrders = useMemo(() => {
        return orders.filter(order => {
            const statusMatch = !filters.status || order.status === filters.status;
            const facilityMatch = !filters.facilityKey || order.facilityKey === filters.facilityKey;
            const mobileMatch = !filters.mobile || (order.mobile && order.mobile.includes(filters.mobile));
            return statusMatch && facilityMatch && mobileMatch;
        });
    }, [orders, filters.status, filters.facilityKey, filters.mobile]);

    // Handle filter changes
    const handleDateRangeChange = (field: 'dateFrom' | 'dateTo', date: Date | undefined) => {
        updateFilter(field, formatDateForAPI(date));
    };

    const handleStatusChange = (status: string) => {
        updateFilter('status', status === 'all' ? '' : status);
    };

    const handleFacilityChange = (facilityKey: string) => {
        updateFilter('facilityKey', facilityKey === 'all' ? '' : facilityKey);
    };

    // Handle refresh button click
    const handleRefresh = () => {
        logger.debug('Orders: Manual refresh triggered', { filters });
        refetch();
    };

    // Handle row click - navigate to order details with preserved filters
    const handleRowClick = (order: Order) => {
        logger.debug('Order clicked', { orderId: order.id, currentFilters: filters });

        // Preserve current filters in URL when navigating to order detail
        const currentParams = new URLSearchParams();
        if (filters.dateFrom) currentParams.set('dateFrom', filters.dateFrom);
        if (filters.dateTo) currentParams.set('dateTo', filters.dateTo);
        if (filters.status) currentParams.set('status', filters.status);
        if (filters.facilityKey) currentParams.set('facilityKey', filters.facilityKey);

        const orderDetailURL = `/admin/orders/${order.id}?returnFilters=${encodeURIComponent(currentParams.toString())}`;
        router.push(orderDetailURL);
    };

    // Handle export functionality
    const handleExport = async (exportFiltered: boolean) => {
        setIsExporting(true);
        try {
            logger.debug('Orders export triggered', {
                exportFiltered,
                filteredCount: filteredOrders.length,
                totalCount: orders.length,
                filters
            });

            // Choose data to export
            const exportData = exportFiltered ? filteredOrders : orders;

            if (exportData.length === 0) {
                toast.error(t('admin.orders.export.error'));
                return;
            }

            // Step 1: Collect all unique SKU IDs from all orders
            const allSkuIds = new Set<number>();
            exportData.forEach(order => {
                if (order.skuItems && order.skuItems.length > 0) {
                    order.skuItems.forEach(item => {
                        allSkuIds.add(item.skuID);
                    });
                }
            });

            // Step 2: Batch fetch all SKUs at once (optimized)
            const skuLookupMap: Record<number, any> = {};
            if (allSkuIds.size > 0) {
                try {
                    const uniqueSkuIds = Array.from(allSkuIds);
                    logger.debug('Orders export: Batch fetching SKUs', {
                        uniqueSkuCount: uniqueSkuIds.length,
                        totalOrderItems: exportData.reduce((sum, order) => sum + (order.skuItems?.length || 0), 0)
                    });

                    const batchSkus = await getSkus({ skuIds: uniqueSkuIds, allowInactive: true });
                    batchSkus.forEach((sku: any) => {
                        skuLookupMap[sku.skuId] = sku;
                    });
                } catch (error) {
                    logger.warn('Orders export: Batch SKU fetch failed, will use fallback names', { error });
                }
            }

            // Step 3: Format orders for export using pre-fetched SKUs
            const formattedData = exportData.map((order) => {
                // Build SKU details string
                let skuDetails = '';
                let skuIds = '';

                if (order.skuItems && order.skuItems.length > 0) {
                    const skuDetailsArray = order.skuItems.map((item) => {
                        const sku = skuLookupMap[item.skuID];
                        const skuName = sku?.name?.en || `SKU#${item.skuID}`;
                        const unit = item.unit || 'unit';
                        const quantity = item.quantity || 0;
                        const price = item.price || 0;
                        return `SKU#${item.skuID}: ${skuName} ${unit} (Qty: ${quantity}, ₹${price} each)`;
                    });

                    skuDetails = skuDetailsArray.join(', ');
                    skuIds = order.skuItems.map(item => item.skuID).join(',');
                }

                return {
                    orderId: order.id,
                    status: order.status,
                    mobile: order.mobile || '',
                    facilityKey: order.facilityKey || '',
                    itemsCount: order.skuItems?.length || 0,
                    totalValue: calculateOrderTotal(order),
                    createdAt: order.createdAt ? formatDateIST(new Date(order.createdAt * 1000)) : '',
                    deliveryDate: formatDeliveryDate(order.deliveryDate),
                    skuDetails,
                    skuIds,
                    deliveryLocation: order.deliveryLocation || '',
                    orderComments: order.metadata?.orderComments || '',
                    deliveryInstructions: order.metadata?.deliveryInstruction || '',
                    createdBy: order.createdBy ? order.createdBy.toString() : '',
                };
            });

            // Get template for export
            const orderTemplate = templateService.getTemplate('order-list');
            if (!orderTemplate) {
                throw new Error('Order export template not found');
            }

            // Generate filename with filter info
            const dateStr = new Date().toISOString().split('T')[0];
            let filename = `orders_${dateStr}`;

            // Add filter info to filename
            if (filters.dateFrom && filters.dateTo) {
                const fromDate = filters.dateFrom.replace(/-/g, '');
                const toDate = filters.dateTo.replace(/-/g, '');
                filename = `orders_${fromDate}_to_${toDate}`;
            }

            if (exportFiltered && (filters.status || filters.facilityKey)) {
                if (filters.status) filename += `_${filters.status}`;
                if (filters.facilityKey) filename += `_${filters.facilityKey}`;
            }

            filename += '.csv';

            // Generate CSV
            await excelService.generateCSV(
                formattedData,
                orderTemplate.columns,
                filename,
                {
                    includeInstructions: false,
                    instructions: [
                        ...orderTemplate.instructions || [],
                        `Export date: ${new Date().toLocaleDateString('en-IN')}`,
                        `Date range: ${filters.dateFrom} to ${filters.dateTo}`,
                        exportFiltered
                            ? `Filtered data: ${filteredOrders.length} orders (Status: ${filters.status || 'All'}, Facility: ${filters.facilityKey || 'All'})`
                            : `All data: ${orders.length} orders in date range`
                    ]
                }
            );

            toast.success(t('admin.orders.export.success'));
            logger.info('Orders export completed successfully', {
                exportCount: formattedData.length,
                exportFiltered
            });

        } catch (error) {
            logger.error('Orders export failed', { error });
            toast.error(t('admin.orders.export.error'));
        } finally {
            setIsExporting(false);
        }
    };

    // Define table columns with proper sorting
    const columns: Column<Order>[] = useMemo(() => [
        {
            key: 'id',
            header: t('admin.orders.table.orderId'),
            width: '100px',
            render: (order) => `#${order.id}`,
            sortable: true,
            getSortValue: (order) => order.id
        },
        {
            key: 'status',
            header: t('admin.orders.table.status'),
            width: '120px',
            render: (order) => <StatusBadge status={order.status.toLowerCase()} />,
            sortable: true,
            getSortValue: (order) => order.status
        },
        {
            key: 'mobile',
            header: t('admin.orders.table.mobile'),
            width: '140px',
            render: (order) => order.mobile || t('admin.orders.table.noMobile'),
            sortable: true,
            getSortValue: (order) => order.mobile || ''
        },
        {
            key: 'itemsCount',
            header: t('admin.orders.table.itemsCount'),
            width: '100px',
            render: (order) => order.skuItems.length.toString(),
            sortable: true,
            getSortValue: (order) => order.skuItems.length
        },
        {
            key: 'totalValue',
            header: t('admin.orders.table.totalValue'),
            width: '120px',
            render: (order) => `₹${calculateOrderTotal(order).toFixed(2)}`,
            sortable: true,
            getSortValue: (order) => calculateOrderTotal(order)
        },
        {
            key: 'endCustomerName',
            header: t('admin.orders.table.endCustomerName'),
            width: '120px',
            render: (order) => order.endCustomerName || t('admin.orders.table.noEndCustomerName'),
            sortable: true,
            getSortValue: (order) => order.endCustomerName || ''
        },
        {
            key: 'createdBy',
            header: t('admin.orders.table.createdBy'),
            width: '120px',
            render: (order) => order.createdBy ? order.createdBy.toString() : '',
            sortable: true,
            getSortValue: (order) => order.createdBy || 0
        },
        {
            key: 'createdAt',
            header: t('admin.orders.table.createdAt'),
            width: '120px',
            render: (order) => order.createdAt ? formatDateIST(new Date(order.createdAt * 1000)) : '',
            sortable: true,
            getSortValue: (order) => order.createdAt || 0
        },
        {
            key: 'deliveryDate',
            header: t('admin.orders.table.deliveryDate'),
            width: '180px',
            render: (order) => formatDeliveryDate(order.deliveryDate),
            sortable: true,
            getSortValue: (order) => order.deliveryDate ? new Date(order.deliveryDate).getTime() : 0
        },
        {
            key: 'facilityKey',
            header: t('admin.orders.table.facility'),
            width: '120px',
            render: (order) => order.facilityKey || t('admin.orders.table.noFacility'),
            sortable: true,
            getSortValue: (order) => order.facilityKey || ''
        }
    ], [t]);

    if (isError) {
        return (
            <AdminGuard requiredPermission="viewAllOrders">
                <div className="p-6">
                    <Alert variant="destructive">
                        <AlertTitle>{t('admin.orders.error.title')}</AlertTitle>
                        <AlertDescription className="mt-2">
                            {error?.message || t('admin.orders.error.generic')}
                        </AlertDescription>
                        <Button
                            onClick={() => refetch()}
                            variant="outline"
                            size="sm"
                            className="mt-4"
                        >
                            {t('admin.orders.error.retry')}
                        </Button>
                    </Alert>
                </div>
            </AdminGuard>
        );
    }

    return (
        <AdminGuard requiredPermission="viewAllOrders">
            <div className="space-y-6">
                {/* Modern Page Header */}
                <div>
                    <h1 className="text-3xl font-bold tracking-tight text-foreground">
                        {t('admin.orders.title')}
                    </h1>
                    <p className="text-muted-foreground mt-2">
                        {t('admin.orders.subtitle')}
                    </p>
                </div>

                {/* Improved Filters Layout */}
                <Card>
                    <CardContent className="p-4 py-0">
                        {/* Filter Row - Web: Horizontal, Mobile: Collapsible */}
                        <div className="space-y-3">
                            {/* Top Filter Bar */}
                            <div className="flex flex-col md:flex-row md:items-end gap-3 md:gap-2">
                                {/* Filter Icon - Non-clickable on web, clickable on mobile */}
                                <div className="flex items-center">
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        className="md:pointer-events-none md:cursor-default p-1 md:p-0 md:bg-transparent md:hover:bg-transparent"
                                        onClick={() => setFiltersExpanded(!filtersExpanded)}
                                    >
                                        <FunnelIcon className="h-5 w-5 text-muted-foreground" />
                                        <span className="md:hidden ml-2 text-sm">
                                            {filtersExpanded ? t('admin.orders.filters.hide') : t('admin.orders.filters.show')}
                                        </span>
                                    </Button>
                                </div>

                                {/* Filters Container - Always visible on web, collapsible on mobile */}
                                <div className={cn(
                                    "flex-1 flex flex-col md:flex-row gap-3 md:gap-2",
                                    !filtersExpanded && "hidden md:flex"
                                )}>
                                    {/* Date From */}
                                    <div className="space-y-1 md:space-y-0 md:min-w-[180px]">
                                        <label className="text-xs font-medium text-muted-foreground">
                                            {t('admin.orders.filters.dateFrom')}
                                        </label>
                                        <Popover open={dateFromOpen} onOpenChange={setDateFromOpen}>
                                            <PopoverTrigger asChild>
                                                <Button
                                                    variant="outline"
                                                    className={cn(
                                                        "w-full justify-start text-left font-normal h-9",
                                                        !filters.dateFrom && "text-muted-foreground"
                                                    )}
                                                >
                                                    <CalendarIcon className="mr-2 h-4 w-4" />
                                                    {filters.dateFrom ? formatDateForDisplay(parseDate(filters.dateFrom)) : "dd/mm/yyyy"}
                                                </Button>
                                            </PopoverTrigger>
                                            <PopoverContent className="w-auto p-0" align="start">
                                                <Calendar
                                                    mode="single"
                                                    selected={parseDate(filters.dateFrom)}
                                                    onSelect={(date) => {
                                                        handleDateRangeChange('dateFrom', date);
                                                        setDateFromOpen(false);
                                                    }}
                                                    initialFocus
                                                />
                                            </PopoverContent>
                                        </Popover>
                                    </div>

                                    {/* Date To */}
                                    <div className="space-y-1 md:space-y-0 md:min-w-[180px]">
                                        <label className="text-xs font-medium text-muted-foreground">
                                            {t('admin.orders.filters.dateTo')}
                                        </label>
                                        <Popover open={dateToOpen} onOpenChange={setDateToOpen}>
                                            <PopoverTrigger asChild>
                                                <Button
                                                    variant="outline"
                                                    className={cn(
                                                        "w-full justify-start text-left font-normal h-9",
                                                        !filters.dateTo && "text-muted-foreground"
                                                    )}
                                                >
                                                    <CalendarIcon className="mr-2 h-4 w-4" />
                                                    {filters.dateTo ? formatDateForDisplay(parseDate(filters.dateTo)) : "dd/mm/yyyy"}
                                                </Button>
                                            </PopoverTrigger>
                                            <PopoverContent className="w-auto p-0" align="start">
                                                <Calendar
                                                    mode="single"
                                                    selected={parseDate(filters.dateTo)}
                                                    onSelect={(date) => {
                                                        handleDateRangeChange('dateTo', date);
                                                        setDateToOpen(false);
                                                    }}
                                                    initialFocus
                                                />
                                            </PopoverContent>
                                        </Popover>
                                    </div>

                                    {/* Facility Filter */}
                                    <div className="space-y-1 md:space-y-0 md:min-w-[120px]">
                                        <label className="text-xs font-medium text-muted-foreground">
                                            {t('admin.orders.filters.facility')}
                                        </label>
                                        <Select
                                            value={filters.facilityKey}
                                            onValueChange={handleFacilityChange}
                                            disabled={facilityKeys.length === 0}
                                        >
                                            <SelectTrigger className="h-9">
                                                <SelectValue placeholder={t('admin.orders.filters.allFacilities')} />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="all">{t('admin.orders.filters.allFacilities')}</SelectItem>
                                                {facilityKeys.map(facility => (
                                                    <SelectItem key={facility} value={facility}>
                                                        {facility}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    {/* Status Filter */}
                                    <div className="space-y-1 md:space-y-0 md:min-w-[160px]">
                                        <label className="text-xs font-medium text-muted-foreground">
                                            {t('admin.orders.filters.status')}
                                        </label>
                                        <Select value={filters.status} onValueChange={handleStatusChange}>
                                            <SelectTrigger className="h-9">
                                                <SelectValue placeholder={t('admin.orders.filters.allStatuses')} />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="all">{t('admin.orders.filters.allStatuses')}</SelectItem>
                                                {ORDER_STATUSES.map(status => (
                                                    <SelectItem key={status} value={status}>
                                                        {status}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>
                                    {/* Mobile Number Filter */}
                                    <div className="space-y-1 md:space-y-0 md:min-w-[160px]">
                                        <label className="text-xs font-medium text-muted-foreground">
                                            {t('admin.orders.filters.mobile')}
                                        </label>
                                        <Input
                                            type="tel"
                                            inputMode="numeric"
                                            pattern="[0-9]*"
                                            maxLength={10}
                                            value={mobileInput}
                                            onChange={e => {
                                                // Only allow numbers, max 10 digits
                                                const val = e.target.value.replace(/\D/g, '').slice(0, 10);
                                                setMobileInput(val);
                                            }}
                                            placeholder={t('admin.orders.filters.mobilePlaceholder') || "Enter mobile number"}
                                            className="h-9"
                                        />
                                    </div>

                                    {/* Spacer to push Clear button to the right */}
                                    <div className="flex-1"></div>

                                    {/* Clear Filters - Compact Button */}
                                    <div className="flex items-end">
                                        {(filters.status || filters.facilityKey || filters.mobile) && (
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                onClick={clearStatusAndFacility}
                                                className="text-muted-foreground hover:text-foreground h-9 px-3"
                                            >
                                                <XMarkIcon className="h-4 w-4 mr-1" />
                                                <span className="text-xs">{t('admin.orders.filters.clear')}</span>
                                            </Button>
                                        )}
                                    </div>
                                </div>
                            </div>

                            {/* Filter Summary & Actions */}
                            <div className="flex items-center justify-between pt-3 border-t mt-3">
                                <div className="flex items-center space-x-4">
                                    <div className="text-sm text-muted-foreground">
                                        {t('admin.orders.filters.showing', {
                                            count: filteredOrders.length,
                                            total: orders.length
                                        })}
                                    </div>
                                    {filteredOrders.length !== orders.length && (
                                        <Badge variant="secondary" className="text-xs">
                                            {t('admin.orders.filters.filtered')}
                                        </Badge>
                                    )}
                                </div>

                                <div className="flex items-center space-x-2">
                                    <ExportButton
                                        onExport={handleExport}
                                        filteredCount={filteredOrders.length}
                                        totalCount={orders.length}
                                        disabled={isExporting || isLoading}
                                        entityName="orders"
                                    />

                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={handleRefresh}
                                        disabled={isFetching}
                                        className="min-w-[100px]"
                                    >
                                        <ArrowPathIcon className={cn("h-4 w-4 mr-2", isFetching && "animate-spin")} />
                                        {isFetching ? t('admin.orders.filters.refreshing') : t('admin.orders.filters.refresh')}
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Orders Table */}
                {isLoading ? (
                    <Card>
                        <CardContent className="p-6">
                            <div className="space-y-4">
                                <Skeleton className="h-8 w-full" />
                                <Skeleton className="h-8 w-full" />
                                <Skeleton className="h-8 w-full" />
                                <Skeleton className="h-8 w-full" />
                                <Skeleton className="h-8 w-full" />
                            </div>
                        </CardContent>
                    </Card>
                ) : (
                    <DataTable
                        data={filteredOrders}
                        columns={columns}
                        loading={isLoading}
                        emptyMessage={t('admin.orders.table.empty')}
                        onRowClick={handleRowClick}
                        getItemId={(order) => order.id}
                    />
                )}
            </div>
        </AdminGuard>
    );
};

export default OrdersPage;
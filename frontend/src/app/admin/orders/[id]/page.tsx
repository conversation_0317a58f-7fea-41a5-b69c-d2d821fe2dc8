"use client";

import React, { useState, useMemo, useEffect, useCallback } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter, useSearchParams } from 'next/navigation';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import Image from 'next/image';
import Modal from 'react-modal';
import { AdminGuard } from '../../../components/common/AdminGuard';
import StatusBadge from '../../components/StatusBadge';
import MapSelector from '../../../components/MapSelector';
import {
    getOrderById,
    updateOrder,
    cancelOrder,
    confirmOrder,
    markOrderUnreachable,
    calculateOrderTotal,
    formatDeliveryDate,
} from '../../../services/orderService';
import { getSkuById } from '../../../services/skuService';
import { Order, OrderItem, UpdateOrderRequest, OrderStatus, OrderMetadata } from '../../../types/order';
import { SKU } from '../../../types/sku';
import { useTranslation } from 'react-i18next';
import { toast } from 'react-toastify';
import { getClosestFacility, getAllTerminalPolygons, LatLng } from '@/lib/polygonUtils';
import SkuSelectionModal from '@/app/components/SkuSelectionModal';
import { logger } from '@/lib/logger';

// Utility function to safely render values and prevent object rendering errors
const safeRender = (value: unknown): string => {
    if (value === null || value === undefined) {
        return '';
    }
    if (typeof value === 'object') {
        logger.warn('Attempted to render object directly', { value });
        return JSON.stringify(value);
    }
    return String(value);
};

// Helper functions for quantity display system
const getQuantityDisplayConfig = (status: string) => {
    const statusLower = status.toLowerCase();

    if (['new', 'confirmed', 'unreachable_1', 'unreachable_2'].includes(statusLower)) {
        return {
            showOrdered: true,
            showPicked: false,
            showDelivered: false,
            columns: ['product', 'unit', 'orderedQty', 'price', 'orderedTotal']
        };
    } else if (['picked'].includes(statusLower)) {
        return {
            showOrdered: true,
            showPicked: true,
            showDelivered: false,
            columns: ['product', 'unit', 'orderedQty', 'pickedQty', 'price', 'orderedTotal']
        };
    } else if (['delivered', 'partially_delivered'].includes(statusLower)) {
        return {
            showOrdered: true,
            showPicked: true,
            showDelivered: true,
            columns: ['product', 'unit', 'orderedQty', 'pickedQty', 'deliveredQty', 'price', 'orderedTotal']
        };
    }

    // Default fallback
    return {
        showOrdered: true,
        showPicked: false,
        showDelivered: false,
        columns: ['product', 'unit', 'orderedQty', 'price', 'orderedTotal']
    };
};

const getQuantityValue = (item: OrderItem, type: 'ordered' | 'picked' | 'delivered'): number | null => {
    switch (type) {
        case 'ordered':
            return item.quantity;
        case 'picked':
            return item.pickedQuantity ?? null;
        case 'delivered':
            return item.deliveredQuantity ?? null;
        default:
            return null;
    }
};

const getQuantityDisplayStyle = (item: OrderItem, type: 'picked' | 'delivered'): string => {
    const orderedQty = item.quantity;
    const currentQty = getQuantityValue(item, type);

    if (currentQty === null) {
        return 'text-gray-500'; // For dash display
    }

    // Only show highlighting when there's a difference
    if (currentQty === orderedQty) {
        return 'text-gray-900'; // No highlighting for perfect match
    } else if (currentQty < orderedQty) {
        return 'bg-orange-50 text-orange-800 border border-orange-200'; // Under-fulfillment
    } else {
        return 'bg-red-50 text-red-800 border border-red-200'; // Over-fulfillment
    }
};

const calculateTotal = (item: OrderItem, type: 'ordered' | 'picked' | 'delivered'): number => {
    const qty = getQuantityValue(item, type);
    return qty !== null ? qty * item.price : 0;
};

const OrderDetailPage: React.FC = () => {
    const { t } = useTranslation();
    const params = useParams();
    const router = useRouter();
    const searchParams = useSearchParams();
    const queryClient = useQueryClient();
    const orderId = parseInt(params.id as string, 10);

    const [isEditing, setIsEditing] = useState(false);
    const [editData, setEditData] = useState<Partial<Order>>({});
    const [skuDetails, setSkuDetails] = useState<Record<number, SKU>>({});

    // SKU Modal states
    const [showSkuModal, setShowSkuModal] = useState(false);

    // Cancellation Modal states
    const [showCancelModal, setShowCancelModal] = useState(false);
    const [cancellationReason, setCancellationReason] = useState('');

    // Confirmation Modal states
    const [showConfirmModal, setShowConfirmModal] = useState(false);
    const [selectedDeliveryDate, setSelectedDeliveryDate] = useState<'today' | 'tomorrow' | null>(null);

    // Unreachable Modal states
    const [showUnreachableModal, setShowUnreachableModal] = useState(false);
    const [unreachableType, setUnreachableType] = useState<'UNREACHABLE_1' | 'UNREACHABLE_2' | null>(null);

    // Tooltip state
    const [showFacilityTooltip, setShowFacilityTooltip] = useState(false);

    // Fetch order data
    const {
        data: order,
        isLoading,
        isError,
        error,
        refetch
    } = useQuery<Order, Error>({
        queryKey: ['admin-order', orderId],
        queryFn: () => getOrderById(orderId),
        enabled: !isNaN(orderId),
        staleTime: 30000,
        gcTime: 300000,
    });

    // Update order mutation
    const updateOrderMutation = useMutation({
        mutationFn: (updateData: UpdateOrderRequest) => updateOrder(updateData),
        onSuccess: (updatedOrder) => {
            queryClient.setQueryData(['admin-order', orderId], updatedOrder);
            queryClient.invalidateQueries({ queryKey: ['admin-orders'] });
            setIsEditing(false);
            setEditData({});
            toast.success(t('admin.orders.detail.updateSuccess'), {
                position: "top-right",
                autoClose: 3000,
            });
        },
        onError: (error: Error) => {
            toast.error(error.message || t('admin.orders.detail.updateError'), {
                position: "top-right",
                autoClose: 5000,
            });
        }
    });

    // Cancel order mutation
    const cancelOrderMutation = useMutation({
        mutationFn: ({ id, reason }: { id: number; reason: string }) => cancelOrder(id, reason),
        onSuccess: (message) => {
            // Refresh the order data to get the updated status
            refetch();
            queryClient.invalidateQueries({ queryKey: ['admin-orders'] });
            setShowCancelModal(false);
            setCancellationReason('');
            toast.success(message || t('admin.orders.detail.cancelSuccess'), {
                position: "top-right",
                autoClose: 3000,
            });
        },
        onError: (error: Error) => {
            toast.error(error.message || t('admin.orders.detail.cancelError'), {
                position: "top-right",
                autoClose: 5000,
            });
        }
    });

    // Confirm order mutation
    const confirmOrderMutation = useMutation({
        mutationFn: ({ orderId, deliveryDate }: { orderId: number; deliveryDate: string }) => confirmOrder(orderId, deliveryDate),
        onSuccess: (message) => {
            // Refresh the order data to get the updated status
            refetch();
            queryClient.invalidateQueries({ queryKey: ['admin-orders'] });
            setShowConfirmModal(false);
            setSelectedDeliveryDate(null);
            toast.success(message || 'Order confirmed successfully', {
                position: "top-right",
                autoClose: 3000,
            });
        },
        onError: (error: Error) => {
            toast.error(error.message || 'Failed to confirm order', {
                position: "top-right",
                autoClose: 5000,
            });
        }
    });

    // Mark unreachable mutation
    const markUnreachableMutation = useMutation({
        mutationFn: ({ orderId, type }: { orderId: number; type: 'UNREACHABLE_1' | 'UNREACHABLE_2' }) => markOrderUnreachable(orderId, type),
        onSuccess: (message) => {
            // Refresh the order data to get the updated status
            refetch();
            queryClient.invalidateQueries({ queryKey: ['admin-orders'] });
            setShowUnreachableModal(false);
            setUnreachableType(null);

            // Show success toast with order ID
            const successKey = unreachableType === 'UNREACHABLE_1' ? 'unreachable.successMessage1' : 'unreachable.successMessage2';
            toast.success(t(`admin.orders.detail.${successKey}`, { id: orderId }), {
                position: "top-right",
                autoClose: 3000,
            });
        },
        onError: (error: Error) => {
            toast.error(error.message || 'Failed to mark order as unreachable', {
                position: "top-right",
                autoClose: 5000,
            });
        }
    });

    const [facilityData, setFacilityData] = useState<Array<{ id: string; name: string; polygon: LatLng[] }>>([]);
    const [terminalIds, setTerminalIds] = useState<Array<{ id: string; name: string; }>>([]);

    useEffect(() => {
        const fetchFacilityData = async () => {
            const data = await getAllTerminalPolygons();
            setFacilityData(data);
            // unique set of terminal ids
            const uniqueTerminalIds = Array<{ id: string; name: string; }>();
            const set = new Set<string>();

            for (const terminal of data) {
                if (!set.has(terminal.id)) {
                    set.add(terminal.id);
                    uniqueTerminalIds.push({ id: terminal.id, name: terminal.name });
                }
            }
            setTerminalIds(uniqueTerminalIds);
        };
        fetchFacilityData();
    }, []);

    // No need for loading SKUs here as the modal will handle it

    const [facilityInPolygon, setFacilityInPolygon] = useState<string | null>(null);

    useEffect(() => {
        const currentLocation = isEditing ? editData.deliveryLocation : order?.deliveryLocation;
        if (!currentLocation) {
            setFacilityInPolygon(null);
            return;
        }
        const fetchFacilityInPolygon = async () => {
            const [lat, lng] = currentLocation.split(' ').map(Number);
            const facility = await getClosestFacility(lat, lng);
            setFacilityInPolygon(facility);
        }
        fetchFacilityInPolygon();
    }, [isEditing, editData.deliveryLocation, order?.deliveryLocation]);

    useEffect(() => {
        if (order != null && order.createdBy != null) {
            return;
        }
        const facility = facilityInPolygon;
        if (facility && isEditing) {
            setEditData(prev => ({ ...prev, facilityKey: facility }));
        }
    }, [facilityInPolygon, isEditing]);

    // Initialize edit data when entering edit mode
    const handleEditToggle = () => {
        if (!isEditing && order) {
            setEditData({
                status: order.status,
                mobile: order.mobile,
                facilityKey: order.facilityKey,
                deliveryLocation: order.deliveryLocation,
                deliveryDate: order.deliveryDate,
                skuItems: [...order.skuItems],
                metadata: order.metadata ? { ...order.metadata } : {}
            });
        }
        setIsEditing(!isEditing);
    };

    const handleSave = async () => {
        if (!order || !editData) return;

        const updateData: UpdateOrderRequest = {
            id: order.id,
            status: editData.status as OrderStatus,
            mobile: editData.mobile,
            facilityKey: editData.facilityKey,
            deliveryLocation: editData.deliveryLocation,
            deliveryDate: editData.deliveryDate,
            skuItems: editData.skuItems,
            metadata: editData.metadata
        };

        updateOrderMutation.mutate(updateData);
    };

    const handleCancel = () => {
        setIsEditing(false);
        setEditData({});
    };

    const handleFieldChange = useCallback((field: keyof Order, value: unknown) => {
        setEditData(prev => ({ ...prev, [field]: value }));
    }, []);

    const handleSkuItemChange = (index: number, field: keyof OrderItem, value: unknown) => {
        setEditData(prev => ({
            ...prev,
            skuItems: prev.skuItems?.map((item, i) =>
                i === index ? { ...item, [field]: value } : item
            )
        }));
    };

    const handleAddSkuItem = () => {
        setShowSkuModal(true);
    };

    const handleSkuModalSelection = (selectedSkus: SKU[]) => {
        // Use real SKU data with actual pricing information
        const newItems: OrderItem[] = selectedSkus.map(sku => ({
            skuID: sku.skuId,
            unit: "PIECES",
            price: sku.sellingPrice || 0, // Use real selling price from SKU, fallback to 0
            quantity: 1,
            skuName: sku.name.en,
            skuImage: sku.imageUrl,
        }));

        setEditData(prev => ({
            ...prev,
            skuItems: [...(prev.skuItems || []), ...newItems]
        }));

        // Store SKU details in the details cache for immediate display
        const skuDetailsToAdd = selectedSkus.reduce((acc, sku) => {
            acc[sku.skuId] = sku;
            return acc;
        }, {} as Record<number, SKU>);

        setSkuDetails(prev => ({ ...prev, ...skuDetailsToAdd }));

        setShowSkuModal(false);
    };

    const handleRemoveSkuItem = (index: number) => {
        setEditData(prev => ({
            ...prev,
            skuItems: prev.skuItems?.filter((_, i) => i !== index)
        }));
    };

    const handleMetadataChange = (key: keyof OrderMetadata, value: string) => {
        setEditData(prev => ({
            ...prev,
            metadata: {
                ...(prev.metadata || {}),
                [key]: value
            }
        }));
    };

    const handleCancelOrder = () => {
        setShowCancelModal(true);
        setCancellationReason('');
    };

    const handleConfirmCancellation = () => {
        if (!order) return;

        if (!cancellationReason.trim()) {
            toast.error(t('admin.orders.detail.cancellation.reasonRequired'), {
                position: "top-right",
                autoClose: 3000,
            });
            return;
        }

        cancelOrderMutation.mutate({
            id: order.id,
            reason: cancellationReason.trim()
        });
    };

    const handleConfirmOrder = () => {
        setSelectedDeliveryDate(null);
        setShowConfirmModal(true);
    };

    const handleConfirmConfirmation = () => {
        if (!selectedDeliveryDate) {
            toast.error(t('admin.orders.detail.confirmation.deliveryDate.required'), {
                position: "top-right",
                autoClose: 3000,
            });
            return;
        }

        // Calculate delivery date based on selection
        const today = new Date();
        const deliveryDate = selectedDeliveryDate === 'today' ? today : new Date(today.getTime() + 24 * 60 * 60 * 1000);
        const deliveryDateISO = deliveryDate.toISOString().split('T')[0];

        confirmOrderMutation.mutate({ orderId, deliveryDate: deliveryDateISO });
    };

    const handleMarkUnreachable = (type: 'UNREACHABLE_1' | 'UNREACHABLE_2') => {
        setUnreachableType(type);
        setShowUnreachableModal(true);
    };

    const handleConfirmUnreachable = () => {
        if (!unreachableType) return;
        markUnreachableMutation.mutate({ orderId, type: unreachableType });
    };

    // Parse delivery location string into coordinates
    const deliveryCoords = useMemo(() => {
        const currentLocation = editData.deliveryLocation || order?.deliveryLocation;
        if (!currentLocation) return null;

        try {
            // Try to parse as "lat lng" format (space-separated)
            const parts = currentLocation.split(/\s+/); // Split by whitespace
            if (parts.length === 2) {
                const lat = parseFloat(parts[0].trim());
                const lng = parseFloat(parts[1].trim());
                if (!isNaN(lat) && !isNaN(lng)) {
                    return { lat, lng };
                }
            }
        } catch (error) {
            console.warn('Error parsing delivery location:', error);
        }
        return null;
    }, [editData.deliveryLocation, order?.deliveryLocation]);

    // Handle location selection from map
    const handleLocationSelect = useCallback((location: { lat: number; lng: number }) => {
        const locationString = `${location.lat} ${location.lng}`;
        console.log('Admin Orders: Location selected:', location, 'formatted as:', locationString);
        setEditData(prev => ({
            ...prev,
            deliveryLocation: locationString
        }));
    }, []);

    // Function to fetch SKU details for missing SKUs
    const fetchMissingSkuDetails = useCallback(async (skuIds: number[]) => {
        // Filter out invalid SKU IDs (0, negative numbers, etc.)
        const validSkuIds = skuIds.filter(skuId => skuId && skuId > 0);
        const missingSkuIds = validSkuIds.filter(skuId => !skuDetails[skuId]);

        if (missingSkuIds.length === 0) return;

        logger.debug(`OrderDetail: Fetching details for ${missingSkuIds.length} missing SKUs`, { missingSkuIds });

        for (const skuId of missingSkuIds) {
            try {
                const skuDetail = await getSkuById(skuId, { allowInactive: true });
                if (skuDetail) {
                    setSkuDetails(prev => ({ ...prev, [skuId]: skuDetail }));
                    logger.debug(`OrderDetail: Successfully fetched SKU details for ID ${skuId}`);
                } else {
                    logger.warn(`OrderDetail: No SKU details found for ID ${skuId}`);
                }
            } catch (error) {
                logger.error(`Failed to fetch SKU details for ID ${skuId}`, error);
            }
        }
    }, [skuDetails]);

    // Fetch SKU details for initial order items
    useEffect(() => {
        if (order?.skuItems && order.skuItems.length > 0) {
            const skuIds = order.skuItems.map(item => item.skuID);
            fetchMissingSkuDetails(skuIds);
        }
    }, [order?.skuItems]);

    // Fetch SKU details for edit data items when they change
    useEffect(() => {
        if (isEditing && editData.skuItems && editData.skuItems.length > 0) {
            const skuIds = editData.skuItems.map(item => item.skuID);
            fetchMissingSkuDetails(skuIds);
        }
    }, [editData.skuItems, isEditing]);

    // Debug: Log SKU details state changes
    useEffect(() => {
        logger.debug('OrderDetail: SKU details state updated', {
            totalSkuDetails: Object.keys(skuDetails).length,
            skuDetailIds: Object.keys(skuDetails),
            orderSkuIds: order?.skuItems?.map(item => item.skuID) || []
        });

        // Debug: Check for problematic objects in order data
        if (order?.metadata) {
            logger.debug('OrderDetail: Order metadata', order.metadata);
            // Check if metadata contains any non-string values that could cause rendering issues
            Object.entries(order.metadata).forEach(([key, value]) => {
                if (typeof value === 'object' && value !== null) {
                    logger.warn(`OrderDetail: Found object in metadata.${key}`, { key, value });
                }
            });
        }
    }, [skuDetails, order?.skuItems, order?.metadata]);

    if (isNaN(orderId)) {
        return (
            <AdminGuard requiredPermission="viewAllOrders">
                <div className="p-6">
                    <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                        <h3 className="text-lg font-medium text-red-800">
                            {t('admin.orders.detail.invalidId')}
                        </h3>
                    </div>
                </div>
            </AdminGuard>
        );
    }

    if (isLoading) {
        return (
            <AdminGuard requiredPermission="viewAllOrders">
                <div className="p-6">
                    <div className="animate-pulse space-y-6">
                        <div className="h-8 bg-gray-200 rounded w-1/4"></div>
                        <div className="bg-white p-6 rounded-lg shadow space-y-4">
                            <div className="h-6 bg-gray-200 rounded w-1/3"></div>
                            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                            <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                        </div>
                    </div>
                </div>
            </AdminGuard>
        );
    }

    if (isError || !order) {
        return (
            <AdminGuard requiredPermission="viewAllOrders">
                <div className="p-6">
                    <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                        <h3 className="text-lg font-medium text-red-800 mb-2">
                            {t('admin.orders.detail.error.title')}
                        </h3>
                        <p className="text-red-600 mb-4">
                            {error?.message || t('admin.orders.detail.error.notFound')}
                        </p>
                        <div className="flex space-x-3">
                            <button
                                onClick={() => refetch()}
                                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
                            >
                                {t('admin.orders.error.retry')}
                            </button>
                            <button
                                onClick={() => {
                                    // Preserve filter state when navigating back from error state too
                                    const returnFilters = searchParams.get('returnFilters');
                                    if (returnFilters) {
                                        const decodedFilters = decodeURIComponent(returnFilters);
                                        router.push(`/admin/orders?${decodedFilters}`);
                                    } else {
                                        router.push('/admin/orders');
                                    }
                                }}
                                className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
                            >
                                {t('admin.actions.back')}
                            </button>
                        </div>
                    </div>
                </div>
            </AdminGuard>
        );
    }

    const currentData = isEditing ? { ...order, ...editData } : order;
    const totalValue = calculateOrderTotal(currentData);
    const quantityConfig = getQuantityDisplayConfig(currentData.status);


    return (
        <AdminGuard requiredPermission="viewAllOrders">
            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <button
                            onClick={() => {
                                // Check if we have returnFilters to preserve filter state
                                const returnFilters = searchParams.get('returnFilters');
                                if (returnFilters) {
                                    const decodedFilters = decodeURIComponent(returnFilters);
                                    logger.debug('Returning to orders with filters', { decodedFilters });
                                    router.push(`/admin/orders?${decodedFilters}`);
                                } else {
                                    router.push('/admin/orders');
                                }
                            }}
                            className="text-gray-400 hover:text-gray-600"
                        >
                            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                            </svg>
                        </button>
                        <div>
                            <h1 className="text-2xl font-bold text-gray-900">
                                {t('admin.orders.detail.title', { id: order.id })}
                            </h1>
                            <p className="text-gray-600 mt-1">
                                {t('admin.orders.detail.subtitle')}
                            </p>
                        </div>
                    </div>
                    <div className="flex items-center space-x-3">
                        {isEditing ? (
                            <>
                                <button
                                    onClick={handleCancel}
                                    disabled={updateOrderMutation.isPending}
                                    className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors disabled:opacity-50"
                                >
                                    {t('admin.actions.dismiss')}
                                </button>
                                <button
                                    onClick={handleSave}
                                    disabled={updateOrderMutation.isPending}
                                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50"
                                >
                                    {updateOrderMutation.isPending ? t('admin.orders.detail.saving') : t('admin.orders.detail.updateOrder')}
                                </button>
                            </>
                        ) : (
                            // Only show edit button if order is not confirmed or cancelled
                            currentData.status.toLowerCase() !== 'confirmed' && currentData.status.toLowerCase() !== 'cancelled' && (
                                <button
                                    onClick={handleEditToggle}
                                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                                >
                                    {t('admin.actions.edit')}
                                </button>
                            )
                        )}
                    </div>
                </div>

                {/* Order Overview */}
                <div className="bg-white p-6 rounded-lg shadow">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">
                        {t('admin.orders.detail.overview.title')}
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                {t('admin.orders.detail.overview.status')}
                            </label>
                            <div className="space-y-2">
                                <StatusBadge status={currentData.status.toLowerCase()} />
                                {currentData.status.toLowerCase() === 'confirmed' && (
                                    <p className="text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded">
                                        {t('admin.orders.detail.overview.confirmedNote')}
                                    </p>
                                )}
                            </div>
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                {t('admin.orders.detail.overview.total')}
                            </label>
                            <p className="text-lg font-semibold text-green-600">₹{totalValue.toFixed(2)}</p>
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                {t('admin.orders.detail.overview.items')}
                            </label>
                            <p className="text-lg font-medium">{currentData.skuItems.length}</p>
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                {t('admin.orders.detail.overview.facility')}
                            </label>
                            {isEditing ? (
                                <select
                                    value={editData.facilityKey || order.facilityKey}
                                    onChange={(e) => handleFieldChange('facilityKey', e.target.value)}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white"
                                >
                                    <option value="">{t('admin.orders.detail.overview.selectFacility')}</option>
                                    {order.facilityKey != null && order.facilityKey !== '' && !(terminalIds.some(t => t.id == order.facilityKey)) && (
                                        <option value={order.facilityKey}>{order.facilityKey}</option>
                                    )}
                                    {terminalIds.map(terminal => (
                                        <option key={terminal.id} value={terminal.id}>{`${terminal.id} - ${terminal.name}`}</option>
                                    ))}
                                </select>
                            ) : (
                                <p className="text-gray-900">{currentData.facilityKey}</p>
                            )}
                        </div>

                    </div>
                    {currentData.status.toLowerCase() === 'cancelled' && currentData.metadata && currentData.metadata.reason && (
                        <div className="bg-red-50 border border-red-200 rounded-md p-3 mt-4">
                            <p className="text-sm font-medium text-red-800 mb-1">
                                {t('admin.orders.detail.overview.cancellationReason')}
                            </p>
                            <p className="text-sm text-red-700">
                                {safeRender(currentData.metadata.reason)}
                            </p>
                        </div>
                    )}

                    {/* Order Actions - Only visible in non-editing mode */}
                    {!isEditing && currentData.status.toLowerCase() !== 'cancelled' && (
                        <div className="flex items-center justify-between mt-6 pt-4 border-t border-gray-200">
                            {/* Created By Information */}
                            <div className="text-sm text-gray-600">
                                {currentData.createdBy
                                    ? t('admin.orders.detail.overview.createdByUser', { userId: currentData.createdBy })
                                    : t('admin.orders.detail.overview.createdByBhumi')
                                }
                            </div>

                            {/* Action Buttons */}
                            <div className="flex items-center space-x-3">
                                {/* Cancel Order button - available for all non-cancelled orders */}
                                <button
                                    onClick={handleCancelOrder}
                                    disabled={cancelOrderMutation.isPending}
                                    className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors disabled:opacity-50"
                                >
                                    {cancelOrderMutation.isPending ? t('admin.orders.detail.cancelling') : t('admin.orders.detail.cancelOrder')}
                                </button>

                                {/* Not Reachable button - show based on status */}
                                {currentData.status.toLowerCase() === 'new' && (
                                    <button
                                        onClick={() => handleMarkUnreachable('UNREACHABLE_1')}
                                        disabled={markUnreachableMutation.isPending}
                                        className="px-4 py-2 bg-amber-600 text-white rounded-md hover:bg-amber-700 transition-colors disabled:opacity-50 flex items-center space-x-2"
                                    >
                                        <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 8l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2M3 16.5v2A1.5 1.5 0 004.5 20h2M3 16.5l6-6 2.5 2.5M3 16.5L1.5 15M20.5 16.5L19 15m1.5 1.5v2a1.5 1.5 0 01-1.5 1.5h-2m1.5-1.5l-6-6-2.5 2.5" />
                                        </svg>
                                        <span>{markUnreachableMutation.isPending ? t('admin.orders.detail.markingUnreachable') : t('admin.orders.detail.notReachable1')}</span>
                                    </button>
                                )}

                                {currentData.status.toLowerCase() === 'unreachable_1' && (
                                    <button
                                        onClick={() => handleMarkUnreachable('UNREACHABLE_2')}
                                        disabled={markUnreachableMutation.isPending}
                                        className="px-4 py-2 bg-amber-600 text-white rounded-md hover:bg-amber-700 transition-colors disabled:opacity-50 flex items-center space-x-2"
                                    >
                                        <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 8l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2M3 16.5v2A1.5 1.5 0 004.5 20h2M3 16.5l6-6 2.5 2.5M3 16.5L1.5 15M20.5 16.5L19 15m1.5 1.5v2a1.5 1.5 0 01-1.5 1.5h-2m1.5-1.5l-6-6-2.5 2.5" />
                                        </svg>
                                        <span>{markUnreachableMutation.isPending ? t('admin.orders.detail.markingUnreachable') : t('admin.orders.detail.notReachable2')}</span>
                                    </button>
                                )}

                                {/* Confirm Order button - for new and unreachable orders */}
                                {['new', 'unreachable_1', 'unreachable_2'].includes(currentData.status.toLowerCase()) && (
                                    <div
                                        className="relative"
                                        onMouseEnter={() => {
                                            const isFacilityMissing = !currentData.facilityKey || currentData.facilityKey.trim() === '';
                                            if (isFacilityMissing) {
                                                setShowFacilityTooltip(true);
                                            }
                                        }}
                                        onMouseLeave={() => {
                                            setShowFacilityTooltip(false);
                                        }}
                                    >
                                        <button
                                            onClick={handleConfirmOrder}
                                            disabled={confirmOrderMutation.isPending || !currentData.facilityKey || currentData.facilityKey.trim() === ''}
                                            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                        >
                                            {confirmOrderMutation.isPending ? t('admin.orders.detail.confirming') : t('admin.orders.detail.confirmOrder')}
                                        </button>

                                        {/* Custom Tooltip */}
                                        {showFacilityTooltip && (!currentData.facilityKey || currentData.facilityKey.trim() === '') && (
                                            <div className="absolute bottom-full right-0 mb-2 px-3 py-2 bg-red-600 text-white text-sm rounded-lg shadow-lg whitespace-nowrap z-[9999] min-w-max">
                                                <div className="text-center">
                                                    {t('admin.orders.detail.facilityRequired.message')}
                                                </div>
                                                {/* Tooltip arrow */}
                                                <div className="absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-red-600"></div>
                                            </div>
                                        )}
                                    </div>
                                )}
                            </div>
                        </div>
                    )}
                </div>

                {/* Customer Information */}
                <div className="bg-white p-6 rounded-lg shadow">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">
                        {t('admin.orders.detail.customer.title')}
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                {t('admin.orders.table.endCustomerName')}
                            </label>
                            <p className="text-gray-900">{safeRender(currentData.endCustomerName) || t('admin.orders.table.noEndCustomerName')}</p>
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                {t('admin.orders.detail.customer.mobile')}
                            </label>
                            <p className="text-gray-900">{safeRender(currentData.mobile) || t('admin.orders.table.noMobile')}</p>
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                {t('admin.orders.detail.customer.deliveryDate')}
                            </label>

                            <p className="text-gray-900">{safeRender(formatDeliveryDate(currentData.deliveryDate))}</p>

                        </div>
                    </div>
                </div>

                {/* Delivery Location */}
                <div className="bg-white p-6 rounded-lg shadow">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">
                        {t('admin.orders.detail.location.title')}
                    </h3>
                    <div className="grid grid-cols-1 gap-6">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                {t('admin.orders.detail.location.address')}
                            </label>
                            {isEditing ? (
                                <input
                                    type="text"
                                    value={editData.metadata?.deliveryAddress || ''}
                                    onChange={(e) => handleMetadataChange('deliveryAddress', e.target.value)}
                                    placeholder={t('admin.orders.detail.location.addressPlaceholder')}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                />
                            ) : (
                                <p className="text-gray-900 px-3 py-2 border border-gray-200 rounded-md bg-gray-50">
                                    {safeRender(currentData.metadata?.deliveryAddress) || t('admin.orders.detail.location.noAddress')}
                                </p>
                            )}
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                {t('admin.orders.detail.location.coordinates')}
                            </label>
                            {isEditing ? (
                                <input
                                    type="text"
                                    value={editData.deliveryLocation || ''}
                                    onChange={(e) => setEditData({ ...editData, deliveryLocation: e.target.value })}
                                    placeholder={t('admin.orders.detail.location.coordinatesPlaceholder')}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                />
                            ) : (
                                <p className="text-gray-900 px-3 py-2 border border-gray-200 rounded-md bg-gray-50">
                                    {safeRender(currentData.deliveryLocation)}
                                </p>
                            )}
                        </div>

                        {/* Serviceability Status */}
                        {(
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    {t('admin.orders.detail.location.serviceability')}
                                </label>
                                {facilityInPolygon ? (
                                    <div className="flex items-center px-3 py-2 border border-green-200 rounded-md bg-green-50">
                                        <svg className="h-5 w-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        <span className="text-sm font-medium text-green-800">
                                            {t('admin.orders.detail.location.serviceableWith', { facility: facilityInPolygon })}
                                        </span>
                                    </div>
                                ) : (
                                    <div className="flex items-center px-3 py-2 border border-red-200 rounded-md bg-red-50">
                                        <svg className="h-5 w-5 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.966-.833-2.736 0L3.732 16c-.77.833.192 2.5 1.732 2.5z" />
                                        </svg>
                                        <span className="text-sm font-medium text-red-800">
                                            {t('admin.orders.detail.location.notServiceable')}
                                        </span>
                                    </div>
                                )}
                            </div>
                        )}

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                {t('admin.orders.detail.location.map')}
                            </label>
                            <MapSelector
                                initialLocation={deliveryCoords || undefined}
                                onLocationSelect={handleLocationSelect}
                                disabled={!isEditing}
                                showVillagePolygons={true}
                                facilityData={facilityData}
                            />
                        </div>
                    </div>
                </div>

                {/* Order Items */}
                <div className="bg-white rounded-lg shadow">
                    <div className="px-6 py-4 border-b border-gray-200">
                        <div className="flex items-center justify-between">
                            <div>
                                <h3 className="text-lg font-medium text-gray-900">
                                    {t('admin.orders.detail.items.title')} ({currentData.skuItems.length})
                                </h3>
                                <p className="text-sm text-gray-500 mt-1">
                                    {isEditing ? t('admin.orders.detail.items.editMode') : t('admin.orders.detail.items.viewMode')}
                                </p>
                            </div>
                            {isEditing && (
                                <button
                                    onClick={handleAddSkuItem}
                                    className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center space-x-2 transition-colors"
                                >
                                    <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                    </svg>
                                    <span>{t('admin.orders.detail.items.addItem')}</span>
                                </button>
                            )}
                        </div>
                    </div>

                    <div className="overflow-x-auto">
                        {currentData.skuItems.length === 0 ? (
                            <div className="text-center py-12">
                                <div className="h-12 w-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <svg className="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                                    </svg>
                                </div>
                                <p className="text-gray-500 mb-2">{t('admin.orders.detail.items.emptyOrder')}</p>
                                {isEditing && (
                                    <button
                                        onClick={handleAddSkuItem}
                                        className="text-blue-600 hover:text-blue-800 underline"
                                    >
                                        {t('admin.orders.detail.items.addSomeItems')}
                                    </button>
                                )}
                            </div>
                        ) : (
                            <table className="min-w-full divide-y divide-gray-200">
                                <thead className="bg-gray-50">
                                    <tr>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            {t('admin.orders.detail.items.product')}
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            {t('admin.orders.detail.items.unit')}
                                        </th>
                                        {quantityConfig.showOrdered && (
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                {t('admin.orders.detail.items.orderedQty')}
                                            </th>
                                        )}
                                        {quantityConfig.showPicked && (
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                {t('admin.orders.detail.items.pickedQty')}
                                            </th>
                                        )}
                                        {quantityConfig.showDelivered && (
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                {t('admin.orders.detail.items.deliveredQty')}
                                            </th>
                                        )}
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            {t('admin.orders.detail.items.price')}
                                        </th>
                                        {quantityConfig.showOrdered && (
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                {t('admin.orders.detail.items.orderedTotal')}
                                            </th>
                                        )}
                                        {isEditing && (
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                {t('admin.actions.actions')}
                                            </th>
                                        )}
                                    </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                    {currentData.skuItems.map((item, index) => {
                                        const skuDetail = skuDetails[item.skuID];
                                        const isInvalidSku = !item.skuID || item.skuID <= 0;

                                        return (
                                            <tr key={index} className="hover:bg-gray-50">
                                                <td className="px-6 py-4">
                                                    <div className="flex items-center">
                                                        <div className="h-10 w-10 flex-shrink-0">
                                                            <Image
                                                                src={skuDetail?.imageUrl || '/placeholder-product.png'}
                                                                alt={skuDetail?.name?.en || `SKU ${item.skuID}`}
                                                                width={40}
                                                                height={40}
                                                                className="h-10 w-10 rounded-lg object-cover"
                                                            />
                                                        </div>
                                                        <div className="ml-4">
                                                            <div className={`text-sm font-medium ${isInvalidSku ? 'text-red-600' : 'text-gray-900'}`}>
                                                                #{item.skuID}
                                                                {isInvalidSku && (
                                                                    <span className="ml-2 text-xs bg-red-100 text-red-800 px-2 py-1 rounded">
                                                                        {t('admin.orders.detail.items.invalidSku')}
                                                                    </span>
                                                                )}
                                                            </div>
                                                            {skuDetail ? (
                                                                <>
                                                                    <div className="text-sm font-medium text-gray-900">{skuDetail.name.en}</div>
                                                                    {skuDetail.name.ta && (
                                                                        <div className="text-sm text-gray-500">{skuDetail.name.ta}</div>
                                                                    )}
                                                                </>
                                                            ) : !isInvalidSku ? (
                                                                <div className="text-sm text-gray-500 italic">{t('admin.orders.detail.items.loadingSkuDetails')}</div>
                                                            ) : (
                                                                <div className="text-sm text-red-500 italic">{t('admin.orders.detail.items.skuDetailsUnavailable')}</div>
                                                            )}
                                                        </div>
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                        {item.unit}
                                                    </span>
                                                </td>
                                                {quantityConfig.showOrdered && (
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        {isEditing ? (
                                                            <input
                                                                type="number"
                                                                step="1"
                                                                min="0"
                                                                value={item.quantity}
                                                                onChange={(e) => handleSkuItemChange(index, 'quantity', Math.floor(parseFloat(e.target.value)) || 0)}
                                                                onBlur={(e) => {
                                                                    const value = Math.floor(parseFloat(e.target.value)) || 0;
                                                                    e.target.value = value.toString();
                                                                    handleSkuItemChange(index, 'quantity', value);
                                                                }}
                                                                className="w-20 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                                                            />
                                                        ) : (
                                                            <span className="text-sm font-medium text-gray-900">{item.quantity}</span>
                                                        )}
                                                    </td>
                                                )}
                                                {quantityConfig.showPicked && (
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <span className={`text-sm font-medium px-2 py-1 rounded ${getQuantityDisplayStyle(item, 'picked')}`}>
                                                            {getQuantityValue(item, 'picked') !== null ? getQuantityValue(item, 'picked') : t('admin.orders.detail.items.notSet')}
                                                        </span>
                                                    </td>
                                                )}
                                                {quantityConfig.showDelivered && (
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <span className={`text-sm font-medium px-2 py-1 rounded ${getQuantityDisplayStyle(item, 'delivered')}`}>
                                                            {getQuantityValue(item, 'delivered') !== null ? getQuantityValue(item, 'delivered') : t('admin.orders.detail.items.notSet')}
                                                        </span>
                                                    </td>
                                                )}
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    {/* {isEditing ? (
                                                        <div className="flex items-center space-x-1">
                                                            <span className="text-sm text-gray-500">₹</span>
                                                            <input
                                                                type="number"
                                                                step="1"
                                                                min="0"
                                                                value={item.price}
                                                                onChange={(e) => handleSkuItemChange(index, 'price', Math.floor(parseFloat(e.target.value)) || 0)}
                                                                onBlur={(e) => {
                                                                    const value = Math.floor(parseFloat(e.target.value)) || 0;
                                                                    e.target.value = value.toString();
                                                                    handleSkuItemChange(index, 'price', value);
                                                                }}
                                                                className="w-24 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                                                            />
                                                        </div>
                                                    ) : ( */}
                                                    <div className="text-sm text-gray-900">
                                                        <div className="font-medium">₹{item.price.toFixed(2)}</div>
                                                    </div>
                                                    {/* )} */}
                                                </td>
                                                {quantityConfig.showOrdered && (
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <span className="text-sm font-semibold text-green-600">
                                                            ₹{calculateTotal(item, 'ordered').toFixed(2)}
                                                        </span>
                                                    </td>
                                                )}
                                                {isEditing && (
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <button
                                                            onClick={() => handleRemoveSkuItem(index)}
                                                            className="text-red-600 hover:text-red-800 focus:outline-none focus:ring-2 focus:ring-red-500 rounded p-1 transition-colors"
                                                            title={t('admin.actions.delete')}
                                                        >
                                                            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                            </svg>
                                                        </button>
                                                    </td>
                                                )}
                                            </tr>
                                        );
                                    })}
                                </tbody>
                                <tfoot className="bg-gray-50">
                                    <tr>
                                        <td colSpan={2} className="px-6 py-4 text-right text-sm font-medium text-gray-900">
                                            {t('admin.orders.detail.items.orderTotal')}
                                        </td>
                                        {quantityConfig.showOrdered && (
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <span className="text-sm font-bold text-green-600">
                                                    ₹{currentData.skuItems.reduce((total, item) => total + calculateTotal(item, 'ordered'), 0).toFixed(2)}
                                                </span>
                                            </td>
                                        )}
                                        {quantityConfig.showPicked && (
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <span className="text-sm font-bold text-blue-600">
                                                    ₹{currentData.skuItems.reduce((total, item) => total + calculateTotal(item, 'picked'), 0).toFixed(2)}
                                                </span>
                                            </td>
                                        )}
                                        {quantityConfig.showDelivered && (
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <span className="text-sm font-bold text-purple-600">
                                                    ₹{currentData.skuItems.reduce((total, item) => total + calculateTotal(item, 'delivered'), 0).toFixed(2)}
                                                </span>
                                            </td>
                                        )}
                                        <td className="px-6 py-4 text-center text-xs text-gray-500">
                                            {t('admin.orders.detail.items.price')}
                                        </td>
                                        {quantityConfig.showOrdered && (
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <span className="text-lg font-bold text-green-600">
                                                    ₹{currentData.skuItems.reduce((total, item) => total + calculateTotal(item, 'ordered'), 0).toFixed(2)}
                                                </span>
                                            </td>
                                        )}
                                        {isEditing && <td></td>}
                                    </tr>
                                </tfoot>
                            </table>
                        )}
                    </div>
                </div>

                {/* Additional Information */}
                <div className="bg-white p-6 rounded-lg shadow">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">
                        {t('admin.orders.detail.additional.title')}
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                {t('admin.orders.detail.additional.deliveryInstructions')}
                            </label>
                            {isEditing ? (
                                <textarea
                                    value={editData.metadata?.deliveryInstruction || ''}
                                    onChange={(e) => handleMetadataChange('deliveryInstruction', e.target.value)}
                                    placeholder={t('admin.orders.detail.additional.deliveryInstructionsPlaceholder')}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                                    rows={3}
                                />
                            ) : (
                                <div className="min-h-[76px] px-3 py-2 border border-gray-200 rounded-md bg-gray-50">
                                    {currentData.metadata?.deliveryInstruction ? (
                                        <p className="text-sm text-gray-900">{currentData.metadata.deliveryInstruction}</p>
                                    ) : (
                                        <p className="text-sm text-gray-500 italic">
                                            {t('admin.orders.detail.additional.noDeliveryInstructions')}
                                        </p>
                                    )}
                                </div>
                            )}
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                {t('admin.orders.detail.additional.orderComments')}
                            </label>
                            {isEditing ? (
                                <textarea
                                    value={editData.metadata?.orderComments || ''}
                                    onChange={(e) => handleMetadataChange('orderComments', e.target.value)}
                                    placeholder={t('admin.orders.detail.additional.orderCommentsPlaceholder')}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                                    rows={3}
                                />
                            ) : (
                                <div className="min-h-[76px] px-3 py-2 border border-gray-200 rounded-md bg-gray-50">
                                    {currentData.metadata?.orderComments ? (
                                        <p className="text-sm text-gray-900">{safeRender(currentData.metadata.orderComments)}</p>
                                    ) : (
                                        <p className="text-sm text-gray-500 italic">
                                            {t('admin.orders.detail.additional.noOrderComments')}
                                        </p>
                                    )}
                                </div>
                            )}
                        </div>
                    </div>
                </div>

                {/* Fulfillment Details */}
                <div className="bg-white p-6 rounded-lg shadow">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">
                        {t('admin.orders.detail.fulfillment.title')}
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                {t('admin.orders.detail.fulfillment.rosterId')}
                            </label>
                            <p className="text-gray-900">{safeRender(order.fullfillingOpsRosterId)}</p>
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                {t('admin.orders.detail.fulfillment.orderId')}
                            </label>
                            <p className="text-gray-900">{safeRender(order.orderId) || t('admin.orders.detail.fulfillment.noOrderId')}</p>
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                {t('admin.orders.detail.fulfillment.type')}
                            </label>
                            <p className="text-gray-900">{safeRender(order.type)}</p>
                        </div>
                    </div>
                </div>

                {/* SKU Selection Modal */}
                <SkuSelectionModal
                    isOpen={showSkuModal}
                    onClose={() => setShowSkuModal(false)}
                    onSelect={handleSkuModalSelection}
                    title={t('admin.orders.detail.skuModal.title')}
                    searchPlaceholder={t('admin.orders.detail.skuModal.searchPlaceholder')}
                    filterType="child"
                    activeOnly={true}
                    excludeSkuIds={currentData.skuItems.map(item => item.skuID)}
                />

                {/* Cancellation Modal */}
                <Modal
                    isOpen={showCancelModal}
                    onRequestClose={() => {
                        setShowCancelModal(false);
                        setCancellationReason('');
                    }}
                    contentLabel={t('admin.orders.detail.cancellation.title')}
                    className="relative top-20 mx-auto p-5 border w-11/12 max-w-md shadow-lg rounded-md bg-white focus:outline-none"
                    shouldCloseOnOverlayClick={true}
                    shouldCloseOnEsc={true}
                >
                    <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-medium text-gray-900">
                            {t('admin.orders.detail.cancellation.title')}
                        </h3>
                        <button
                            onClick={() => {
                                setShowCancelModal(false);
                                setCancellationReason('');
                            }}
                            className="text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded"
                        >
                            <span className="sr-only">{t('admin.actions.close')}</span>
                            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>

                    <div className="mb-4">
                        <p className="text-sm text-gray-600 mb-3">
                            {t('admin.orders.detail.cancellation.message')}
                        </p>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            {t('admin.orders.detail.cancellation.reason')}
                        </label>
                        <textarea
                            value={cancellationReason}
                            onChange={(e) => setCancellationReason(e.target.value)}
                            placeholder={t('admin.orders.detail.cancellation.reasonPlaceholder')}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                            rows={3}
                            required
                        />
                    </div>

                    <div className="flex justify-end space-x-3">
                        <button
                            onClick={() => {
                                setShowCancelModal(false);
                                setCancellationReason('');
                            }}
                            disabled={cancelOrderMutation.isPending}
                            className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
                        >
                            {t('admin.actions.cancel')}
                        </button>
                        <button
                            onClick={handleConfirmCancellation}
                            disabled={cancelOrderMutation.isPending || !cancellationReason.trim()}
                            className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-red-500"
                        >
                            {cancelOrderMutation.isPending ? t('admin.orders.detail.cancelling') : t('admin.orders.detail.cancellation.confirm')}
                        </button>
                    </div>
                </Modal>

                {/* Confirmation Modal */}
                <Modal
                    isOpen={showConfirmModal}
                    onRequestClose={() => setShowConfirmModal(false)}
                    contentLabel={t('admin.orders.detail.confirmation.title')}
                    className="relative top-20 mx-auto p-5 border w-11/12 max-w-md shadow-lg rounded-md bg-white focus:outline-none"
                    shouldCloseOnOverlayClick={true}
                    shouldCloseOnEsc={true}
                >
                    <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-medium text-gray-900">
                            {t('admin.orders.detail.confirmation.title')}
                        </h3>
                        <button
                            onClick={() => setShowConfirmModal(false)}
                            className="text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded"
                        >
                            <span className="sr-only">{t('admin.actions.close')}</span>
                            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>

                    <div className="mb-6">
                        <p className="text-sm text-gray-600 mb-4">
                            {t('admin.orders.detail.confirmation.message')}
                        </p>

                        {/* Delivery Date Selection */}
                        <div className="mb-4">
                            <label className="block text-sm font-medium text-gray-700 mb-3">
                                {t('admin.orders.detail.confirmation.deliveryDate.title')}
                            </label>
                            <div className="space-y-2">
                                <label className="flex items-center">
                                    <input
                                        type="radio"
                                        name="deliveryDate"
                                        value="today"
                                        checked={selectedDeliveryDate === 'today'}
                                        onChange={(e) => setSelectedDeliveryDate(e.target.value as 'today')}
                                        className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300"
                                    />
                                    <span className="ml-2 block text-sm text-gray-900">
                                        {t('admin.orders.detail.confirmation.deliveryDate.today')}
                                    </span>
                                </label>
                                <label className="flex items-center">
                                    <input
                                        type="radio"
                                        name="deliveryDate"
                                        value="tomorrow"
                                        checked={selectedDeliveryDate === 'tomorrow'}
                                        onChange={(e) => setSelectedDeliveryDate(e.target.value as 'tomorrow')}
                                        className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300"
                                    />
                                    <span className="ml-2 block text-sm text-gray-900">
                                        {t('admin.orders.detail.confirmation.deliveryDate.tomorrow')}
                                    </span>
                                </label>
                            </div>
                        </div>

                        <div className="bg-green-50 border border-green-200 rounded-md p-3">
                            <div className="flex">
                                <div className="flex-shrink-0">
                                    <svg className="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                    </svg>
                                </div>
                                <div className="ml-3">
                                    <p className="text-sm text-green-700">
                                        {t('admin.orders.detail.confirmation.successMessage', { id: order?.id })}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="flex justify-end space-x-3">
                        <button
                            onClick={() => setShowConfirmModal(false)}
                            disabled={confirmOrderMutation.isPending}
                            className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
                        >
                            {t('admin.orders.detail.confirmation.cancelButton')}
                        </button>
                        <button
                            onClick={handleConfirmConfirmation}
                            disabled={confirmOrderMutation.isPending || !selectedDeliveryDate}
                            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-green-500"
                        >
                            {confirmOrderMutation.isPending ? t('admin.orders.detail.confirming') : t('admin.orders.detail.confirmation.confirmButton')}
                        </button>
                    </div>
                </Modal>

                {/* Unreachable Confirmation Modal */}
                <Modal
                    isOpen={showUnreachableModal}
                    onRequestClose={() => setShowUnreachableModal(false)}
                    contentLabel={unreachableType === 'UNREACHABLE_1' ? t('admin.orders.detail.unreachable.title1') : t('admin.orders.detail.unreachable.title2')}
                    className="relative top-20 mx-auto p-5 border w-11/12 max-w-md shadow-lg rounded-md bg-white focus:outline-none"
                    shouldCloseOnOverlayClick={true}
                    shouldCloseOnEsc={true}
                >
                    <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-medium text-gray-900">
                            {unreachableType === 'UNREACHABLE_1' ? t('admin.orders.detail.unreachable.title1') : t('admin.orders.detail.unreachable.title2')}
                        </h3>
                        <button
                            onClick={() => setShowUnreachableModal(false)}
                            className="text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded"
                        >
                            <span className="sr-only">{t('admin.actions.close')}</span>
                            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>

                    <div className="mb-6">
                        <p className="text-sm text-gray-600 mb-3">
                            {unreachableType === 'UNREACHABLE_1' ? t('admin.orders.detail.unreachable.message1') : t('admin.orders.detail.unreachable.message2')}
                        </p>
                        <div className="bg-amber-50 border border-amber-200 rounded-md p-3">
                            <div className="flex">
                                <div className="flex-shrink-0">
                                    <svg className="h-5 w-5 text-amber-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 8l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2M3 16.5v2A1.5 1.5 0 004.5 20h2M3 16.5l6-6 2.5 2.5M3 16.5L1.5 15M20.5 16.5L19 15m1.5 1.5v2a1.5 1.5 0 01-1.5 1.5h-2m1.5-1.5l-6-6-2.5 2.5" />
                                    </svg>
                                </div>
                                <div className="ml-3">
                                    <p className="text-sm text-amber-700">
                                        This will mark the order as {unreachableType === 'UNREACHABLE_1' ? 'Not Reachable 1' : 'Not Reachable 2'} and track the attempt.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="flex justify-end space-x-3">
                        <button
                            onClick={() => setShowUnreachableModal(false)}
                            disabled={markUnreachableMutation.isPending}
                            className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
                        >
                            {t('admin.orders.detail.unreachable.cancelButton')}
                        </button>
                        <button
                            onClick={handleConfirmUnreachable}
                            disabled={markUnreachableMutation.isPending}
                            className="px-4 py-2 bg-amber-600 text-white rounded-lg hover:bg-amber-700 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-amber-500"
                        >
                            {markUnreachableMutation.isPending ? t('admin.orders.detail.markingUnreachable') : (unreachableType === 'UNREACHABLE_1' ? t('admin.orders.detail.unreachable.confirmButton1') : t('admin.orders.detail.unreachable.confirmButton2'))}
                        </button>
                    </div>
                </Modal>
            </div>
        </AdminGuard>
    );
};

export default OrderDetailPage;
'use client';

import React, { useState } from 'react';
import { debugSkuCache, clearCachedSkus, getSkus, getSkusByCategory, getSkuById } from '../../services/skuService';
import { AdminGuard } from '../../components/common/AdminGuard';

export default function DebugCachePage() {
    const [cacheInfo, setCacheInfo] = useState<any>(null);
    const [loading, setLoading] = useState(false);
    const [logs, setLogs] = useState<string[]>([]);

    const addLog = (message: string) => {
        const timestamp = new Date().toLocaleTimeString();
        setLogs(prev => [...prev, `[${timestamp}] ${message}`]);
    };

    const checkCache = async () => {
        setLoading(true);
        addLog('Checking cache...');
        try {
            const info = await debugSkuCache();
            setCacheInfo(info);
            addLog(`Cache contains ${info.totalSkus} SKUs`);
        } catch (error) {
            addLog(`Error checking cache: ${error}`);
        } finally {
            setLoading(false);
        }
    };

    const fetchAllSkus = async () => {
        setLoading(true);
        addLog('Fetching all SKUs (no params)...');
        try {
            const skus = await getSkus({ allowInactive: true }); // Admin debug can see inactive SKUs
            addLog(`Fetched ${skus ? skus.length : 0} SKUs`);
            await checkCache();
        } catch (error) {
            addLog(`Error fetching SKUs: ${error}`);
        } finally {
            setLoading(false);
        }
    };

    const fetchCategorySkus = async () => {
        setLoading(true);
        addLog('Fetching SKUs for category 1...');
        try {
            const skus = await getSkusByCategory('1', { allowInactive: true }); // Admin debug can see inactive SKUs
            addLog(`Fetched ${skus ? skus.length : 0} SKUs for category 1`);
            await checkCache();
        } catch (error) {
            addLog(`Error fetching category SKUs: ${error}`);
        } finally {
            setLoading(false);
        }
    };

    const clearCache = async () => {
        setLoading(true);
        addLog('Clearing cache...');
        try {
            await clearCachedSkus();
            addLog('Cache cleared');
            await checkCache();
        } catch (error) {
            addLog(`Error clearing cache: ${error}`);
        } finally {
            setLoading(false);
        }
    };

    const inspectIndexedDB = async () => {
        setLoading(true);
        addLog('Inspecting IndexedDB directly...');
        try {
            // Access IndexedDB directly to see what's stored
            const request = indexedDB.open('infinityAppDB');
            request.onsuccess = (event) => {
                const db = (event.target as any).result;
                const transaction = db.transaction(['sku_store'], 'readonly');
                const store = transaction.objectStore('sku_store');
                const getAllRequest = store.getAll();

                getAllRequest.onsuccess = () => {
                    const allData = getAllRequest.result;
                    addLog(`IndexedDB contains ${allData.length} entries`);
                    allData.forEach((item: any, index: number) => {
                        addLog(`Entry ${index}: Key=${item.key || 'unknown'}, Data keys=${Object.keys(item.data || {}).length}`);
                    });
                };
            };
            request.onerror = () => {
                addLog('Error accessing IndexedDB');
            };
        } catch (error) {
            addLog(`Error inspecting IndexedDB: ${error}`);
        } finally {
            setLoading(false);
        }
    };

    const handleTestVariantNames = async () => {
        try {
            console.log('🧪 Testing variant name fix...');

            // Clear cache to force fresh data load
            await clearCachedSkus();
            console.log('✅ Cache cleared');

            // Fetch SKU 769346 specifically to test
            const testSku = await getSkuById(769346, { forceRefresh: true });

            if (testSku) {
                console.log('🔍 SKU 769346 data:', {
                    skuId: testSku.skuId,
                    name: testSku.name,
                    variantName: testSku.variantName,
                    type: testSku.type
                });

                if (testSku.variantName) {
                    console.log('✅ variantName is preserved:', testSku.variantName);
                } else {
                    console.log('❌ variantName is missing');
                }
            } else {
                console.log('❌ SKU 769346 not found');
            }

        } catch (error) {
            console.error('❌ Test failed:', error);
        }
    };

    return (
        <AdminGuard requiredPermission="viewAllOrders">
            <div className="p-6 max-w-4xl mx-auto">
                <h1 className="text-2xl font-bold mb-6">SKU Cache Debug</h1>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Controls */}
                    <div className="space-y-4">
                        <h2 className="text-lg font-semibold">Actions</h2>
                        <div className="space-y-2">
                            <button
                                onClick={checkCache}
                                disabled={loading}
                                className="w-full px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
                            >
                                Check Cache
                            </button>
                            <button
                                onClick={fetchAllSkus}
                                disabled={loading}
                                className="w-full px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
                            >
                                Fetch All SKUs
                            </button>
                            <button
                                onClick={fetchCategorySkus}
                                disabled={loading}
                                className="w-full px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50"
                            >
                                Fetch Category 1 SKUs
                            </button>
                            <button
                                onClick={clearCache}
                                disabled={loading}
                                className="w-full px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 disabled:opacity-50"
                            >
                                Clear Cache
                            </button>
                            <button
                                onClick={inspectIndexedDB}
                                disabled={loading}
                                className="w-full px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700 disabled:opacity-50"
                            >
                                Inspect IndexedDB
                            </button>
                            <button
                                onClick={handleTestVariantNames}
                                className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
                            >
                                🧪 Test Variant Names Fix
                            </button>
                        </div>
                    </div>

                    {/* Cache Info */}
                    <div>
                        <h2 className="text-lg font-semibold mb-2">Cache Info</h2>
                        {cacheInfo ? (
                            <div className="bg-gray-100 p-4 rounded">
                                <p><strong>Total SKUs:</strong> {cacheInfo.totalSkus}</p>
                                <p><strong>Sample SKU IDs:</strong> {cacheInfo.skuIds.join(', ')}</p>
                                {cacheInfo.sampleSku && (
                                    <div className="mt-2">
                                        <p><strong>Sample SKU:</strong></p>
                                        <pre className="text-xs bg-white p-2 rounded mt-1 overflow-auto">
                                            {JSON.stringify(cacheInfo.sampleSku, null, 2)}
                                        </pre>
                                    </div>
                                )}
                            </div>
                        ) : (
                            <p className="text-gray-500">No cache info available. Click "Check Cache" to load.</p>
                        )}
                    </div>
                </div>

                {/* Logs */}
                <div className="mt-6">
                    <h2 className="text-lg font-semibold mb-2">Logs</h2>
                    <div className="bg-black text-green-400 p-4 rounded h-64 overflow-y-auto font-mono text-sm">
                        {logs.length === 0 ? (
                            <p>No logs yet...</p>
                        ) : (
                            logs.map((log, index) => (
                                <div key={index}>{log}</div>
                            ))
                        )}
                    </div>
                    <button
                        onClick={() => setLogs([])}
                        className="mt-2 px-3 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-700"
                    >
                        Clear Logs
                    </button>
                </div>

                {/* Instructions */}
                <div className="mt-6 bg-blue-50 p-4 rounded">
                    <h3 className="font-semibold text-blue-900">Instructions:</h3>
                    <ol className="list-decimal list-inside text-blue-800 mt-2 space-y-1">
                        <li>Open browser DevTools (F12)</li>
                        <li>Go to Application tab → Storage → IndexedDB</li>
                        <li>Look for "infinityAppDB" → "sku_store" → "all_skus"</li>
                        <li>Click "Fetch All SKUs" and check if data appears in IndexedDB</li>
                        <li>Check the logs and console for any errors</li>
                    </ol>
                </div>
            </div>
        </AdminGuard>
    );
}
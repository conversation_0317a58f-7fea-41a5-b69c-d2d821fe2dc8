"use client";

import React, { useEffect } from 'react';
import Link from 'next/link';
import { useQuery } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';
import { getOrderedCategories } from '../services/categoryService';
import { getCartsForAdmin } from '../services/cartService';
import { getOrdersForAdmin, formatDateForAPI } from '../services/orderService';
import { useAdminPermissions } from '../hooks/useAdminPermissions';
import { useAuth } from '../context/AuthContext';
import { logger } from '@/lib/logger';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import {
    RectangleGroupIcon,
    CubeIcon,
    ShoppingCartIcon,
    ClipboardDocumentListIcon,
    PlusIcon,
    EyeIcon,
    ClipboardDocumentIcon
} from '@heroicons/react/24/outline';
import { cn } from '@/lib/utils';

/**
 * Gets date range for recent orders: yesterday + today
 */
const getRecentOrdersDateRange = (): { dateFrom: string; dateTo: string } => {
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    return {
        dateFrom: formatDateForAPI(yesterday), // Yesterday in YYYY-MM-DD
        dateTo: formatDateForAPI(today)        // Today in YYYY-MM-DD
    };
};

/**
 * Gets date range for recent carts in epoch seconds
 * From yesterday 00:00:00 to today 23:59:59
 */
const getRecentCartsDateRange = (): { dateFrom: string; dateTo: string } => {
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    // Set yesterday to 00:00:00
    yesterday.setHours(0, 0, 0, 0);

    // Set today to 23:59:59
    const todayEnd = new Date(today);
    todayEnd.setHours(23, 59, 59, 999);

    return {
        dateFrom: formatDateForAPI(yesterday), // Yesterday in YYYY-MM-DD
        dateTo: formatDateForAPI(todayEnd)     // Today in YYYY-MM-DD
    };
};

const AdminDashboard: React.FC = () => {
    const { t } = useTranslation();
    const { isUserLoggedIn } = useAuth();
    const { forceLoadAdminRoles, checkPermission, adminRoles, adminRolesChecked } = useAdminPermissions();

    // Load admin roles when component mounts (fallback mechanism)
    useEffect(() => {
        const loadRoles = async () => {
            if (isUserLoggedIn && !adminRoles && !adminRolesChecked) {
                logger.info('[AdminDashboard] Loading admin roles as fallback...');
                try {
                    await forceLoadAdminRoles();
                } catch (error) {
                    logger.error('[AdminDashboard] Failed to load admin roles:', error);
                }
            }
        };

        loadRoles();
    }, [isUserLoggedIn, adminRoles, adminRolesChecked]);

    // Fetch categories for dashboard stats
    const {
        data: categories,
        isLoading: isLoadingCategories,
    } = useQuery({
        queryKey: ['categories'],
        queryFn: async () => {
            try {
                // Direct service call with includeInactive for admin
                return await getOrderedCategories({ includeInactive: true });
            } catch (error) {
                logger.error('Error loading admin categories:', error);
                return [];
            }
        },
    });

    // Fetch recent carts count (CREATED status, yesterday + today)
    const {
        data: cartsData,
        isLoading: isLoadingCarts,
    } = useQuery({
        queryKey: ['recent-carts-count'],
        queryFn: async () => {
            try {
                const dateRange = getRecentCartsDateRange();
                // Fetch minimal data with server-side filtering to get totalRows for count
                return await getCartsForAdmin(1, 1, {
                    status: 'CREATED',
                    dateFrom: dateRange.dateFrom,
                    dateTo: dateRange.dateTo
                });
            } catch (error) {
                logger.error('Error loading recent carts count:', error);
                return null;
            }
        },
    });

    // Fetch recent orders (yesterday + today)
    const {
        data: recentOrders,
        isLoading: isLoadingOrders,
    } = useQuery({
        queryKey: ['recent-orders'],
        queryFn: async () => {
            try {
                const dateRange = getRecentOrdersDateRange();
                return await getOrdersForAdmin(dateRange.dateFrom, dateRange.dateTo);
            } catch (error) {
                logger.error('Error loading recent orders:', error);
                return [];
            }
        },
    });

    const stats = [
        {
            name: t('admin.dashboard.stats.totalCategories'),
            value: categories?.length || 0,
            icon: RectangleGroupIcon,
            description: t('admin.dashboard.stats.categoriesDesc', 'Active product categories'),
            loading: isLoadingCategories,
        },
        {
            name: t('admin.dashboard.stats.totalSkus'),
            value: categories?.reduce((total, cat) => total + cat.skuIds.length, 0) || 0,
            icon: CubeIcon,
            description: t('admin.dashboard.stats.skusDesc', 'Total product SKUs'),
            loading: isLoadingCategories,
        },
        {
            name: t('admin.dashboard.stats.recentCarts'),
            value: cartsData?.pagination?.totalRows || '—',
            icon: ShoppingCartIcon,
            description: t('admin.dashboard.stats.cartsDesc', 'Created in last 24h'),
            loading: isLoadingCarts,
        },
        {
            name: t('admin.dashboard.stats.recentOrders'),
            value: recentOrders?.length || '—',
            icon: ClipboardDocumentListIcon,
            description: t('admin.dashboard.stats.ordersDesc', 'Placed in last 24h'),
            loading: isLoadingOrders,
        },
    ];

    const quickActions = [
        {
            name: t('admin.dashboard.quickActions.createCategory.name'),
            description: t('admin.dashboard.quickActions.createCategory.description'),
            href: '/admin/categories/create',
            icon: PlusIcon,
            permission: 'manageCategories' as const,
            available: true,
            variant: 'create' as const,
        },
        {
            name: t('admin.dashboard.quickActions.createSku.name'),
            description: t('admin.dashboard.quickActions.createSku.description'),
            href: '/admin/skus/create',
            icon: PlusIcon,
            permission: 'manageSku' as const,
            available: true,
            variant: 'create' as const,
        },
        {
            name: t('admin.dashboard.quickActions.viewCategories.name'),
            description: t('admin.dashboard.quickActions.viewCategories.description'),
            href: '/admin/categories',
            icon: RectangleGroupIcon,
            permission: 'manageCategories' as const,
            available: true,
            variant: 'view' as const,
        },
        {
            name: t('admin.dashboard.quickActions.viewSkus.name'),
            description: t('admin.dashboard.quickActions.viewSkus.description'),
            href: '/admin/skus',
            icon: CubeIcon,
            permission: 'manageSku' as const,
            available: true,
            variant: 'view' as const,
        },
        {
            name: t('admin.dashboard.quickActions.viewCarts.name'),
            description: t('admin.dashboard.quickActions.viewCarts.description'),
            href: '/admin/cart',
            icon: ShoppingCartIcon,
            permission: 'viewAllCarts' as const,
            available: true,
            variant: 'view' as const,
        },
    ];

    return (
        <div className="space-y-6">
            {/* Welcome Section - Modern */}
            <Card className="border-primary/20 bg-gradient-to-r from-primary/5 to-primary/10">
                <CardHeader>
                    <CardTitle className="text-xl">
                        {t('admin.dashboard.welcome.title')}
                    </CardTitle>
                    <CardDescription className="text-base">
                        {t('admin.dashboard.welcome.description')}
                    </CardDescription>
                </CardHeader>
            </Card>

            {/* Stats Grid - Modern Cards */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                {stats.map((stat) => {
                    const IconComponent = stat.icon;
                    return (
                        <Card key={stat.name} className="hover:shadow-md transition-shadow">
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">
                                    {stat.name}
                                </CardTitle>
                                <IconComponent className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">
                                    {stat.loading ? (
                                        <Skeleton className="h-8 w-16" />
                                    ) : (
                                        stat.value
                                    )}
                                </div>
                                <p className="text-xs text-muted-foreground mt-1">
                                    {stat.description}
                                </p>
                            </CardContent>
                        </Card>
                    );
                })}
            </div>

            {/* Quick Actions - Modern Cards */}
            <Card>
                <CardHeader>
                    <CardTitle className="text-lg">
                        {t('admin.dashboard.quickActions.title')}
                    </CardTitle>
                    <CardDescription>
                        {t('admin.dashboard.quickActions.subtitle', 'Common admin tasks and operations')}
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                        {quickActions.map((action) => {
                            const hasPermission = checkPermission(action.permission);
                            const isDisabled = !hasPermission || !action.available;
                            const IconComponent = action.icon;

                            return (
                                <div key={action.name}>
                                    {isDisabled ? (
                                        <Card className="opacity-50 cursor-not-allowed">
                                            <CardContent className="flex flex-col items-center justify-center p-6 min-h-[120px]">
                                                <IconComponent className="h-8 w-8 text-muted-foreground mb-3" />
                                                <div className="text-sm font-medium text-muted-foreground mb-1 text-center">
                                                    {action.name}
                                                </div>
                                                <div className="text-xs text-muted-foreground mb-2 text-center">
                                                    {action.description}
                                                </div>
                                                {!action.available && (
                                                    <Badge variant="secondary" className="text-xs">
                                                        {t('admin.dashboard.quickActions.comingSoon')}
                                                    </Badge>
                                                )}
                                            </CardContent>
                                        </Card>
                                    ) : (
                                        <Card className="hover:shadow-md transition-all duration-200 group cursor-pointer">
                                            <Link href={action.href}>
                                                <CardContent className="flex flex-col items-center justify-center p-6 min-h-[120px]">
                                                    <div className={cn(
                                                        "p-2 rounded-md mb-3 transition-colors",
                                                        action.variant === 'create'
                                                            ? "bg-primary/10 text-primary group-hover:bg-primary group-hover:text-primary-foreground"
                                                            : "bg-muted text-muted-foreground group-hover:bg-accent group-hover:text-accent-foreground"
                                                    )}>
                                                        <IconComponent className="h-6 w-6" />
                                                    </div>
                                                    <div className="text-sm font-medium mb-1 text-center group-hover:text-primary">
                                                        {action.name}
                                                    </div>
                                                    <div className="text-xs text-muted-foreground text-center">
                                                        {action.description}
                                                    </div>
                                                </CardContent>
                                            </Link>
                                        </Card>
                                    )}
                                </div>
                            );
                        })}
                    </div>
                </CardContent>
            </Card>

            {/* Recent Activity - Modern */}
            <Card>
                <CardHeader>
                    <CardTitle className="text-lg">
                        {t('admin.dashboard.recentActivity.title')}
                    </CardTitle>
                    <CardDescription>
                        {t('admin.dashboard.recentActivity.subtitle', 'Latest system activities and updates')}
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="text-center text-muted-foreground py-8">
                        <ClipboardDocumentIcon className="w-12 h-12 mx-auto mb-4 text-muted-foreground/50" />
                        <p className="text-sm">
                            {t('admin.dashboard.recentActivity.placeholder')}
                        </p>
                        <p className="text-xs mt-2">
                            {t('admin.dashboard.recentActivity.coming', 'Activity feed coming soon')}
                        </p>
                    </div>
                </CardContent>
            </Card>
        </div>
    );
};

export default AdminDashboard;
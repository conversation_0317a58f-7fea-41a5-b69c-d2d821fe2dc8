'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import {
    ChevronLeftIcon,
    PencilIcon,
    TagIcon,
    CurrencyRupeeIcon,
    ChevronRightIcon,
    HomeIcon,
    CheckCircleIcon,
    XCircleIcon,
    EyeIcon,
    InformationCircleIcon,
    PhotoIcon
} from '@heroicons/react/24/outline';
import { AdminGuard } from '@/app/components/common/AdminGuard';
import { getSkuById } from '@/app/services/skuService';
import { getOrderedCategories } from '@/app/services/categoryService';
import type { SKU } from '@/app/types/sku';
import type { Category } from '@/app/types/category';
import DynamicHeroIcon from '@/app/components/common/DynamicHeroIcon';
import { addNavigationParams } from '@/lib/utils';

export default function ViewSkuPage() {
    const params = useParams();
    const skuId = parseInt(params.id as string);

    const [sku, setSku] = useState<SKU | null>(null);
    const [parentSku, setParentSku] = useState<SKU | null>(null);
    const [skuCategories, setSkuCategories] = useState<Category[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        const loadData = async () => {
            try {
                setLoading(true);
                setError(null);

                // Fetch SKU and categories in parallel
                const [skuData, categoriesList] = await Promise.all([
                    getSkuById(skuId, { allowInactive: true }), // Admin can view inactive SKUs
                    getOrderedCategories({ includeInactive: true }) // Direct service call for admin
                ]);

                if (!skuData) {
                    setError('SKU not found');
                    return;
                }

                const relatedCategories = categoriesList.filter(cat => cat.skuIds.includes(skuId));

                setSku(skuData);
                setSkuCategories(relatedCategories);

                // If this is a child SKU with a parent, fetch the parent
                if (skuData.type === 'child' && skuData.parentId) {
                    const parentData = await getSkuById(skuData.parentId, { allowInactive: true });
                    setParentSku(parentData);
                }

            } catch (err) {
                console.error('Error loading SKU data:', err);
                setError('Failed to load SKU data');
            } finally {
                setLoading(false);
            }
        };

        loadData();
    }, [skuId]);

    const getSkuTypeDisplay = (sku: SKU) => {
        if (sku.type === 'parent') {
            return {
                label: 'Parent SKU',
                description: 'Container for product variants',
                badge: 'bg-yellow-100 text-yellow-800 border-yellow-200',
                icon: '📦'
            };
        }

        if (sku.parentId) {
            return {
                label: 'Child SKU',
                description: 'Product variant with specific attributes',
                badge: 'bg-blue-100 text-blue-800 border-blue-200',
                icon: '🏷️'
            };
        }

        return {
            label: 'Standalone SKU',
            description: 'Independent product without variants',
            badge: 'bg-green-100 text-green-800 border-green-200',
            icon: '🎯'
        };
    };

    if (loading) {
        return (
            <AdminGuard requiredPermission="manageSku">
                <div className="flex items-center justify-center min-h-screen">
                    <div className="text-center">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                        <p className="mt-4 text-gray-600">Loading SKU information...</p>
                    </div>
                </div>
            </AdminGuard>
        );
    }

    if (error || !sku) {
        return (
            <AdminGuard requiredPermission="manageSku">
                <div className="flex flex-col items-center justify-center min-h-screen">
                    <div className="text-center">
                        <XCircleIcon className="mx-auto h-12 w-12 text-red-500" />
                        <h3 className="mt-4 text-lg font-medium text-gray-900">SKU Not Found</h3>
                        <p className="mt-2 text-sm text-gray-500">{error || 'The requested SKU could not be found.'}</p>
                        <div className="mt-6">
                            <Link
                                href="/admin/skus"
                                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                            >
                                <ChevronLeftIcon className="h-4 w-4 mr-2" />
                                Back to SKUs
                            </Link>
                        </div>
                    </div>
                </div>
            </AdminGuard>
        );
    }

    const typeDisplay = getSkuTypeDisplay(sku);

    return (
        <AdminGuard requiredPermission="manageSku">
            <div className="space-y-6">
                {/* Breadcrumb Navigation */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                    <nav className="flex" aria-label="Breadcrumb">
                        <ol className="flex items-center space-x-4">
                            <li>
                                <Link href="/admin" className="text-gray-400 hover:text-gray-500">
                                    <HomeIcon className="flex-shrink-0 h-5 w-5" />
                                    <span className="sr-only">Admin</span>
                                </Link>
                            </li>
                            <li>
                                <div className="flex items-center">
                                    <ChevronRightIcon className="flex-shrink-0 h-5 w-5 text-gray-400" />
                                    <Link href="/admin/skus" className="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700">
                                        SKUs
                                    </Link>
                                </div>
                            </li>
                            {/* Parent SKU Breadcrumb for Child SKUs */}
                            {sku.type === 'child' && parentSku && (
                                <li>
                                    <div className="flex items-center">
                                        <ChevronRightIcon className="flex-shrink-0 h-5 w-5 text-gray-400" />
                                        <Link
                                            href={`/admin/skus/${parentSku.skuId}`}
                                            className="ml-4 text-sm font-medium text-blue-600 hover:text-blue-800"
                                        >
                                            #{parentSku.skuId} {parentSku.name.en}
                                        </Link>
                                    </div>
                                </li>
                            )}
                            <li>
                                <div className="flex items-center">
                                    <ChevronRightIcon className="flex-shrink-0 h-5 w-5 text-gray-400" />
                                    <span className="ml-4 text-sm font-medium text-gray-900">
                                        #{sku.skuId}
                                    </span>
                                </div>
                            </li>
                        </ol>
                    </nav>
                </div>

                {/* Header with SKU Type Badge */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div className="flex items-start justify-between">
                        <div className="flex-1">
                            <div className="flex items-center space-x-3 mb-4">
                                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${typeDisplay.badge}`}>
                                    <span className="mr-2">{typeDisplay.icon}</span>
                                    {typeDisplay.label}
                                </span>
                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${sku.isActive === 1
                                    ? 'bg-green-100 text-green-800'
                                    : 'bg-red-100 text-red-800'
                                    }`}>
                                    {sku.isActive === 1 ? (
                                        <>
                                            <CheckCircleIcon className="h-3 w-3 mr-1" />
                                            Active
                                        </>
                                    ) : (
                                        <>
                                            <XCircleIcon className="h-3 w-3 mr-1" />
                                            Inactive
                                        </>
                                    )}
                                </span>
                            </div>

                            <div className="mb-2">
                                <h1 className="text-3xl font-bold text-gray-900">
                                    SKU #{sku.skuId}
                                </h1>
                                <p className="mt-1 text-lg text-gray-600">{sku.name.en}</p>
                                {sku.name.ta && (
                                    <p className="text-md text-gray-500">{sku.name.ta}</p>
                                )}
                            </div>

                            {/* Variant Name for Child SKUs */}
                            {sku.type === 'child' && sku.variantName && (
                                <div className="mb-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                                    <div className="flex items-center">
                                        <TagIcon className="h-5 w-5 text-blue-600 mr-2" />
                                        <div>
                                            <p className="text-sm font-medium text-blue-900">Variant Name</p>
                                            <p className="text-blue-800">{sku.variantName.en}</p>
                                            {sku.variantName.ta && sku.variantName.ta !== sku.variantName.en && (
                                                <p className="text-sm text-blue-600">({sku.variantName.ta})</p>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            )}

                            {/* Parent Reference for Child SKUs */}
                            {sku.type === 'child' && parentSku && (
                                <div className="mb-4 p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center">
                                            <InformationCircleIcon className="h-5 w-5 text-yellow-600 mr-2" />
                                            <div>
                                                <p className="text-sm font-medium text-yellow-900">Parent Product</p>
                                                <p className="text-yellow-800">#{parentSku.skuId} {parentSku.name.en}</p>
                                            </div>
                                        </div>
                                        <Link
                                            href={`/admin/skus/${parentSku.skuId}`}
                                            className="inline-flex items-center px-3 py-1 border border-yellow-300 text-sm font-medium rounded-md text-yellow-700 bg-yellow-100 hover:bg-yellow-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500"
                                        >
                                            <EyeIcon className="h-4 w-4 mr-1" />
                                            View Parent
                                        </Link>
                                    </div>
                                </div>
                            )}

                            <p className="text-gray-600">{typeDisplay.description}</p>
                        </div>

                        <div className="flex items-center space-x-3 ml-6">
                            <Link
                                href={addNavigationParams(`/admin/skus/${sku.skuId}/edit`, { from: 'detail' })}
                                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center space-x-2 transition-colors"
                            >
                                <PencilIcon className="h-4 w-4" />
                                <span>Edit SKU</span>
                            </Link>
                        </div>
                    </div>
                </div>

                {/* Main Content Grid */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* Left Column - Product Information */}
                    <div className="lg:col-span-2 space-y-6">
                        {/* Product Details */}
                        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                            <h2 className="text-lg font-medium text-gray-900 mb-6">Product Information</h2>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                {/* Description */}
                                <div className="md:col-span-2">
                                    <h3 className="text-sm font-medium text-gray-700 mb-3">Description</h3>
                                    <div className="space-y-2">
                                        <p className="text-gray-900">{sku.description.en || 'No description available'}</p>
                                        {sku.description.ta && (
                                            <p className="text-gray-600 text-sm">{sku.description.ta}</p>
                                        )}
                                    </div>
                                </div>

                                {/* Pricing Information for Child SKUs */}
                                {sku.type === 'child' && (
                                    <>
                                        <div className="space-y-4">
                                            <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                                                <div className="flex items-center">
                                                    <CurrencyRupeeIcon className="h-5 w-5 text-green-600 mr-2" />
                                                    <div>
                                                        <p className="text-sm font-medium text-green-700">Selling Price</p>
                                                        <p className="text-xl font-bold text-green-900">₹{sku.sellingPrice}</p>
                                                    </div>
                                                </div>
                                            </div>

                                            <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                                                <div className="flex items-center">
                                                    <CurrencyRupeeIcon className="h-5 w-5 text-blue-600 mr-2" />
                                                    <div>
                                                        <p className="text-sm font-medium text-blue-700">MRP</p>
                                                        <p className="text-xl font-bold text-blue-900">₹{sku.mrp}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div className="space-y-4">
                                            <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                                                <div className="flex items-center">
                                                    <CurrencyRupeeIcon className="h-5 w-5 text-gray-600 mr-2" />
                                                    <div>
                                                        <p className="text-sm font-medium text-gray-700">Cost Price</p>
                                                        <p className="text-xl font-bold text-gray-900">₹{sku.costPrice}</p>
                                                    </div>
                                                </div>
                                            </div>

                                            {/* Profit Analysis */}
                                            <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                                                <h4 className="text-sm font-medium text-yellow-800 mb-2">Profit Analysis</h4>
                                                <div className="space-y-1">
                                                    <div className="flex justify-between text-sm">
                                                        <span className="text-yellow-700">Profit Margin:</span>
                                                        <span className="font-medium text-yellow-900">
                                                            ₹{sku.sellingPrice! - sku.costPrice!}
                                                            ({(((sku.sellingPrice! - sku.costPrice!) / sku.costPrice!) * 100).toFixed(1)}%)
                                                        </span>
                                                    </div>
                                                    <div className="flex justify-between text-sm">
                                                        <span className="text-yellow-700">Discount from MRP:</span>
                                                        <span className="font-medium text-yellow-900">
                                                            {(((sku.mrp! - sku.sellingPrice!) / sku.mrp!) * 100).toFixed(1)}%
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </>
                                )}

                                {/* Parent SKU Notice */}
                                {sku.type === 'parent' && (
                                    <div className="md:col-span-2 bg-blue-50 p-4 rounded-lg border border-blue-200">
                                        <div className="flex items-center">
                                            <InformationCircleIcon className="h-5 w-5 text-blue-600 mr-2" />
                                            <p className="text-blue-800">
                                                This is a parent SKU. Pricing information is available for individual variants below.
                                            </p>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* Categories */}
                        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                            <h2 className="text-lg font-medium text-gray-900 mb-4">Categories</h2>
                            {skuCategories.length === 0 ? (
                                <div className="text-center py-8 bg-gray-50 rounded-lg">
                                    <TagIcon className="mx-auto h-8 w-8 text-gray-400" />
                                    <p className="mt-2 text-sm text-gray-500">This SKU is not assigned to any categories.</p>
                                </div>
                            ) : (
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    {skuCategories.map((category) => (
                                        <div key={category.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                                            <div className="flex items-center space-x-3">
                                                {category.icon && (
                                                    <div className="flex-shrink-0">
                                                        <div className="w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-medium"
                                                            style={{ backgroundColor: category.background || '#6B7280' }}>
                                                            <DynamicHeroIcon iconName={category.icon} />
                                                        </div>
                                                    </div>
                                                )}
                                                <div className="flex-1">
                                                    <h3 className="text-sm font-medium text-gray-900">{category.name.en}</h3>
                                                    {category.name.ta && (
                                                        <p className="text-sm text-gray-500">{category.name.ta}</p>
                                                    )}
                                                    <p className="text-xs text-gray-400 mt-1">
                                                        {category.skuIds.length} SKUs in this category
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Right Column - Image Gallery */}
                    <div className="space-y-6">
                        {/* Primary Image */}
                        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                            <h2 className="text-lg font-medium text-gray-900 mb-4">Product Image</h2>
                            <div className="aspect-square w-full">
                                {sku.imageUrl ? (
                                    <Image
                                        src={sku.imageUrl}
                                        alt={sku.name.en}
                                        width={400}
                                        height={400}
                                        className="w-full h-full object-cover rounded-lg border border-gray-200"
                                    />
                                ) : (
                                    <div className="w-full h-full bg-gray-100 rounded-lg border border-gray-200 flex items-center justify-center">
                                        <div className="text-center">
                                            <PhotoIcon className="mx-auto h-12 w-12 text-gray-400" />
                                            <p className="mt-2 text-sm text-gray-500">No image available</p>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* Additional Images */}
                        {sku.images && sku.images.length > 0 && (
                            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                                <h2 className="text-lg font-medium text-gray-900 mb-4">Additional Images</h2>
                                <div className="grid grid-cols-2 gap-4">
                                    {sku.images.map((imageUrl, index) => (
                                        <div key={index} className="aspect-square">
                                            <Image
                                                src={imageUrl}
                                                alt={`${sku.name.en} - Image ${index + 1}`}
                                                width={150}
                                                height={150}
                                                className="w-full h-full object-cover rounded-lg border border-gray-200"
                                            />
                                        </div>
                                    ))}
                                </div>
                            </div>
                        )}
                    </div>
                </div>

                {/* Enhanced Variants Table for Parent SKUs */}
                {sku.type === 'parent' && (
                    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <div className="flex items-center justify-between mb-6">
                            <div>
                                <h2 className="text-lg font-medium text-gray-900">Product Variants</h2>
                                <p className="text-sm text-gray-500">
                                    {sku.variants?.length} variant{sku.variants?.length !== 1 ? 's' : ''} available
                                </p>
                            </div>
                        </div>

                        {sku.variants?.length === 0 ? (
                            <div className="text-center py-12 bg-gray-50 rounded-lg">
                                <div className="mx-auto h-12 w-12 text-gray-400">
                                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                                    </svg>
                                </div>
                                <h3 className="mt-4 text-sm font-medium text-gray-900">No variants available</h3>
                                <p className="mt-2 text-sm text-gray-500">
                                    This parent SKU doesn&apos;t have any variants yet. Add variants by editing this SKU.
                                </p>
                                <div className="mt-6">
                                    <Link
                                        href={addNavigationParams(`/admin/skus/${sku.skuId}/edit`, { from: 'detail' })}
                                        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                    >
                                        <PencilIcon className="h-4 w-4 mr-2" />
                                        Add Variants
                                    </Link>
                                </div>
                            </div>
                        ) : (
                            <div className="overflow-x-auto">
                                <table className="min-w-full divide-y divide-gray-200">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                SKU ID
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Variant Name
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Status
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Pricing
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Profit
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Actions
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                        {sku.variants?.map((variant, index) => (
                                            <tr key={variant.skuId || index} className="hover:bg-gray-50">
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="flex items-center">
                                                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                            #{variant.skuId}
                                                        </span>
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div>
                                                        <div className="text-sm font-medium text-gray-900">
                                                            {variant.variantName?.en || variant.name.en}
                                                        </div>
                                                        {variant.variantName?.ta && variant.variantName.ta !== variant.variantName.en && (
                                                            <div className="text-sm text-gray-500">{variant.variantName.ta}</div>
                                                        )}
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${variant.isActive === 1
                                                        ? 'bg-green-100 text-green-800'
                                                        : 'bg-red-100 text-red-800'
                                                        }`}>
                                                        {variant.isActive === 1 ? (
                                                            <>
                                                                <CheckCircleIcon className="h-3 w-3 mr-1" />
                                                                Active
                                                            </>
                                                        ) : (
                                                            <>
                                                                <XCircleIcon className="h-3 w-3 mr-1" />
                                                                Inactive
                                                            </>
                                                        )}
                                                    </span>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="space-y-1">
                                                        <div className="text-sm font-medium text-gray-900">
                                                            SP: ₹{variant.sellingPrice}
                                                        </div>
                                                        <div className="text-xs text-gray-500">
                                                            MRP: ₹{variant.mrp} • CP: ₹{variant.costPrice}
                                                        </div>
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="space-y-1">
                                                        <div className="text-sm font-medium text-green-600">
                                                            ₹{variant.sellingPrice - variant.costPrice}
                                                        </div>
                                                        <div className="text-xs text-gray-500">
                                                            {(((variant.sellingPrice - variant.costPrice) / variant.costPrice) * 100).toFixed(1)}%
                                                        </div>
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                    <div className="flex items-center space-x-2">
                                                        <Link
                                                            href={`/admin/skus/${variant.skuId}`}
                                                            className="text-blue-600 hover:text-blue-900 p-1 rounded"
                                                            title="View variant details"
                                                        >
                                                            <EyeIcon className="h-4 w-4" />
                                                        </Link>
                                                        <Link
                                                            href={addNavigationParams(`/admin/skus/${variant.skuId}/edit`, {
                                                                from: 'detail',
                                                                parentId: sku.skuId
                                                            })}
                                                            className="text-green-600 hover:text-green-900 p-1 rounded"
                                                            title="Edit variant"
                                                        >
                                                            <PencilIcon className="h-4 w-4" />
                                                        </Link>
                                                    </div>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        )}
                    </div>
                )}
            </div>
        </AdminGuard>
    );
}
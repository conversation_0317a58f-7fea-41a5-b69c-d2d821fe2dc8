'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import { ChevronLeftIcon } from '@heroicons/react/24/outline';
import { AdminGuard } from '@/app/components/common/AdminGuard';
import SkuForm from '@/app/components/SkuForm';
import { getSkuById } from '@/app/services/skuService';
import type { SKU } from '@/app/types/sku';
import { toast } from 'react-toastify';
import { getNavigationContext, buildNavigationUrl } from '@/lib/utils';

export default function EditSkuPage() {
    const router = useRouter();
    const params = useParams();
    const searchParams = useSearchParams();
    const skuId = Number(params.id);

    const [sku, setSku] = useState<SKU | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    // Get navigation context
    const navigationContext = getNavigationContext(searchParams);

    // Get back URL with fallback validation
    const getBackUrl = () => {
        // If context points to a specific parent, verify it exists when SKU is loaded
        if (navigationContext.from === 'detail' && navigationContext.parentId && sku) {
            // For child SKUs being edited from variant table, ensure parent ID matches
            if (sku.parentId && sku.parentId !== navigationContext.parentId) {
                return '/admin/skus'; // Fallback if parent mismatch
            }
            return `/admin/skus/${navigationContext.parentId}`;
        }
        return buildNavigationUrl(navigationContext);
    };

    // Load SKU data
    useEffect(() => {
        const loadSku = async () => {
            try {
                setLoading(true);
                setError(null);

                if (!skuId || isNaN(skuId)) {
                    setError('Invalid SKU ID');
                    return;
                }

                const skuData = await getSkuById(skuId, { allowInactive: true }); // Admin can edit inactive SKUs
                if (!skuData) {
                    setError('SKU not found');
                    return;
                }

                setSku(skuData);
            } catch (error) {
                console.error('Error loading SKU:', error);
                setError('Failed to load SKU');
            } finally {
                setLoading(false);
            }
        };

        loadSku();
    }, [skuId]);

    const handleSave = async (updatedSku: SKU) => {
        // This function is called after SkuOperationsService.saveSingleSku() has already completed successfully
        toast.success('SKU updated successfully', {
            position: 'top-right',
            autoClose: 3000,
            hideProgressBar: false,
            closeOnClick: true,
            pauseOnHover: true,
            draggable: true,
        });

        // Smart navigation based on context
        router.push(getBackUrl());
    };

    const handleCancel = () => {
        router.push(getBackUrl());
    };

    if (loading) {
        return (
            <AdminGuard requiredPermission="manageSku">
                <div className="flex items-center justify-center min-h-screen">
                    <div className="text-center">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                        <p className="mt-2 text-gray-600">Loading SKU...</p>
                    </div>
                </div>
            </AdminGuard>
        );
    }

    if (error || !sku) {
        return (
            <AdminGuard requiredPermission="manageSku">
                <div className="space-y-6">
                    {/* Header */}
                    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <div className="space-y-4">
                            <Link
                                href={getBackUrl()}
                                className="inline-flex items-center text-sm text-gray-600 hover:text-gray-900 transition-colors"
                            >
                                <ChevronLeftIcon className="h-4 w-4 mr-1" />
                                {navigationContext.from === 'detail' ? 'Back to SKU Details' : 'Back to SKUs'}
                            </Link>
                            <div>
                                <h1 className="text-3xl font-bold text-gray-900">Error</h1>
                                <p className="mt-1 text-sm text-red-600">
                                    {error || 'SKU not found'}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </AdminGuard>
        );
    }

    return (
        <AdminGuard requiredPermission="manageSku">
            <div className="space-y-6">
                {/* Header */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div className="space-y-4">
                        <Link
                            href={getBackUrl()}
                            className="inline-flex items-center text-sm text-gray-600 hover:text-gray-900 transition-colors"
                        >
                            <ChevronLeftIcon className="h-4 w-4 mr-1" />
                            {navigationContext.from === 'detail' ? 'Back to SKU Details' : 'Back to SKUs'}
                        </Link>
                        <div>
                            <h1 className="text-3xl font-bold text-gray-900">
                                Edit SKU #{sku.skuId}
                            </h1>
                            <p className="mt-1 text-sm text-gray-600">
                                Update product information, variants, and category assignments.
                            </p>
                        </div>
                    </div>
                </div>

                {/* SKU Form */}
                <SkuForm
                    mode="edit"
                    initialData={sku}
                    onSave={handleSave}
                    onCancel={handleCancel}
                />
            </div>
        </AdminGuard>
    );
}
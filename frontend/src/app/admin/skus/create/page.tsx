'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { ChevronLeftIcon } from '@heroicons/react/24/outline';
import { AdminGuard } from '@/app/components/common/AdminGuard';
import SkuForm from '@/app/components/SkuForm';
import type { SKU } from '@/app/types/sku';
import { toast } from 'react-toastify';
import { getNavigationContext, buildNavigationUrl } from '@/lib/utils';

export default function CreateSkuPage() {
    const router = useRouter();
    const searchParams = useSearchParams();

    // Get navigation context
    const navigationContext = getNavigationContext(searchParams);
    const backUrl = buildNavigationUrl(navigationContext);

    const handleSave = async (sku: SKU) => {
        // This function is called after SkuOperationsService.saveSingleSku() has already completed successfully
        toast.success('SKU created successfully', {
            position: 'top-right',
            autoClose: 3000,
            hideProgressBar: false,
            closeOnClick: true,
            pauseOnHover: true,
            draggable: true,
        });

        // Smart navigation based on context
        router.push(backUrl);
    };

    const handleCancel = () => {
        router.push(backUrl);
    };

    return (
        <AdminGuard requiredPermission="manageSku">
            <div className="space-y-6">
                {/* Header */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div className="space-y-4">
                        <Link
                            href={backUrl}
                            className="inline-flex items-center text-sm text-gray-600 hover:text-gray-900 transition-colors"
                        >
                            <ChevronLeftIcon className="h-4 w-4 mr-1" />
                            {navigationContext.from === 'detail' ? 'Back to SKU Details' : 'Back to SKUs'}
                        </Link>
                        <div>
                            <h1 className="text-3xl font-bold text-gray-900">Create New SKU</h1>
                            <p className="mt-1 text-sm text-gray-600">
                                Add a new product SKU to your inventory with variants and category assignments.
                            </p>
                        </div>
                    </div>
                </div>

                {/* SKU Form */}
                <SkuForm
                    mode="create"
                    onSave={handleSave}
                    onCancel={handleCancel}
                />
            </div>
        </AdminGuard>
    );
}
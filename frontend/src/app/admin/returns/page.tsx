"use client";

import React, { useState, useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';
import { AdminGuard } from '../../components/common/AdminGuard';
import StatusBadge from '../components/StatusBadge';
import Image from 'next/image';
import {
    getOrdersForAdmin,
    calculateOrderTotal,
    formatDeliveryDate,
    updateOrder
} from '../../services/orderService';
import { Order } from '../../types/order';
import { getSkus } from '../../services/skuService';
import { SKU } from '../../types/sku';
import { maskMobile } from '../../../lib/utils';
import { useReturnsFilters } from '../../hooks/useReturnsFilters';
import { useTranslation } from 'react-i18next';
import { logger } from '@/lib/logger';
import DatePicker from '../../components/common/DatePicker';
import {
    ChevronDownIcon,
    ChevronRightIcon,
    ArrowPathIcon,
    PlusIcon,
    MinusIcon,
    CheckIcon
} from '@heroicons/react/24/outline';
import { toast } from 'react-toastify';

interface ReturnSkuData {
    skuId: number;
    expectedQuantity: number;
    quantity: number;
    unit: string;
    inventoryLoss: number;
}

interface ExpandedOrders {
    [orderId: number]: boolean;
}

interface ReturnData {
    [orderId: number]: {
        [skuId: number]: ReturnSkuData;
    };
}

const ReturnsPage: React.FC = () => {
    const { t } = useTranslation();
    // Use the returns filters hook for URL query parameter synchronization
    const { dateFrom, dateTo, updateDateFilter } = useReturnsFilters();

    const [expandedOrders, setExpandedOrders] = useState<ExpandedOrders>({});
    const [returnData, setReturnData] = useState<ReturnData>({});
    const [submittingReturns, setSubmittingReturns] = useState<Set<number>>(new Set());

    // Fetch orders
    const {
        data: orders = [],
        isLoading: ordersLoading,
        isError: ordersError,
        error: ordersErrorMessage,
        refetch: refetchOrders,
        isFetching
    } = useQuery<Order[], Error>({
        queryKey: ['returns-orders', dateFrom, dateTo],
        queryFn: () => getOrdersForAdmin(dateFrom, dateTo),
        staleTime: 30000,
        gcTime: 300000,
        refetchOnMount: true,
        refetchOnWindowFocus: false,
    });

    // Fetch SKU details
    const {
        data: skuDetailsArray = [],
        isLoading: skuLoading
    } = useQuery<SKU[], Error>({
        queryKey: ['all-skus'],
        queryFn: () => getSkus({ allowInactive: true }), // Admin can see inactive SKUs
        staleTime: 300000, // 5 minutes
        gcTime: 600000, // 10 minutes
    });

    // Convert SKU[] to Record<number, SKU> for easier access
    const skuDetails = useMemo(() => {
        const details: Record<number, SKU> = {};
        skuDetailsArray.forEach(sku => {
            details[sku.skuId] = sku;
        });
        return details;
    }, [skuDetailsArray]);

    // Filter to only show PARTIALLY_DELIVERED orders or UNDELIVERED orders
    const partiallyDeliveredOrders = useMemo(() => {
        return orders.filter(order => {
            if (!(order.status == 'PARTIALLY_DELIVERED' || order.status == 'UNDELIVERED')) {
                return false;
            }

            if (order.metadata.returnData) {
                return false;
            }

            if (order.status == 'UNDELIVERED') {
                return true;
            }

            const partialSkus = order.skuItems.filter(item => item.pickedQuantity && item.pickedQuantity > 0 && item.deliveredQuantity !== undefined && item.deliveredQuantity !== null && item.pickedQuantity > item.deliveredQuantity);
            if (partialSkus.length == 0) {
                return false;
            }
            return true;
        });
    }, [orders]);

    // Initialize return data for orders
    const initializeReturnData = (order: Order) => {
        if (returnData[order.id]) return;

        const orderReturnData: { [skuId: number]: ReturnSkuData } = {};

        order.skuItems.forEach(item => {
            const deliveredQuantity = item.deliveredQuantity || 0;
            const pickedQuantity = item.pickedQuantity || 0;
            // Use pickedQuantity if available, otherwise use ordered quantity
            if (pickedQuantity > deliveredQuantity) {

                const expectedQuantity = pickedQuantity - deliveredQuantity;

                orderReturnData[item.skuID] = {
                    skuId: item.skuID,
                    expectedQuantity: expectedQuantity,
                    quantity: expectedQuantity, // Start with expected quantity as default
                    unit: item.unit,
                    inventoryLoss: 0 // Initially no loss since all is returned
                };
            }
        });

        setReturnData(prev => ({
            ...prev,
            [order.id]: orderReturnData
        }));
    };

    // Handle refresh button click
    const handleRefresh = () => {
        logger.debug('Returns: Manual refresh triggered', { dateFrom, dateTo });
        refetchOrders();
    };

    // Toggle order expansion
    const toggleOrderExpansion = (orderId: number) => {
        setExpandedOrders(prev => ({
            ...prev,
            [orderId]: !prev[orderId]
        }));

        // Initialize return data when expanding
        const order = partiallyDeliveredOrders.find(o => o.id === orderId);
        if (order) {
            initializeReturnData(order);
        }
    };

    // Update return quantity for a SKU
    const updateReturnQuantity = (orderId: number, skuId: number, newQuantity: number) => {
        setReturnData(prev => {
            const orderData = prev[orderId] || {};
            const skuData = orderData[skuId];

            if (!skuData) return prev;

            // Ensure quantity doesn't exceed expected quantity
            const validQuantity = Math.max(0, Math.min(newQuantity, skuData.expectedQuantity));

            return {
                ...prev,
                [orderId]: {
                    ...orderData,
                    [skuId]: {
                        ...skuData,
                        quantity: validQuantity,
                        inventoryLoss: skuData.expectedQuantity - validQuantity
                    }
                }
            };
        });
    };

    // Submit returns for an order
    const submitReturns = async (order: Order) => {
        const orderReturns = returnData[order.id];
        if (!orderReturns) {
            toast.error(t('admin.returns.messages.noReturnData'));
            return;
        }

        // Convert return data to the required format
        const returnsSkus = Object.values(orderReturns);

        setSubmittingReturns(prev => new Set([...prev, order.id]));

        try {
            await updateOrder({
                ...order,
                metadata: {
                    ...order.metadata,
                    returnData: {
                        skus: returnsSkus.filter(sku => sku.inventoryLoss > 0)
                    }
                }
            });

            toast.success(t('admin.returns.messages.submitSuccess', { id: order.id }));

            // Refresh orders list
            refetchOrders();

            // Clear local return data for this order
            setReturnData(prev => {
                const updated = { ...prev };
                delete updated[order.id];
                return updated;
            });

        } catch (error) {
            logger.error('Returns: Failed to submit returns', {
                error: error instanceof Error ? error.message : String(error),
                orderId: order.id
            });
            toast.error(t('admin.returns.messages.submitError'));
        } finally {
            setSubmittingReturns(prev => {
                const updated = new Set(prev);
                updated.delete(order.id);
                return updated;
            });
        }
    };

    // Render mobile card view
    const renderMobileCard = (order: Order) => {
        const isExpanded = expandedOrders[order.id];
        const isSubmitting = submittingReturns.has(order.id);
        const orderReturns = returnData[order.id];

        return (
            <div key={order.id} className="bg-white rounded-lg shadow-sm border border-gray-200 mb-4">
                {/* Card Header - Always Visible */}
                <div
                    className="p-4 cursor-pointer hover:bg-gray-50 transition-colors"
                    onClick={() => toggleOrderExpansion(order.id)}
                >
                    <div className="flex items-center justify-between">
                        <div className="flex-1">
                            <div className="flex items-center justify-between mb-2">
                                <span className="text-lg font-semibold text-gray-900">#{order.id}</span>
                                <StatusBadge status={order.status.toLowerCase()} />
                            </div>
                            <div className="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                    <span className="text-gray-500">{t('admin.returns.table.mobile')}:</span>
                                    <div className="font-medium">{order.mobile}</div>
                                </div>
                                <div>
                                    <span className="text-gray-500">{t('admin.returns.table.items')}:</span>
                                    <div className="font-medium">{order.skuItems.length}</div>
                                </div>
                                <div>
                                    <span className="text-gray-500">{t('admin.returns.table.total')}:</span>
                                    <div className="font-medium">₹{calculateOrderTotal(order).toFixed(2)}</div>
                                </div>
                                <div>
                                    <span className="text-gray-500">{t('admin.returns.table.facility')}:</span>
                                    <div className="font-medium">{order.facilityKey || 'N/A'}</div>
                                </div>
                            </div>
                        </div>
                        <div className="ml-4">
                            {isExpanded ? (
                                <ChevronDownIcon className="h-5 w-5 text-gray-400" />
                            ) : (
                                <ChevronRightIcon className="h-5 w-5 text-gray-400" />
                            )}
                        </div>
                    </div>
                </div>

                {/* Expanded Content - SKU Details */}
                {isExpanded && (
                    <div className="border-t border-gray-200">
                        {/* Sticky Header for Mobile */}
                        <div className="sticky top-0 bg-gray-50 px-4 py-2 border-b border-gray-200 z-10">
                            <h4 className="font-medium text-gray-900">{t('admin.returns.items.title')}</h4>
                        </div>

                        <div className="p-4 space-y-4">
                            {order.skuItems.map((item, index) => {
                                const skuDetail = skuDetails[item.skuID];
                                const returnItem = orderReturns?.[item.skuID];
                                const expectedQty = returnItem?.expectedQuantity || 0;
                                const returnedQty = returnItem?.quantity || 0;

                                if (expectedQty == 0) {
                                    return (<></>);
                                }


                                return (
                                    <div key={index} className="bg-gray-50 rounded-lg p-4">
                                        {/* SKU Header */}
                                        <div className="flex items-center space-x-3 mb-3">
                                            <div className="h-12 w-12 flex-shrink-0">
                                                <Image
                                                    src={skuDetail?.imageUrl || '/placeholder-product.png'}
                                                    alt={skuDetail?.name?.en || `SKU ${item.skuID}`}
                                                    width={48}
                                                    height={48}
                                                    className="h-12 w-12 rounded-lg object-cover"
                                                />
                                            </div>
                                            <div className="flex-1">
                                                <div className="font-medium text-gray-900">
                                                    #{item.skuID}
                                                </div>
                                                {skuDetail && (
                                                    <div className="text-sm text-gray-600">
                                                        {skuDetail.name.en}
                                                    </div>
                                                )}
                                            </div>
                                        </div>

                                        {/* Quantity Controls */}
                                        <div className="grid grid-cols-2 gap-4 text-sm">
                                            <div>
                                                <span className="text-gray-500">{t('admin.returns.items.expectedLabel')}</span>
                                                <div className="font-medium">{expectedQty} {item.unit}</div>
                                            </div>
                                            <div>
                                                <span className="text-gray-500">{t('admin.returns.items.returnedLabel')}</span>
                                                <div className="flex items-center space-x-2 mt-1">
                                                    <button
                                                        onClick={() => updateReturnQuantity(order.id, item.skuID, returnedQty - 1)}
                                                        disabled={returnedQty <= 0 || isSubmitting}
                                                        className="h-8 w-8 rounded-full bg-red-100 text-red-600 hover:bg-red-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center touch-manipulation active:scale-95"
                                                    >
                                                        <MinusIcon className="h-4 w-4" />
                                                    </button>
                                                    <span className="w-12 text-center font-medium">
                                                        {returnedQty}
                                                    </span>
                                                    <button
                                                        onClick={() => updateReturnQuantity(order.id, item.skuID, returnedQty + 1)}
                                                        disabled={returnedQty >= expectedQty || isSubmitting}
                                                        className="h-8 w-8 rounded-full bg-green-100 text-green-600 hover:bg-green-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center touch-manipulation active:scale-95"
                                                    >
                                                        <PlusIcon className="h-4 w-4" />
                                                    </button>
                                                </div>
                                            </div>
                                        </div>

                                        {returnItem && returnItem.inventoryLoss > 0 && (
                                            <div className="mt-2 text-xs text-orange-600">
                                                {t('admin.returns.items.inventoryLoss', { amount: returnItem.inventoryLoss, unit: item.unit })}
                                            </div>
                                        )}
                                    </div>
                                );
                            })}

                            {/* Submit Button */}
                            <div className="pt-4 border-t border-gray-200">
                                <button
                                    onClick={() => submitReturns(order)}
                                    disabled={isSubmitting}
                                    className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2 font-medium touch-manipulation active:scale-95 min-h-[44px]"
                                >
                                    {isSubmitting ? (
                                        <>
                                            <ArrowPathIcon className="h-5 w-5 animate-spin" />
                                            <span>{t('admin.returns.actions.submitting')}</span>
                                        </>
                                    ) : (
                                        <>
                                            <CheckIcon className="h-5 w-5" />
                                            <span>{t('admin.returns.actions.submitReturns')}</span>
                                        </>
                                    )}
                                </button>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        );
    };

    // Render desktop table row
    const renderDesktopRow = (order: Order) => {
        const isExpanded = expandedOrders[order.id];
        const isSubmitting = submittingReturns.has(order.id);
        const orderReturns = returnData[order.id];

        return (
            <React.Fragment key={order.id}>
                {/* Main Order Row */}
                <tr className="hover:bg-gray-50 cursor-pointer" onClick={() => toggleOrderExpansion(order.id)}>
                    <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                            {isExpanded ? (
                                <ChevronDownIcon className="h-4 w-4 text-gray-400 mr-2" />
                            ) : (
                                <ChevronRightIcon className="h-4 w-4 text-gray-400 mr-2" />
                            )}
                            <span className="text-sm font-medium text-gray-900">#{order.id}</span>
                        </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                        <StatusBadge status={order.status.toLowerCase()} />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {order.mobile}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {order.skuItems.length}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ₹{calculateOrderTotal(order).toFixed(2)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatDeliveryDate(order.deliveryDate)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {order.facilityKey || 'N/A'}
                    </td>
                </tr>

                {/* Expanded SKU Details Row */}
                {isExpanded && (
                    <tr>
                        <td colSpan={7} className="px-6 py-4 bg-gray-50">
                            <div className="space-y-4">
                                <h4 className="font-medium text-gray-900 mb-4">{t('admin.returns.items.titleForOrder', { id: order.id })}</h4>

                                <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                                    <table className="min-w-full divide-y divide-gray-200">
                                        <thead className="bg-gray-50">
                                            <tr>
                                                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">{t('admin.returns.items.product')}</th>
                                                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">{t('admin.returns.items.unit')}</th>
                                                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">{t('admin.returns.items.expected')}</th>
                                                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">{t('admin.returns.items.returned')}</th>
                                                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">{t('admin.returns.items.loss')}</th>
                                            </tr>
                                        </thead>
                                        <tbody className="bg-white divide-y divide-gray-200">
                                            {order.skuItems.map((item, index) => {

                                                const skuDetail = skuDetails[item.skuID];
                                                const returnItem = orderReturns?.[item.skuID];
                                                const expectedQty = returnItem?.expectedQuantity || 0;
                                                const returnedQty = returnItem?.quantity || 0;

                                                if (expectedQty == 0) {
                                                    return (<></>);
                                                }

                                                return (
                                                    <tr key={index} className="hover:bg-gray-50">
                                                        <td className="px-4 py-3">
                                                            <div className="flex items-center">
                                                                <div className="h-10 w-10 flex-shrink-0">
                                                                    <Image
                                                                        src={skuDetail?.imageUrl || '/placeholder-product.png'}
                                                                        alt={skuDetail?.name?.en || `SKU ${item.skuID}`}
                                                                        width={40}
                                                                        height={40}
                                                                        className="h-10 w-10 rounded-lg object-cover"
                                                                    />
                                                                </div>
                                                                <div className="ml-4">
                                                                    <div className="text-sm font-medium text-gray-900">#{item.skuID}</div>
                                                                    {skuDetail && (
                                                                        <div className="text-sm text-gray-500">{skuDetail.name.en}</div>
                                                                    )}
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td className="px-4 py-3">
                                                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                                {item.unit}
                                                            </span>
                                                        </td>
                                                        <td className="px-4 py-3 text-sm text-gray-900">{expectedQty}</td>
                                                        <td className="px-4 py-3">
                                                            <div className="flex items-center space-x-2">
                                                                <button
                                                                    onClick={() => updateReturnQuantity(order.id, item.skuID, returnedQty - 1)}
                                                                    disabled={returnedQty <= 0 || isSubmitting}
                                                                    className="h-6 w-6 rounded-full bg-red-100 text-red-600 hover:bg-red-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                                                                >
                                                                    <MinusIcon className="h-3 w-3" />
                                                                </button>
                                                                <span className="w-8 text-center text-sm font-medium">{returnedQty}</span>
                                                                <button
                                                                    onClick={() => updateReturnQuantity(order.id, item.skuID, returnedQty + 1)}
                                                                    disabled={returnedQty >= expectedQty || isSubmitting}
                                                                    className="h-6 w-6 rounded-full bg-green-100 text-green-600 hover:bg-green-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                                                                >
                                                                    <PlusIcon className="h-3 w-3" />
                                                                </button>
                                                            </div>
                                                        </td>
                                                        <td className="px-4 py-3">
                                                            <span className={`text-sm font-medium ${returnItem && returnItem.inventoryLoss > 0
                                                                ? 'text-orange-600'
                                                                : 'text-green-600'
                                                                }`}>
                                                                {returnItem ? returnItem.inventoryLoss : expectedQty}
                                                            </span>
                                                        </td>
                                                    </tr>
                                                );
                                            })}
                                        </tbody>
                                    </table>
                                </div>

                                <div className="flex justify-end">
                                    <button
                                        onClick={() => submitReturns(order)}
                                        disabled={isSubmitting}
                                        className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                                    >
                                        {isSubmitting ? (
                                            <>
                                                <ArrowPathIcon className="h-4 w-4 animate-spin" />
                                                <span>{t('admin.returns.actions.submitting')}</span>
                                            </>
                                        ) : (
                                            <>
                                                <CheckIcon className="h-4 w-4" />
                                                <span>{t('admin.returns.actions.submitReturns')}</span>
                                            </>
                                        )}
                                    </button>
                                </div>
                            </div>
                        </td>
                    </tr>
                )}
            </React.Fragment>
        );
    };

    if (ordersError) {
        return (
            <AdminGuard requiredPermission="returnOrders">
                <div className="min-h-screen bg-gray-50 px-0 py-4 sm:px-0 lg:px-0">
                    <div className="max-w-7xl mx-auto">
                        <div className="flex flex-col items-center justify-center min-h-[50vh] text-center">
                            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                                <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                </svg>
                            </div>
                            <h3 className="text-lg font-medium text-gray-900 mb-2">{t('admin.returns.error.title')}</h3>
                            <p className="text-sm text-gray-600 mb-6">
                                {ordersErrorMessage?.message || t('admin.returns.error.description')}
                            </p>
                            <button
                                onClick={() => refetchOrders()}
                                className="inline-flex items-center justify-center px-4 py-3 sm:py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 min-h-[44px] touch-manipulation active:scale-95 transform transition-transform"
                            >
                                {t('admin.returns.error.retry')}
                            </button>
                        </div>
                    </div>
                </div>
            </AdminGuard>
        );
    }

    return (
        <AdminGuard requiredPermission="returnOrders">
            <div className="min-h-screen bg-gray-50 px-2 py-2 sm:px-2 lg:px-2">
                <div className="max-w-7xl mx-auto space-y-4 sm:space-y-6">
                    {/* Page Header */}
                    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6">
                        <div>
                            <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900">
                                {t('admin.returns.title')}
                            </h1>
                            <p className="text-sm sm:text-base text-gray-600 mt-1">
                                {t('admin.returns.subtitle')}
                            </p>
                        </div>
                    </div>

                    {/* Date Range Filters */}
                    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6">
                        <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-4">
                            {t('admin.returns.filters.title')}
                        </h3>

                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                            {/* Date From */}
                            <div>
                                <DatePicker
                                    label={t('admin.returns.filters.dateFrom')}
                                    value={dateFrom}
                                    onChange={(value) => updateDateFilter('dateFrom', value)}
                                    placeholder="dd/MM/yyyy"
                                />
                            </div>

                            {/* Date To */}
                            <div>
                                <DatePicker
                                    label={t('admin.returns.filters.dateTo')}
                                    value={dateTo}
                                    onChange={(value) => updateDateFilter('dateTo', value)}
                                    placeholder="dd/MM/yyyy"
                                />
                            </div>

                            {/* Refresh Button */}
                            <div className="flex items-end">
                                <button
                                    onClick={handleRefresh}
                                    disabled={isFetching}
                                    className="inline-flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed min-h-[42px]"
                                    title={t('common.refresh')}
                                >
                                    <ArrowPathIcon className={`h-4 w-4 mr-2 ${isFetching ? 'animate-spin' : ''}`} />
                                    {t('common.refresh')}
                                </button>
                            </div>
                        </div>

                        <div className="mt-4 text-sm text-gray-600">
                            {t('admin.returns.filters.summary', { count: partiallyDeliveredOrders.length })}
                        </div>
                    </div>

                    {/* Returns Content */}
                    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                        {ordersLoading || skuLoading ? (
                            <div className="p-4 sm:p-6">
                                <div className="animate-pulse space-y-4">
                                    <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                                    {[...Array(3)].map((_, i) => (
                                        <div key={i} className="h-16 bg-gray-200 rounded"></div>
                                    ))}
                                </div>
                            </div>
                        ) : partiallyDeliveredOrders.length === 0 ? (
                            <div className="p-12 text-center">
                                <svg className="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99" />
                                </svg>
                                <h3 className="text-lg font-medium text-gray-900 mb-2">{t('admin.returns.table.empty')}</h3>
                                <p className="text-sm text-gray-600">
                                    {t('admin.returns.table.emptyDescription')}
                                </p>
                            </div>
                        ) : (
                            <>
                                {/* Mobile View */}
                                <div className="lg:hidden">
                                    <div className="p-4">
                                        {partiallyDeliveredOrders.map(renderMobileCard)}
                                    </div>
                                </div>

                                {/* Desktop View */}
                                <div className="hidden lg:block overflow-x-auto">
                                    <table className="min-w-full divide-y divide-gray-200">
                                        <thead className="bg-gray-50">
                                            <tr>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    {t('admin.returns.table.orderId')}
                                                </th>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    {t('admin.returns.table.status')}
                                                </th>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    {t('admin.returns.table.mobile')}
                                                </th>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    {t('admin.returns.table.items')}
                                                </th>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    {t('admin.returns.table.total')}
                                                </th>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    {t('admin.returns.table.deliveryDate')}
                                                </th>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    {t('admin.returns.table.facility')}
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody className="bg-white divide-y divide-gray-200">
                                            {partiallyDeliveredOrders.map(renderDesktopRow)}
                                        </tbody>
                                    </table>
                                </div>
                            </>
                        )}
                    </div>
                </div>
            </div>
        </AdminGuard>
    );
};

export default ReturnsPage;
"use client";

import React, { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import { useAdminPermissions } from '../../hooks/useAdminPermissions';
import { useTranslation } from 'react-i18next';
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';

interface NavItem {
    name: string;
    href: string;
    icon: React.ReactNode;
    permission?: keyof import('../../types/auth').AdminPermissions;
    badge?: string;
}

interface AdminSidebarProps {
    isMobileOpen?: boolean;
    onCloseMobile?: () => void;
    onCollapseChange?: (isCollapsed: boolean) => void;
}

const AdminSidebar: React.FC<AdminSidebarProps> = ({
    isMobileOpen = false,
    onCloseMobile,
    onCollapseChange
}) => {
    const pathname = usePathname();
    const { checkPermission } = useAdminPermissions();
    const { t } = useTranslation();
    const [isCollapsed, setIsCollapsed] = useState(false);

    const navigation: NavItem[] = [
        {
            name: t('admin.navigation.dashboard', 'Dashboard'),
            href: '/admin',
            icon: (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6a2 2 0 01-2 2H10a2 2 0 01-2-2V5z" />
                </svg>
            ),
        },
        {
            name: t('admin.navigation.orders', 'Orders'),
            href: '/admin/orders',
            icon: (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
            ),
            permission: 'viewAllOrders',
        },
        {
            name: t('admin.navigation.carts', 'Carts'),
            href: '/admin/cart',
            icon: (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m6 0V9a2 2 0 00-2-2H9a2 2 0 00-2-2v6z" />
                </svg>
            ),
            permission: 'viewAllCarts',
        },
        {
            name: t('admin.navigation.categories', 'Categories'),
            href: '/admin/categories',
            icon: (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
            ),
            permission: 'manageCategories',
        },
        {
            name: t('admin.navigation.skus', 'SKUs'),
            href: '/admin/skus',
            icon: (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                </svg>
            ),
            permission: 'manageSku',
        },
        {
            name: t('admin.navigation.picker', 'Picker'),
            href: '/admin/picker',
            icon: (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.25 7.5l-.625 10.632a2.25 2.25 0 01-2.247 2.118H6.622a2.25 2.25 0 01-2.247-2.118L3.75 7.5M10 11.25h4M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125z" />
                </svg>
            ),
            permission: 'pickOrder',
        },
        {
            name: t('admin.navigation.returns', 'Returns'),
            href: '/admin/returns',
            icon: (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99" />
                </svg>
            ),
            permission: 'returnOrders',
        },
        {
            name: t('admin.navigation.leads', 'Leads'),
            href: '/admin/leads',
            icon: (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.982 18.725A7.488 7.488 0 0012 15.75a7.488 7.488 0 00-5.982 2.975m11.963 0a9 9 0 10-11.963 0m11.963 0A8.966 8.966 0 0112 21a8.966 8.966 0 01-5.982-2.275M15 9.75a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
            ),
            permission: 'viewLeads',
        },
    ];

    const isActive = (href: string) => {
        if (href === '/admin') {
            return pathname === '/admin';
        }
        return pathname.startsWith(href);
    };

    const hasPermission = (permission?: keyof import('../../types/auth').AdminPermissions) => {
        if (!permission) return true;
        return checkPermission(permission);
    };

    const handleLinkClick = () => {
        // Close mobile menu when a link is clicked
        if (onCloseMobile && isMobileOpen) {
            onCloseMobile();
        }
    };

    const handleToggleCollapse = () => {
        const newCollapsedState = !isCollapsed;
        setIsCollapsed(newCollapsedState);
        onCollapseChange?.(newCollapsedState);
    };

    // Responsive sidebar classes
    const sidebarClasses = `
        fixed inset-y-0 left-0 z-50 bg-white shadow-lg border-r border-gray-200 flex flex-col
        transform transition-all duration-300 ease-in-out
        md:relative md:translate-x-0 md:z-auto
        ${isMobileOpen ? 'translate-x-0' : '-translate-x-full md:translate-x-0'}
        ${isCollapsed ? 'w-16' : 'w-64'}
    `.trim();

    return (
        <div className={sidebarClasses}>
            {/* Logo/Brand */}
            <div className="flex items-center justify-between h-16 px-4 border-b border-gray-200">
                {!isCollapsed ? (
                    <div className="flex items-center">
                        <Image
                            src="/logo-en.png"
                            alt={t('admin.portal.logoAlt', 'WOW Vandhachu')}
                            width={120}
                            height={32}
                            className="h-8 w-auto"
                        />
                        <span className="ml-2 text-sm font-medium text-gray-600">{t('admin.portal.branding', 'Admin')}</span>
                    </div>
                ) : (
                    <div className="flex items-center justify-center w-full">
                        <Image
                            src="/logo-en.png"
                            alt={t('admin.portal.logoAlt', 'WOW Vandhachu')}
                            width={32}
                            height={32}
                            className="h-8 w-8 object-contain"
                        />
                    </div>
                )}

                {/* Collapse Toggle Button - Only show on desktop */}
                <button
                    onClick={handleToggleCollapse}
                    className={`p-1.5 rounded-md hover:bg-gray-100 transition-colors hidden md:flex ${isCollapsed ? 'mx-auto' : 'ml-auto'
                        }`}
                    title={isCollapsed ? t('admin.navigation.expand', 'Expand sidebar') : t('admin.navigation.collapse', 'Collapse sidebar')}
                >
                    {isCollapsed ? (
                        <ChevronRightIcon className="h-5 w-5 text-gray-600" />
                    ) : (
                        <ChevronLeftIcon className="h-5 w-5 text-gray-600" />
                    )}
                </button>
            </div>

            {/* Navigation */}
            <nav className={`mt-6 flex-1 ${isCollapsed ? 'px-2' : 'px-3'}`}>
                <ul className="space-y-1">
                    {navigation.map((item) => {
                        const active = isActive(item.href);
                        const permitted = hasPermission(item.permission);
                        const disabled = !permitted || item.badge === 'Soon';

                        return (
                            <li key={item.name}>
                                {disabled ? (
                                    <div
                                        className={`flex items-center text-sm font-medium text-gray-400 cursor-not-allowed min-h-[44px] ${isCollapsed ? 'px-2 py-3 justify-center' : 'px-3 py-3'
                                            }`}
                                        title={isCollapsed ? item.name : undefined}
                                    >
                                        <span className={isCollapsed ? '' : 'mr-3'}>{item.icon}</span>
                                        {!isCollapsed && (
                                            <>
                                                {item.name}
                                                {item.badge && (
                                                    <span className="ml-auto inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-600">
                                                        {item.badge}
                                                    </span>
                                                )}
                                            </>
                                        )}
                                    </div>
                                ) : (
                                    <Link
                                        href={item.href}
                                        onClick={handleLinkClick}
                                        className={`flex items-center text-sm font-medium rounded-md transition-colors min-h-[44px] ${isCollapsed ? 'px-2 py-3 justify-center' : 'px-3 py-3'
                                            } ${active
                                                ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                                                : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                                            }`}
                                        title={isCollapsed ? item.name : undefined}
                                    >
                                        <span className={isCollapsed ? '' : 'mr-3'}>{item.icon}</span>
                                        {!isCollapsed && (
                                            <>
                                                {item.name}
                                                {item.badge && (
                                                    <span className="ml-auto inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-600">
                                                        {item.badge}
                                                    </span>
                                                )}
                                            </>
                                        )}
                                    </Link>
                                )}
                            </li>
                        );
                    })}
                </ul>
            </nav>

            {/* Footer */}
            {!isCollapsed && (
                <div className="p-4 border-t border-gray-200 mt-auto">
                    <div className="text-xs text-gray-500 text-center">
                        {t('admin.portal.version', 'Admin Portal v{{version}}', { version: process.env.NEXT_PUBLIC_VERSION })}
                    </div>
                </div>
            )}
        </div>
    );
};

export default AdminSidebar; 
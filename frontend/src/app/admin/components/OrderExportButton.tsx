'use client';

import React, { useState } from 'react';
import { ArrowDownTrayIcon } from '@heroicons/react/24/outline';
import { useTranslation } from 'react-i18next';
import { Button } from "@/components/ui/button";

interface ExportButtonProps {
    onExport: (exportFiltered: boolean) => Promise<void>;
    filteredCount: number;
    totalCount: number;
    disabled?: boolean;
    entityName?: string; // e.g., 'orders', 'carts' 
}

const ExportButton: React.FC<ExportButtonProps> = ({
    onExport,
    filteredCount,
    totalCount,
    disabled = false,
    entityName = 'orders'
}) => {
    const { t } = useTranslation();
    const [isExporting, setIsExporting] = useState(false);
    const [showOptions, setShowOptions] = useState(false);
    const [exportFiltered, setExportFiltered] = useState(true);

    const handleExport = async () => {
        setIsExporting(true);
        try {
            await onExport(exportFiltered);
        } finally {
            setIsExporting(false);
            setShowOptions(false);
        }
    };

    const handleButtonClick = () => {
        if (filteredCount === totalCount) {
            // If no filters applied, export directly
            handleExport();
        } else {
            // Show options when filters are applied
            setShowOptions(!showOptions);
        }
    };

    return (
        <div className="relative">
            <Button
                variant="default"
                size="sm"
                onClick={handleButtonClick}
                disabled={disabled || isExporting}
                className="inline-flex items-center bg-emerald-600 hover:bg-emerald-700 text-white"
                title={t(`admin.${entityName}.export.title`)}
            >
                <ArrowDownTrayIcon className="w-4 h-4 mr-2" />
                {isExporting ? t(`admin.${entityName}.export.processing`) : t('admin.importExport.export')}
            </Button>

            {/* Export Options Dropdown */}
            {showOptions && (
                <div className="absolute top-full right-0 mt-2 w-80 bg-popover border border-border rounded-lg shadow-lg z-50">
                    <div className="p-4">
                        <h3 className="text-sm font-medium text-popover-foreground mb-3">
                            {t(`admin.${entityName}.export.title`)}
                        </h3>

                        {/* Filtered Data Option */}
                        <label className="flex items-start space-x-3 mb-4 cursor-pointer">
                            <input
                                type="radio"
                                name="exportOption"
                                checked={exportFiltered}
                                onChange={() => setExportFiltered(true)}
                                className="mt-1 h-4 w-4 text-primary focus:ring-primary border-input"
                            />
                            <div className="flex-1">
                                <div className="text-sm font-medium text-popover-foreground">
                                    {t(`admin.${entityName}.export.filteredData`, { count: filteredCount })}
                                </div>
                                <div className="text-xs text-muted-foreground mt-1">
                                    {t(`admin.${entityName}.export.filteredDescription`)}
                                </div>
                            </div>
                        </label>

                        {/* All Data Option */}
                        <label className="flex items-start space-x-3 mb-4 cursor-pointer">
                            <input
                                type="radio"
                                name="exportOption"
                                checked={!exportFiltered}
                                onChange={() => setExportFiltered(false)}
                                className="mt-1 h-4 w-4 text-primary focus:ring-primary border-input"
                            />
                            <div className="flex-1">
                                <div className="text-sm font-medium text-popover-foreground">
                                    {t(`admin.${entityName}.export.allData`, { total: totalCount })}
                                </div>
                                <div className="text-xs text-muted-foreground mt-1">
                                    {t(`admin.${entityName}.export.allDescription`)}
                                </div>
                            </div>
                        </label>

                        {/* Action Buttons */}
                        <div className="flex items-center justify-end space-x-2">
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => setShowOptions(false)}
                                className="text-muted-foreground hover:text-foreground"
                            >
                                {t('admin.actions.cancel')}
                            </Button>
                            <Button
                                size="sm"
                                onClick={handleExport}
                                disabled={isExporting}
                            >
                                {isExporting ? t(`admin.${entityName}.export.processing`) : t('admin.importExport.export')}
                            </Button>
                        </div>
                    </div>
                </div>
            )}

            {/* Backdrop to close dropdown */}
            {showOptions && (
                <div
                    className="fixed inset-0 z-40"
                    onClick={() => setShowOptions(false)}
                />
            )}
        </div>
    );
};

export default ExportButton; 
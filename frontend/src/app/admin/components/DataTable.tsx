"use client";

import React, { useState, useMemo } from 'react';

export interface Column<T> {
    key: string;
    header: string;
    render?: (item: T) => React.ReactNode;
    sortable?: boolean;
    getSortValue?: (item: T) => any;
    width?: string;
    className?: string;
}

interface DataTableProps<T> {
    data: T[];
    columns: Column<T>[];
    loading?: boolean;
    emptyMessage?: string;
    className?: string;
    onRowClick?: (item: T) => void;
    selectedItems?: T[];
    onSelectionChange?: (items: T[]) => void;
    getItemId?: (item: T) => string | number;
}

type SortDirection = 'asc' | 'desc';

interface SortState {
    column: string | null;
    direction: SortDirection | null;
}

function DataTable<T>({
    data,
    columns,
    loading = false,
    emptyMessage = "No data available",
    className = "",
    onRowClick,
    selectedItems = [],
    onSelectionChange,
    getItemId,
}: DataTableProps<T>) {
    const [sortState, setSortState] = useState<SortState>({
        column: null,
        direction: null
    });

    const hasSelection = onSelectionChange && getItemId;
    const isAllSelected = hasSelection && data.length > 0 && selectedItems.length === data.length;
    const isPartiallySelected = hasSelection && selectedItems.length > 0 && selectedItems.length < data.length;

    // Sort data based on current sort state
    const sortedData = useMemo(() => {
        if (!sortState.column || !sortState.direction) {
            return data;
        }

        return [...data].sort((a, b) => {
            const column = columns.find(col => col.key === sortState.column);

            // Use getSortValue if available, otherwise fall back to direct property access
            const aValue = column?.getSortValue ? column.getSortValue(a) : (a as any)[sortState.column!];
            const bValue = column?.getSortValue ? column.getSortValue(b) : (b as any)[sortState.column!];

            // Handle null/undefined values
            if (aValue == null && bValue == null) return 0;
            if (aValue == null) return sortState.direction === 'asc' ? -1 : 1;
            if (bValue == null) return sortState.direction === 'asc' ? 1 : -1;

            // Handle different data types
            let comparison = 0;

            if (typeof aValue === 'string' && typeof bValue === 'string') {
                comparison = aValue.localeCompare(bValue);
            } else if (typeof aValue === 'number' && typeof bValue === 'number') {
                comparison = aValue - bValue;
            } else if (aValue instanceof Date && bValue instanceof Date) {
                comparison = aValue.getTime() - bValue.getTime();
            } else {
                // Convert to string for comparison
                comparison = String(aValue).localeCompare(String(bValue));
            }

            return sortState.direction === 'asc' ? comparison : -comparison;
        });
    }, [data, sortState, columns]);

    const handleSort = (columnKey: string) => {
        const column = columns.find(col => col.key === columnKey);
        if (!column?.sortable) return;

        setSortState(prevState => {
            if (prevState.column === columnKey) {
                // Toggle between asc and desc (2-state only)
                const newDirection: SortDirection = prevState.direction === 'asc' ? 'desc' : 'asc';
                return {
                    column: columnKey,
                    direction: newDirection
                };
            } else {
                // New column, start with ascending
                return {
                    column: columnKey,
                    direction: 'asc'
                };
            }
        });
    };

    const getSortIcon = (columnKey: string) => {
        if (sortState.column !== columnKey) {
            // Default unsorted state
            return (
                <svg className="w-4 h-4 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />
                </svg>
            );
        }

        if (sortState.direction === 'asc') {
            return (
                <svg className="w-4 h-4 text-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
                </svg>
            );
        }

        if (sortState.direction === 'desc') {
            return (
                <svg className="w-4 h-4 text-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
            );
        }

        return null;
    };

    const handleSelectAll = () => {
        if (!onSelectionChange) return;

        if (isAllSelected) {
            onSelectionChange([]);
        } else {
            onSelectionChange([...sortedData]);
        }
    };

    const handleSelectItem = (item: T) => {
        if (!onSelectionChange || !getItemId) return;

        const itemId = getItemId(item);
        const isSelected = selectedItems.some(selected => getItemId(selected) === itemId);

        if (isSelected) {
            onSelectionChange(selectedItems.filter(selected => getItemId(selected) !== itemId));
        } else {
            onSelectionChange([...selectedItems, item]);
        }
    };

    const isItemSelected = (item: T): boolean => {
        if (!getItemId) return false;
        const itemId = getItemId(item);
        return selectedItems.some(selected => getItemId(selected) === itemId);
    };

    const renderCellContent = (item: T, column: Column<T>) => {
        if (column.render) {
            return column.render(item);
        }

        // Default rendering - access the property by key
        const value = (item as any)[column.key];
        return value !== null && value !== undefined ? String(value) : '-';
    };

    if (loading) {
        return (
            <div className={`bg-card shadow rounded-lg ${className}`}>
                <div className="px-6 py-4 border-b border-border">
                    <div className="animate-pulse bg-muted h-6 w-32 rounded"></div>
                </div>
                <div className="p-6">
                    <div className="space-y-4">
                        {[...Array(5)].map((_, i) => (
                            <div key={i} className="animate-pulse flex space-x-4">
                                <div className="bg-muted h-4 w-full rounded"></div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        );
    }

    if (data.length === 0) {
        return (
            <div className={`bg-card shadow rounded-lg ${className}`}>
                <div className="p-12 text-center">
                    <svg className="w-12 h-12 mx-auto text-muted-foreground mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                    <p className="text-muted-foreground">{emptyMessage}</p>
                </div>
            </div>
        );
    }

    return (
        <div className={`bg-card shadow rounded-lg overflow-hidden ${className}`}>
            <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-border">
                    <thead className="bg-muted/50">
                        <tr>
                            {hasSelection && (
                                <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider w-12">
                                    <input
                                        type="checkbox"
                                        checked={isAllSelected}
                                        ref={(input) => {
                                            if (input) input.indeterminate = isPartiallySelected || false;
                                        }}
                                        onChange={handleSelectAll}
                                        className="h-4 w-4 text-primary focus:ring-primary border-input rounded"
                                    />
                                </th>
                            )}
                            {columns.map((column) => (
                                <th
                                    key={column.key}
                                    className={`px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider ${column.className || ''} ${column.sortable ? 'cursor-pointer hover:bg-muted/70 transition-colors' : ''}`}
                                    style={column.width ? { width: column.width } : undefined}
                                    onClick={() => column.sortable && handleSort(column.key)}
                                >
                                    <div className="flex items-center space-x-1">
                                        <span>{column.header}</span>
                                        {column.sortable && getSortIcon(column.key)}
                                    </div>
                                </th>
                            ))}
                        </tr>
                    </thead>
                    <tbody className="bg-card divide-y divide-border">
                        {sortedData.map((item, index) => (
                            <tr
                                key={getItemId ? getItemId(item) : index}
                                className={`${onRowClick ? 'hover:bg-muted/50 cursor-pointer' : ''
                                    } ${isItemSelected(item) ? 'bg-primary/10' : ''} transition-colors`}
                                onClick={() => onRowClick?.(item)}
                            >
                                {hasSelection && (
                                    <td className="px-6 py-4 whitespace-nowrap w-12">
                                        <input
                                            type="checkbox"
                                            checked={isItemSelected(item)}
                                            onChange={(e) => {
                                                e.stopPropagation();
                                                handleSelectItem(item);
                                            }}
                                            className="h-4 w-4 text-primary focus:ring-primary border-input rounded"
                                        />
                                    </td>
                                )}
                                {columns.map((column) => (
                                    <td
                                        key={column.key}
                                        className={`px-6 py-4 whitespace-nowrap text-sm text-foreground ${column.className || ''}`}
                                    >
                                        {renderCellContent(item, column)}
                                    </td>
                                ))}
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
        </div>
    );
}

export default DataTable;
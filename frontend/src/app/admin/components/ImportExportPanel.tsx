'use client';

import React, { useState, useRef } from 'react';
import {
    ArrowDownTrayIcon,
    ArrowUpTrayIcon
} from '@heroicons/react/24/outline';

export interface ImportExportOption {
    id: string;
    label: string;
    description?: string;
    templateType?: string;
}

interface ImportExportPanelProps {
    // Export options
    exportOptions: ImportExportOption[];
    onExport: (optionId: string) => Promise<void>;

    // Import options
    importOptions: ImportExportOption[];
    onImport: (optionId: string, file: File) => Promise<any>;

    // UI customization
    className?: string;

    // i18n
    t: (key: string) => string;
}

const ImportExportPanel: React.FC<ImportExportPanelProps> = ({
    exportOptions,
    onExport,
    importOptions,
    onImport,
    className = '',
    t
}) => {
    const [isExporting, setIsExporting] = useState(false);
    const [isImporting, setIsImporting] = useState(false);
    const fileInputRef = useRef<HTMLInputElement>(null);

    // Use the first export option as the default
    const defaultExportOption = exportOptions[0];
    const defaultImportOption = importOptions[0];

    const handleExportClick = async () => {
        if (!defaultExportOption) return;

        setIsExporting(true);
        try {
            await onExport(defaultExportOption.id);
        } finally {
            setIsExporting(false);
        }
    };

    const handleImportClick = () => {
        if (!defaultImportOption) return;

        // Store the selected option in the file input's data attribute
        if (fileInputRef.current) {
            fileInputRef.current.dataset.optionId = defaultImportOption.id;
            fileInputRef.current.click();
        }
    };

    const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        const optionId = event.target.dataset.optionId;

        if (!file || !optionId) return;

        setIsImporting(true);
        try {
            await onImport(optionId, file);
        } catch (error) {
            console.error('Import validation failed:', error);
            // TODO: Show error notification
        } finally {
            setIsImporting(false);
        }

        // Reset file input
        if (fileInputRef.current) {
            fileInputRef.current.value = '';
            delete fileInputRef.current.dataset.optionId;
        }
    };

    return (
        <div className={`flex items-center space-x-3 ${className}`}>
            {/* Export Button */}
            <button
                onClick={handleExportClick}
                className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                disabled={isExporting || isImporting || !defaultExportOption}
            >
                <ArrowDownTrayIcon className="w-4 h-4 mr-2" />
                {isExporting ? t('admin.importExport.exporting') : t('admin.importExport.export')}
            </button>

            {/* Import Button */}
            <button
                onClick={handleImportClick}
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                disabled={isExporting || isImporting || !defaultImportOption}
            >
                <ArrowUpTrayIcon className="w-4 h-4 mr-2" />
                {isImporting ? t('admin.importExport.importing') : t('admin.importExport.import')}
            </button>

            {/* Hidden file input */}
            <input
                ref={fileInputRef}
                type="file"
                accept=".csv"
                onChange={handleFileSelect}
                className="hidden"
            />
        </div>
    );
};

export default ImportExportPanel; 
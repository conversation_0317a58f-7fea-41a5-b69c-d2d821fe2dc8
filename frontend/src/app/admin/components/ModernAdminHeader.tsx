"use client"

import React from 'react'
import Link from 'next/link'
import { useAuth } from '../../context/AuthContext'
import { useAdminPermissions } from '../../hooks/useAdminPermissions'
import { useTranslation } from 'react-i18next'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { ThemeToggle } from "@/components/theme-toggle"
import { LanguageSwitcher } from '../../components/Header/LanguageSwitcher'
import {
    UserIcon,
    ArrowRightStartOnRectangleIcon,
    Cog6ToothIcon,
    ExclamationTriangleIcon,
} from '@heroicons/react/24/outline'

interface ModernAdminHeaderProps {
    onMobileMenuToggle?: () => void
}

export function ModernAdminHeader({ onMobileMenuToggle: _onMobileMenuToggle }: ModernAdminHeaderProps) {
    const { t } = useTranslation()
    const { userDetails, userLogout } = useAuth()
    const { getUserRoles, getUserPermissions, checkPermission } = useAdminPermissions()

    const handleLogout = () => {
        userLogout()
    }

    const userRoles = getUserRoles()
    const userPermissions = getUserPermissions()

    return (
        <header className="flex h-16 shrink-0 items-center gap-2 px-4 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
            {/* Sidebar trigger - works for both mobile and desktop */}
            <SidebarTrigger className="-ml-1 text-foreground hover:text-foreground/80" />

            {/* Page title area - flexible space */}
            <div className="flex-1">
                <h1 className="text-lg font-semibold text-foreground">
                    {t('admin.dashboard.title', 'Admin Dashboard')}
                </h1>
            </div>

            {/* Header actions */}
            <div className="flex items-center gap-2">
                {/* Language Switcher */}
                <LanguageSwitcher />

                {/* Theme Toggle */}
                <ThemeToggle />

                {/* User Menu */}
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="relative h-9 w-9 rounded-full">
                            <Avatar className="h-9 w-9">
                                <AvatarFallback className="bg-primary text-primary-foreground">
                                    {userDetails?.name?.charAt(0)?.toUpperCase() || 'A'}
                                </AvatarFallback>
                            </Avatar>
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="w-56" align="end" forceMount>
                        <DropdownMenuLabel className="font-normal">
                            <div className="flex flex-col space-y-1">
                                <p className="text-sm font-medium leading-none">
                                    {userDetails?.name || t('admin.user.defaultName', 'Admin User')}
                                </p>
                                <p className="text-xs leading-none text-muted-foreground">
                                    {userDetails?.mobileNumber || t('admin.user.defaultMobile', '+91XXXXXXXXXX')}
                                </p>
                            </div>
                        </DropdownMenuLabel>

                        <DropdownMenuSeparator />

                        {/* User roles section */}
                        {userRoles.length > 0 && (
                            <>
                                <DropdownMenuLabel>
                                    <div className="flex items-center gap-2">
                                        <UserIcon className="h-4 w-4" />
                                        <span className="text-xs font-medium">
                                            {t('admin.user.roles', 'Roles')}
                                        </span>
                                    </div>
                                </DropdownMenuLabel>
                                <div className="px-2 py-1">
                                    <div className="flex flex-wrap gap-1">
                                        {userRoles.map((role) => (
                                            <Badge key={role} variant="secondary" className="text-xs">
                                                {role}
                                            </Badge>
                                        ))}
                                    </div>
                                </div>
                                <DropdownMenuSeparator />
                            </>
                        )}

                        {/* Permissions badge */}
                        {userPermissions.length > 0 && (
                            <>
                                <DropdownMenuItem disabled>
                                    <div className="flex items-center gap-2">
                                        <Cog6ToothIcon className="h-4 w-4" />
                                        <span className="text-xs">
                                            {userPermissions.length} {t('admin.user.permission', { count: userPermissions.length })}
                                        </span>
                                        <Badge variant="outline" className="text-xs ml-auto">
                                            {userPermissions.length}
                                        </Badge>
                                    </div>
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                            </>
                        )}

                        {/* System Admin - Only show if user has systemAdmin permission */}
                        {checkPermission('systemAdmin') && (
                            <>
                                <DropdownMenuItem asChild>
                                    <Link href="/admin/system-admin" className="flex items-center">
                                        <ExclamationTriangleIcon className="mr-2 h-4 w-4 text-amber-600" />
                                        <span>{t('admin.systemAdmin.menuTitle', 'System Admin')}</span>
                                    </Link>
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                            </>
                        )}

                        {/* Logout */}
                        <DropdownMenuItem onClick={handleLogout}>
                            <ArrowRightStartOnRectangleIcon className="mr-2 h-4 w-4" />
                            <span>{t('header.logout', 'Logout')}</span>
                        </DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            </div>
        </header>
    )
} 
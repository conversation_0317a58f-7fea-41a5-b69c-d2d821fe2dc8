"use client";

import React, { useState } from 'react';
import Link from 'next/link';
import {
    DndContext,
    closestCenter,
    KeyboardSensor,
    PointerSensor,
    useSensor,
    useSensors,
    DragEndEvent,
} from '@dnd-kit/core';
import {
    arrayMove,
    SortableContext,
    sortableKeyboardCoordinates,
    verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
    useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Category } from '../../types/category';
import DynamicHeroIcon from '../../components/common/DynamicHeroIcon';
import StatusBadge from './StatusBadge';
import { useTranslation } from 'react-i18next';

interface SortableCategoryRowProps {
    category: Category;
    isSelected: boolean;
    onSelect: (category: Category, selected: boolean) => void;
}

const SortableCategoryRow: React.FC<SortableCategoryRowProps> = ({
    category,
    isSelected,
    onSelect,
}) => {
    const { t } = useTranslation();
    const {
        attributes,
        listeners,
        setNodeRef,
        transform,
        transition,
        isDragging,
    } = useSortable({ id: category.id.toString() });

    const style = {
        transform: CSS.Transform.toString(transform),
        transition,
        opacity: isDragging ? 0.5 : 1,
    };

    return (
        <tr
            ref={setNodeRef}
            style={style}
            className={`${isDragging ? 'bg-gray-50' : 'bg-white'} hover:bg-gray-50 ${category.id < 0 ? 'border-l-4 border-l-green-400' : ''}`}
        >
            {/* Checkbox */}
            <td className="px-6 py-4 whitespace-nowrap">
                <input
                    type="checkbox"
                    checked={isSelected}
                    onChange={(e) => onSelect(category, e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
            </td>

            {/* Drag Handle */}
            <td className="px-2 py-4 whitespace-nowrap">
                <div
                    {...attributes}
                    {...listeners}
                    className="cursor-grab active:cursor-grabbing p-1 text-gray-400 hover:text-gray-600"
                >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8h16M4 16h16" />
                    </svg>
                </div>
            </td>

            {/* Name */}
            <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center">
                    {category.background && (
                        <div
                            className="w-4 h-4 rounded mr-3 border border-gray-200"
                            style={{ backgroundColor: category.background }}
                        />
                    )}
                    <div>
                        <div className="font-medium text-gray-900">{category.name.en}</div>
                        {category.name.ta && (
                            <div className="text-sm text-gray-500">{category.name.ta}</div>
                        )}
                    </div>
                </div>
            </td>

            {/* Icon */}
            <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center">
                    {category.icon ? (
                        <DynamicHeroIcon
                            iconName={category.icon}
                            className="w-5 h-5 text-gray-600"
                        />
                    ) : (
                        <span className="text-sm text-gray-400">{t('admin.components.sortableTable.noIcon')}</span>
                    )}
                </div>
            </td>

            {/* SKUs */}
            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {category.skuIds.length}
            </td>

            {/* Status */}
            <td className="px-6 py-4 whitespace-nowrap">
                <StatusBadge status={category.isActive === 1 ? "active" : "inactive"} />
            </td>

            {/* Actions */}
            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div className="flex items-center space-x-2">
                    {category.id < 0 ? (
                        // New category - show disabled actions
                        <>
                            <span className="text-gray-400 cursor-not-allowed">{t('admin.components.sortableTable.edit')}</span>
                            <span className="text-gray-300">|</span>
                            <span className="text-gray-400 cursor-not-allowed">{t('admin.components.sortableTable.manageSkus')}</span>
                            <span className="text-xs text-gray-500 ml-2">{t('admin.components.sortableTable.saveFirst')}</span>
                        </>
                    ) : (
                        // Existing category - show active actions
                        <>
                            <Link
                                href={`/admin/categories/${category.id}/edit`}
                                className="text-blue-600 hover:text-blue-900"
                            >
                                {t('admin.components.sortableTable.edit')}
                            </Link>
                            <span className="text-gray-300">|</span>
                            <Link
                                href={`/admin/categories/${category.id}/manage-skus`}
                                className="text-green-600 hover:text-green-900"
                            >
                                {t('admin.components.sortableTable.manageSkus')}
                            </Link>
                        </>
                    )}
                </div>
            </td>
        </tr>
    );
};

interface SortableCategoriesTableProps {
    categories: Category[];
    selectedCategories: Category[];
    onSelectionChange: (categories: Category[]) => void;
    onReorder: (categories: Category[]) => void;
    loading?: boolean;
}

const SortableCategoriesTable: React.FC<SortableCategoriesTableProps> = ({
    categories,
    selectedCategories,
    onSelectionChange,
    onReorder,
    loading = false,
}) => {
    const { t } = useTranslation();
    const [localCategories, setLocalCategories] = useState(categories);

    // Update local state when categories prop changes
    React.useEffect(() => {
        setLocalCategories(categories);
    }, [categories]);

    const sensors = useSensors(
        useSensor(PointerSensor),
        useSensor(KeyboardSensor, {
            coordinateGetter: sortableKeyboardCoordinates,
        })
    );

    const handleDragEnd = (event: DragEndEvent) => {
        const { active, over } = event;

        if (over && active.id !== over.id) {
            const oldIndex = localCategories.findIndex(cat => cat.id?.toString() === active.id);
            const newIndex = localCategories.findIndex(cat => cat.id?.toString() === over.id);

            if (oldIndex !== -1 && newIndex !== -1) {
                const newOrder = arrayMove(localCategories, oldIndex, newIndex);
                setLocalCategories(newOrder);
                onReorder(newOrder);
            }
        }
    };

    const handleSelectCategory = (category: Category, selected: boolean) => {
        if (selected) {
            onSelectionChange([...selectedCategories, category]);
        } else {
            onSelectionChange(selectedCategories.filter(c => c.id !== category.id));
        }
    };

    const handleSelectAll = (selected: boolean) => {
        if (selected) {
            onSelectionChange(localCategories.filter(cat => cat.id != null));
        } else {
            onSelectionChange([]);
        }
    };

    const validCategories = localCategories.filter(cat => cat.id != null);
    const isAllSelected = validCategories.length > 0 && selectedCategories.length === validCategories.length;
    const isIndeterminate = selectedCategories.length > 0 && selectedCategories.length < validCategories.length;

    if (loading) {
        return (
            <div className="bg-white shadow rounded-lg">
                <div className="px-6 py-4">
                    <div className="animate-pulse">
                        <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
                        <div className="space-y-3">
                            {[...Array(3)].map((_, i) => (
                                <div key={i} className="h-4 bg-gray-200 rounded"></div>
                            ))}
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    if (validCategories.length === 0) {
        return (
            <div className="bg-white shadow rounded-lg">
                <div className="px-6 py-12 text-center">
                    <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                    </svg>
                    <h3 className="mt-2 text-sm font-medium text-gray-900">{t('admin.components.sortableTable.noCategories')}</h3>
                    <p className="mt-1 text-sm text-gray-500">{t('admin.components.sortableTable.createFirstCategory')}</p>
                </div>
            </div>
        );
    }

    return (
        <div className="bg-white shadow rounded-lg overflow-hidden">
            <DndContext
                sensors={sensors}
                collisionDetection={closestCenter}
                onDragEnd={handleDragEnd}
            >
                <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                        <tr>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <input
                                    type="checkbox"
                                    checked={isAllSelected}
                                    ref={(input) => {
                                        if (input) input.indeterminate = isIndeterminate;
                                    }}
                                    onChange={(e) => handleSelectAll(e.target.checked)}
                                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                />
                            </th>
                            <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {t('admin.components.sortableTable.order')}
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {t('admin.components.sortableTable.name')}
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {t('admin.components.sortableTable.icon')}
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {t('admin.components.sortableTable.skus')}
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {t('admin.components.sortableTable.status')}
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {t('admin.components.sortableTable.actions')}
                            </th>
                        </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                        <SortableContext
                            items={validCategories.map(cat => cat.id.toString())}
                            strategy={verticalListSortingStrategy}
                        >
                            {validCategories.map((category) => (
                                <SortableCategoryRow
                                    key={category.id}
                                    category={category}
                                    isSelected={selectedCategories.some(c => c.id === category.id)}
                                    onSelect={handleSelectCategory}
                                />
                            ))}
                        </SortableContext>
                    </tbody>
                </table>
            </DndContext>
        </div>
    );
};

export default SortableCategoriesTable; 
"use client";

import React, { useState } from 'react';
import DynamicHeroIcon from '../../components/common/DynamicHeroIcon';
import IconPickerModal from './IconPickerModal';

// Sample SVG for testing
const testSVG = `<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M12 2L2 7l10 5 10-5-10-5z" stroke="currentColor" stroke-width="2" fill="none"/>
  <path d="M2 17l10 5 10-5" stroke="currentColor" stroke-width="2" fill="none"/>
  <path d="M2 12l10 5 10-5" stroke="currentColor" stroke-width="2" fill="none"/>
</svg>`;

const TestSVGIcons: React.FC = () => {
    const [selectedIcon, setSelectedIcon] = useState<string>('UserIcon');
    const [showModal, setShowModal] = useState(false);

    return (
        <div className="p-8 space-y-8">
            <h1 className="text-2xl font-bold text-gray-900">SVG Icon System Test</h1>

            {/* Test DynamicHeroIcon with different inputs */}
            <div className="space-y-4">
                <h2 className="text-lg font-semibold text-gray-800">DynamicHeroIcon Tests</h2>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {/* HeroIcon Test */}
                    <div className="p-4 border border-gray-200 rounded-lg">
                        <h3 className="text-sm font-medium text-gray-700 mb-2">HeroIcon (UserIcon)</h3>
                        <div className="flex items-center space-x-2">
                            <DynamicHeroIcon iconName="UserIcon" className="w-6 h-6 text-blue-600" />
                            <DynamicHeroIcon iconName="UserIcon" className="w-8 h-8 text-green-600" />
                            <DynamicHeroIcon iconName="UserIcon" className="w-10 h-10 text-purple-600" />
                        </div>
                    </div>

                    {/* Custom SVG Test */}
                    <div className="p-4 border border-gray-200 rounded-lg">
                        <h3 className="text-sm font-medium text-gray-700 mb-2">Custom SVG</h3>
                        <div className="flex items-center space-x-2">
                            <DynamicHeroIcon iconName={testSVG} className="w-6 h-6 text-blue-600" />
                            <DynamicHeroIcon iconName={testSVG} className="w-8 h-8 text-green-600" />
                            <DynamicHeroIcon iconName={testSVG} className="w-10 h-10 text-purple-600" />
                        </div>
                    </div>

                    {/* Invalid SVG Test */}
                    <div className="p-4 border border-gray-200 rounded-lg">
                        <h3 className="text-sm font-medium text-gray-700 mb-2">Invalid SVG (shows error icon)</h3>
                        <div className="flex items-center space-x-2">
                            <DynamicHeroIcon iconName="<svg>invalid</svg>" className="w-6 h-6 text-red-600" />
                            <DynamicHeroIcon iconName="not-an-icon" className="w-8 h-8 text-orange-600" />
                            <DynamicHeroIcon iconName="" className="w-10 h-10 text-gray-600" />
                        </div>
                    </div>
                </div>
            </div>

            {/* Icon Picker Test */}
            <div className="space-y-4">
                <h2 className="text-lg font-semibold text-gray-800">Icon Picker Test</h2>

                <div className="p-4 border border-gray-200 rounded-lg">
                    <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center space-x-2">
                            <span className="text-sm font-medium text-gray-700">Selected Icon:</span>
                            <DynamicHeroIcon iconName={selectedIcon} className="w-6 h-6 text-blue-600" />
                            <span className="text-sm text-gray-600">
                                {selectedIcon.startsWith('<svg') ? `Custom SVG (${Math.round(selectedIcon.length / 1024 * 10) / 10}KB)` : selectedIcon}
                            </span>
                        </div>
                        <button
                            onClick={() => setShowModal(true)}
                            className="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        >
                            Change Icon
                        </button>
                    </div>

                    <div className="text-sm text-gray-600">
                        Click "Change Icon" to test the IconPickerModal with both HeroIcons and Custom SVG tabs.
                    </div>
                </div>
            </div>

            {/* Features List */}
            <div className="space-y-4">
                <h2 className="text-lg font-semibold text-gray-800">Features Implemented</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div className="space-y-2">
                        <h3 className="font-medium text-gray-700">DynamicHeroIcon Features:</h3>
                        <ul className="space-y-1 text-gray-600">
                            <li>✅ Backward compatible (existing HeroIcon names work)</li>
                            <li>✅ Automatic SVG detection (`&lt;svg` prefix)</li>
                            <li>✅ DOMPurify sanitization for security</li>
                            <li>✅ 10KB size limit with clear error messages</li>
                            <li>✅ In-memory caching for performance</li>
                            <li>✅ Fallback icons for errors</li>
                            <li>✅ Proper className and aria-hidden support</li>
                        </ul>
                    </div>
                    <div className="space-y-2">
                        <h3 className="font-medium text-gray-700">IconPickerModal Features:</h3>
                        <ul className="space-y-1 text-gray-600">
                            <li>✅ Tab interface (HeroIcons + Custom SVG)</li>
                            <li>✅ Live SVG preview with multiple sizes</li>
                            <li>✅ Real-time validation with error messages</li>
                            <li>✅ Size counter (KB used / 10KB limit)</li>
                            <li>✅ Recent custom SVGs (localStorage)</li>
                            <li>✅ Search functionality in HeroIcons tab</li>
                            <li>✅ Clear/Reset functionality</li>
                            <li>✅ Smart tab switching based on current selection</li>
                        </ul>
                    </div>
                </div>
            </div>

            {/* Icon Picker Modal */}
            <IconPickerModal
                isOpen={showModal}
                onClose={() => setShowModal(false)}
                onSelect={setSelectedIcon}
                selectedIcon={selectedIcon}
            />
        </div>
    );
};

export default TestSVGIcons; 
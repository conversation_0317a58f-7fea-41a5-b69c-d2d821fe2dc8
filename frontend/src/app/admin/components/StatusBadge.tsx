"use client";

import React from 'react';
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

interface StatusBadgeProps {
    status: 'active' | 'inactive' | 'pending' | 'confirmed' | 'cancelled' | 'completed' | string;
    size?: 'sm' | 'md' | 'lg';
    className?: string;
}

const StatusBadge: React.FC<StatusBadgeProps> = ({
    status,
    size = 'md',
    className = ''
}) => {
    const getStatusConfig = (status: string) => {
        const statusLower = status.toLowerCase();

        switch (statusLower) {
            case 'active':
                return {
                    variant: 'default' as const,
                    className: 'bg-emerald-500 hover:bg-emerald-500 text-white',
                    label: 'Active'
                };
            case 'inactive':
                return {
                    variant: 'secondary' as const,
                    className: 'bg-slate-500 hover:bg-slate-500 text-white',
                    label: 'Inactive'
                };
            case 'pending':
            case 'new':
                return {
                    variant: 'default' as const,
                    className: 'bg-amber-500 hover:bg-amber-500 text-white',
                    label: statusLower === 'new' ? 'New' : 'Pending'
                };
            case 'confirmed':
                return {
                    variant: 'default' as const,
                    className: 'bg-blue-500 hover:bg-blue-500 text-white',
                    label: 'Confirmed'
                };
            case 'cancelled':
                return {
                    variant: 'destructive' as const,
                    className: 'bg-red-500 hover:bg-red-500 text-white',
                    label: 'Cancelled'
                };
            case 'completed':
                return {
                    variant: 'default' as const,
                    className: 'bg-emerald-500 hover:bg-emerald-500 text-white',
                    label: 'Completed'
                };
            case 'in-progress':
            case 'in_progress':
                return {
                    variant: 'default' as const,
                    className: 'bg-indigo-500 hover:bg-indigo-500 text-white',
                    label: 'In Progress'
                };
            case 'abandoned':
                return {
                    variant: 'default' as const,
                    className: 'bg-orange-500 hover:bg-orange-500 text-white',
                    label: 'Abandoned'
                };
            case 'converted':
                return {
                    variant: 'default' as const,
                    className: 'bg-purple-500 hover:bg-purple-500 text-white',
                    label: 'Converted'
                };
            default:
                return {
                    variant: 'secondary' as const,
                    className: 'bg-slate-500 hover:bg-slate-500 text-white',
                    label: status.charAt(0).toUpperCase() + status.slice(1)
                };
        }
    };

    const getSizeClasses = (size: string) => {
        switch (size) {
            case 'sm':
                return 'px-2 py-0.5 text-xs h-5';
            case 'lg':
                return 'px-3 py-1 text-sm h-7';
            default:
                return 'px-2.5 py-0.5 text-xs h-6';
        }
    };

    const config = getStatusConfig(status);
    const sizeClasses = getSizeClasses(size);

    return (
        <Badge
            variant={config.variant}
            className={cn(
                config.className,
                sizeClasses,
                'font-medium',
                className
            )}
        >
            {config.label}
        </Badge>
    );
};

export default StatusBadge; 
"use client";

import React from 'react';
import Modal from 'react-modal';
import { useTranslation } from 'react-i18next';
import { ExclamationTriangleIcon } from '@heroicons/react/24/outline';

interface PermissionWarningModalProps {
    isOpen: boolean;
    onClose: () => void;
    onContinue: () => void;
    permissionName?: string;
}

// Custom styles for the Modal matching existing patterns
const customModalStyles = {
    overlay: {
        backgroundColor: 'rgba(0, 0, 0, 0.6)',
        backdropFilter: 'blur(4px)',
        zIndex: 150, // Higher than sidebar to ensure it appears on top
    },
    content: {
        top: '50%',
        left: '50%',
        right: 'auto',
        bottom: 'auto',
        marginRight: '-50%',
        transform: 'translate(-50%, -50%)',
        border: 'none',
        background: 'transparent',
        padding: '0',
        borderRadius: '0',
        width: '100%',
        maxWidth: '32rem',
        overflow: 'visible'
    },
};

const PermissionWarningModal: React.FC<PermissionWarningModalProps> = ({
    isOpen,
    onClose,
    onContinue,
    permissionName
}) => {
    const { t } = useTranslation();

    const handleContinue = () => {
        onContinue();
        onClose();
    };

    return (
        <Modal
            isOpen={isOpen}
            onRequestClose={onClose}
            style={customModalStyles}
            contentLabel={t('admin.permissions.warning.title')}
            shouldCloseOnOverlayClick={true}
            shouldCloseOnEsc={true}
        >
            <div className="bg-white p-6 sm:p-8 rounded-lg shadow-xl w-full relative">
                {/* Warning Icon */}
                <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-yellow-100 mb-4">
                    <ExclamationTriangleIcon className="h-10 w-10 text-yellow-600" />
                </div>

                {/* Title */}
                <h2 className="text-xl font-semibold text-gray-900 mb-4 text-center">
                    {t('admin.permissions.warning.title')}
                </h2>

                {/* Message */}
                <div className="text-center space-y-3 mb-6">
                    <p className="text-gray-700">
                        {t('admin.permissions.warning.message')}
                    </p>

                    {permissionName && (
                        <p className="text-sm text-gray-600 bg-gray-50 p-3 rounded-md">
                            <span className="font-medium">Permission:</span> {permissionName}
                        </p>
                    )}

                    <p className="text-sm text-blue-700 bg-blue-50 p-3 rounded-md">
                        {t('admin.permissions.warning.engineeringTeam')}
                    </p>

                    <p className="text-sm text-orange-700 bg-orange-50 p-3 rounded-md">
                        {t('admin.permissions.warning.futureAccess')}
                    </p>
                </div>

                {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row gap-3">
                    <button
                        onClick={onClose}
                        className="flex-1 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors duration-150"
                    >
                        {t('common.cancel')}
                    </button>
                    <button
                        onClick={handleContinue}
                        className="flex-1 px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 transition-colors duration-150"
                    >
                        {t('admin.permissions.warning.continueButton')}
                    </button>
                </div>
            </div>
        </Modal>
    );
};

export default PermissionWarningModal; 
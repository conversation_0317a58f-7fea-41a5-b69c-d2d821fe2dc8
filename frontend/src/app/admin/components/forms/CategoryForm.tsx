"use client";

import React, { useState, useEffect } from 'react';
import { Category } from '../../../types/category';
import DynamicHeroIcon from '../../../components/common/DynamicHeroIcon';
import IconPickerModal from '../IconPickerModal';
import { useTranslation } from 'react-i18next';

interface CategoryFormData {
    nameEn: string;
    nameTa: string;
    icon: string;
    background: string;
}

interface CategoryFormProps {
    category?: Category;
    onSubmit: (data: CategoryFormData) => Promise<void>;
    onCancel: () => void;
    isLoading?: boolean;
    submitLabel?: string;
}

const CategoryForm: React.FC<CategoryFormProps> = ({
    category,
    onSubmit,
    onCancel,
    isLoading = false,
    submitLabel = 'Save Category',
}) => {
    const { t } = useTranslation();
    // Helper function to sanitize color values
    const sanitizeColor = (color: string | undefined): string => {
        if (!color) return '';

        // Remove any non-hex characters and ensure it starts with #
        let sanitized = color.replace(/[^#0-9A-Fa-f]/g, '');
        if (!sanitized.startsWith('#')) {
            sanitized = '#' + sanitized;
        }
        // If it's an 8-character hex (with alpha), convert to 6-character
        if (sanitized.length === 9) {
            sanitized = sanitized.substring(0, 7);
        }
        // Ensure it's a valid 6-character hex color
        if (sanitized.length !== 7) {
            return '';
        }
        return sanitized;
    };

    const [formData, setFormData] = useState<CategoryFormData>({
        nameEn: category?.name.en || '',
        nameTa: category?.name.ta || '',
        icon: category?.icon || '',
        background: sanitizeColor(category?.background),
    });

    const [errors, setErrors] = useState<Partial<CategoryFormData>>({});
    const [isIconPickerOpen, setIsIconPickerOpen] = useState(false);

    // Update form data when category prop changes (for edit mode)
    useEffect(() => {
        if (category) {
            setFormData({
                nameEn: category.name.en || '',
                nameTa: category.name.ta || '',
                icon: category.icon || '',
                background: sanitizeColor(category.background),
            });
        }
    }, [category]);

    const validateForm = (): boolean => {
        const newErrors: Partial<CategoryFormData> = {};

        if (!formData.nameEn.trim()) {
            newErrors.nameEn = t('admin.components.categoryForm.errors.nameRequired');
        }

        if (!formData.icon.trim()) {
            newErrors.icon = t('admin.components.categoryForm.errors.iconRequired');
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!validateForm()) {
            return;
        }

        try {
            await onSubmit(formData);
        } catch (error) {
            console.error('Form submission error:', error);
        }
    };

    const handleInputChange = (field: keyof CategoryFormData, value: string) => {
        // Sanitize color values to ensure they're valid 6-character hex colors
        if (field === 'background' && value) {
            // Remove any non-hex characters and ensure it starts with #
            value = value.replace(/[^#0-9A-Fa-f]/g, '');
            if (!value.startsWith('#')) {
                value = '#' + value;
            }
            // If it's an 8-character hex (with alpha), convert to 6-character
            if (value.length === 9) {
                value = value.substring(0, 7);
            }
            // Ensure it's a valid 6-character hex color
            if (value.length !== 7) {
                value = '';
            }
        }

        setFormData(prev => ({ ...prev, [field]: value }));

        // Clear error when user starts typing
        if (errors[field]) {
            setErrors(prev => ({ ...prev, [field]: undefined }));
        }
    };

    const handleIconSelect = (iconName: string) => {
        handleInputChange('icon', iconName);
    };

    return (
        <form onSubmit={handleSubmit} className="space-y-6">
            {/* English Name */}
            <div>
                <label htmlFor="nameEn" className="block text-sm font-medium text-gray-700">
                    {t('admin.components.categoryForm.nameEnglishRequired')}
                </label>
                <input
                    type="text"
                    id="nameEn"
                    value={formData.nameEn}
                    onChange={(e) => handleInputChange('nameEn', e.target.value)}
                    className={`mt-1 block w-full px-3 py-3 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm ${errors.nameEn ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''
                        }`}
                    placeholder={t('admin.components.categoryForm.nameEnglishPlaceholder')}
                    disabled={isLoading}
                />
                {errors.nameEn && (
                    <p className="mt-1 text-sm text-red-600">{errors.nameEn}</p>
                )}
            </div>

            {/* Tamil Name */}
            <div>
                <label htmlFor="nameTa" className="block text-sm font-medium text-gray-700">
                    {t('admin.components.categoryForm.nameTamil')}
                </label>
                <input
                    type="text"
                    id="nameTa"
                    value={formData.nameTa}
                    onChange={(e) => handleInputChange('nameTa', e.target.value)}
                    className="mt-1 block w-full px-3 py-3 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                    placeholder={t('admin.components.categoryForm.nameTamilPlaceholder')}
                    disabled={isLoading}
                />
            </div>

            {/* Icon Selection */}
            <div>
                <label className="block text-sm font-medium text-gray-700">
                    {t('admin.components.categoryForm.iconRequired')}
                </label>
                <button
                    type="button"
                    onClick={() => setIsIconPickerOpen(true)}
                    className={`mt-1 w-full flex items-center justify-between px-3 py-3 border rounded-md shadow-sm bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${errors.icon ? 'border-red-300' : 'border-gray-300'
                        }`}
                    disabled={isLoading}
                >
                    <div className="flex items-center">
                        {formData.icon ? (
                            <>
                                <DynamicHeroIcon iconName={formData.icon} className="w-5 h-5 mr-2 text-gray-600" />
                                {/* <span className="text-sm text-gray-900">{formData.icon}</span> */}
                            </>
                        ) : (
                            <span className="text-sm text-gray-500">{t('admin.components.categoryForm.selectIcon')}</span>
                        )}
                    </div>
                    <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                </button>
                {errors.icon && (
                    <p className="mt-1 text-sm text-red-600">{errors.icon}</p>
                )}
                <p className="mt-1 text-sm text-gray-500">
                    {t('admin.components.categoryForm.iconHelpText')}
                </p>
            </div>

            {/* Background Color */}
            <div>
                <label htmlFor="background" className="block text-sm font-medium text-gray-700">
                    {t('admin.components.categoryForm.backgroundColorOptional')}
                </label>
                <div className="mt-1 flex items-center space-x-3">
                    {formData.background ? (
                        <input
                            type="color"
                            id="background"
                            value={formData.background}
                            onChange={(e) => handleInputChange('background', e.target.value)}
                            className="h-12 w-20 rounded-md border border-gray-300 cursor-pointer disabled:cursor-not-allowed"
                            disabled={isLoading}
                        />
                    ) : (
                        <div className="h-12 w-20 rounded-md border-2 border-dashed border-gray-300 bg-gray-50 flex items-center justify-center">
                            <span className="text-xs text-gray-400">{t('admin.components.categoryForm.noColor')}</span>
                        </div>
                    )}
                    <input
                        type="text"
                        value={formData.background}
                        onChange={(e) => handleInputChange('background', e.target.value)}
                        className="block w-32 px-3 py-3 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                        placeholder={t('admin.components.categoryForm.colorPlaceholder')}
                        disabled={isLoading}
                    />
                    {formData.background ? (
                        <button
                            type="button"
                            onClick={() => handleInputChange('background', '')}
                            className="px-3 py-2 text-xs font-medium text-gray-600 bg-gray-100 hover:bg-gray-200 rounded-md border border-gray-300"
                            disabled={isLoading}
                        >
                            {t('admin.components.categoryForm.clear')}
                        </button>
                    ) : (
                        <button
                            type="button"
                            onClick={() => handleInputChange('background', '#3B82F6')}
                            className="px-3 py-2 text-xs font-medium text-blue-600 bg-blue-50 hover:bg-blue-100 rounded-md border border-blue-300"
                            disabled={isLoading}
                        >
                            {t('admin.components.categoryForm.addColor')}
                        </button>
                    )}
                </div>
                <p className="mt-1 text-sm text-gray-500">
                    {t('admin.components.categoryForm.backgroundHelpText')}
                </p>
            </div>

            {/* Preview */}
            <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t('admin.components.categoryForm.preview')}
                </label>
                <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                    <div
                        className="inline-flex items-center px-4 py-2 rounded-lg font-medium text-black"
                        style={{
                            backgroundColor: formData.background || '#f3f4f6',
                            border: !formData.background ? '1px solid #d1d5db' : 'none'
                        }}
                    >
                        {formData.icon ? (
                            <DynamicHeroIcon iconName={formData.icon} className="w-5 h-5 mr-2 text-black" />
                        ) : (
                            <div className="w-5 h-5 mr-2 bg-gray-400 rounded flex items-center justify-center">
                                <span className="text-xs text-white">?</span>
                            </div>
                        )}
                        {formData.nameEn || t('admin.components.categoryForm.categoryName')}
                    </div>
                    {formData.nameTa && (
                        <div className="mt-2 text-sm text-gray-600">
                            {t('admin.components.categoryForm.tamil')} {formData.nameTa}
                        </div>
                    )}
                </div>
            </div>

            {/* Form Actions */}
            <div className="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
                <button
                    type="button"
                    onClick={onCancel}
                    className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    disabled={isLoading}
                >
                    {t('admin.components.categoryForm.cancel')}
                </button>
                <button
                    type="submit"
                    className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    disabled={isLoading}
                >
                    {isLoading ? (
                        <>
                            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            {t('admin.components.categoryForm.saving')}
                        </>
                    ) : (
                        submitLabel
                    )}
                </button>
            </div>

            {/* Icon Picker Modal */}
            <IconPickerModal
                isOpen={isIconPickerOpen}
                onClose={() => setIsIconPickerOpen(false)}
                onSelect={handleIconSelect}
                selectedIcon={formData.icon}
            />
        </form>
    );
};

export default CategoryForm; 
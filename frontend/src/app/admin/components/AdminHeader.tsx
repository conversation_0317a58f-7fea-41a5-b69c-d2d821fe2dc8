"use client";

import React from 'react';
import { useAuth } from '../../context/AuthContext';
import { useAdminPermissions } from '../../hooks/useAdminPermissions';
import { LanguageSwitcher } from '../../components/Header/LanguageSwitcher';
import { useTranslation } from 'react-i18next';

interface AdminHeaderProps {
    onMobileMenuToggle?: () => void;
}

const AdminHeader: React.FC<AdminHeaderProps> = ({ onMobileMenuToggle }) => {
    const { t } = useTranslation();
    const { userDetails, userLogout } = useAuth();
    const { getUserRoles, getUserPermissions } = useAdminPermissions();

    const handleLogout = () => {
        userLogout();
    };

    const userRoles = getUserRoles();
    const userPermissions = getUserPermissions();

    return (
        <header className="bg-white shadow-sm border-b border-gray-200 px-4 py-4 sm:px-6">
            <div className="flex items-center justify-between">
                {/* Mobile menu button and page title */}
                <div className="flex items-center space-x-4 flex-1">
                    {/* Mobile menu button */}
                    {onMobileMenuToggle && (
                        <button
                            onClick={onMobileMenuToggle}
                            className="md:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
                            aria-label={t('admin.components.adminHeader.toggleMenu')}
                        >
                            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                            </svg>
                        </button>
                    )}

                    {/* Page Title Area - Will be updated by individual pages */}
                    <div className="flex-1">
                        <h1 className="text-xl sm:text-2xl font-semibold text-gray-900">{t('admin.dashboard.title', 'Admin Dashboard')}</h1>
                    </div>
                </div>

                {/* User Info & Actions */}
                <div className="flex items-center space-x-2 sm:space-x-4">
                    {/* User Info */}
                    <div className="flex items-center space-x-2 sm:space-x-3">
                        <div className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full">
                            <span className="text-sm font-medium text-blue-600">
                                {userDetails?.name?.charAt(0)?.toUpperCase() || 'A'}
                            </span>
                        </div>
                        <div className="hidden sm:block">
                            <div className="text-sm font-medium text-gray-900">
                                {userDetails?.name || t('admin.user.defaultName', 'Admin User')}
                            </div>
                            <div className="text-xs text-gray-500">
                                {userRoles.length > 0 ? userRoles.join(', ') : t('admin.user.defaultRole', 'Admin')}
                            </div>
                        </div>
                    </div>

                    {/* Permissions Badge */}
                    {userPermissions.length > 0 && (
                        <div className="hidden lg:block">
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                {userPermissions.length} {t('admin.user.permission', { count: userPermissions.length })}
                            </span>
                        </div>
                    )}

                    {/* Language Switcher */}
                    <LanguageSwitcher />

                    {/* Logout Button */}
                    <button
                        onClick={handleLogout}
                        className="inline-flex items-center px-2 py-2 sm:px-3 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors min-h-[44px]"
                    >
                        <svg className="w-4 h-4 sm:mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                        </svg>
                        <span className="hidden sm:inline">{t('header.logout')}</span>
                    </button>
                </div>
            </div>
        </header>
    );
};

export default AdminHeader; 
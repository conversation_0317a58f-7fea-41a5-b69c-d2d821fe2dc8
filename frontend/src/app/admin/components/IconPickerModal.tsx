"use client";

import React, { useState, useMemo, useEffect } from 'react';
import Modal from 'react-modal';
import * as HIconsOutline from '@heroicons/react/24/outline';
import * as HIconsSolid from '@heroicons/react/24/solid';
import DynamicHeroIcon from '../../components/common/DynamicHeroIcon';
import { useTranslation } from 'react-i18next';
import DOMPurify from 'dompurify';

// Combine all icons
const allHeroIcons = {
    ...HIconsOutline,
    ...HIconsSolid,
};

// Get all icon names
const iconNames = Object.keys(allHeroIcons) as Array<keyof typeof allHeroIcons>;

// SVG validation constants
const MAX_SVG_SIZE = 10240; // 10KB

// DOMPurify configuration for SVGs (same as DynamicHeroIcon)
const svgSanitizeConfig = {
    USE_PROFILES: { svg: true, svgFilters: true },
    ALLOWED_TAGS: [
        'svg', 'path', 'circle', 'rect', 'g', 'polygon', 'polyline',
        'line', 'ellipse', 'text', 'defs', 'linearGradient',
        'radialGradient', 'stop', 'clipPath', 'mask'
    ],
    ALLOWED_ATTR: [
        'viewBox', 'width', 'height', 'd', 'cx', 'cy', 'r', 'x', 'y',
        'fill', 'stroke', 'stroke-width', 'transform', 'class', 'id',
        'opacity', 'fill-opacity', 'stroke-opacity', 'stroke-linecap',
        'stroke-linejoin', 'stroke-dasharray', 'stroke-dashoffset'
    ],
    FORBID_TAGS: ['script', 'iframe', 'object', 'embed', 'link', 'style'],
    FORBID_ATTR: ['onclick', 'onload', 'onerror', 'onmouseover', 'javascript', 'href']
};

interface IconPickerModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSelect: (iconName: string) => void;
    selectedIcon?: string;
}

type TabType = 'heroicons' | 'svg';

/**
 * Validates SVG content (same logic as DynamicHeroIcon but without caching)
 */
const validateSVG = (svgContent: string): { isValid: boolean; error?: string } => {
    try {
        if (!svgContent.trim()) {
            return { isValid: false, error: 'SVG content is empty' };
        }

        // Size check
        if (svgContent.length > MAX_SVG_SIZE) {
            return {
                isValid: false,
                error: `SVG too large (${Math.round(svgContent.length / 1024)}KB). Maximum size is 10KB.`
            };
        }

        // Basic structure check
        if (!svgContent.trim().startsWith('<svg') || !svgContent.trim().endsWith('</svg>')) {
            return {
                isValid: false,
                error: 'Invalid SVG structure. Must start with <svg and end with </svg>'
            };
        }

        // Sanitize with DOMPurify
        const sanitized = DOMPurify.sanitize(svgContent, svgSanitizeConfig);

        if (!sanitized || sanitized.trim().length === 0) {
            return {
                isValid: false,
                error: 'SVG content was removed during sanitization (may contain unsafe elements)'
            };
        }

        // Test if sanitized SVG can be parsed
        const parser = new DOMParser();
        const doc = parser.parseFromString(sanitized, 'image/svg+xml');
        const parserError = doc.querySelector('parsererror');

        if (parserError) {
            return {
                isValid: false,
                error: 'SVG contains syntax errors and cannot be parsed'
            };
        }

        return { isValid: true };

    } catch (error) {
        return {
            isValid: false,
            error: `SVG validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
        };
    }
};

/**
 * Debounce hook for SVG validation
 */
const useDebounce = (value: string, delay: number) => {
    const [debouncedValue, setDebouncedValue] = useState(value);

    useEffect(() => {
        const handler = setTimeout(() => {
            setDebouncedValue(value);
        }, delay);

        return () => {
            clearTimeout(handler);
        };
    }, [value, delay]);

    return debouncedValue;
};

/**
 * Recent SVGs localStorage management
 */
const RECENT_SVGS_KEY = 'recent_custom_svgs';
const MAX_RECENT_SVGS = 5;

const getRecentSVGs = (): string[] => {
    try {
        const stored = localStorage.getItem(RECENT_SVGS_KEY);
        return stored ? JSON.parse(stored) : [];
    } catch {
        return [];
    }
};

const addRecentSVG = (svgContent: string) => {
    try {
        const recent = getRecentSVGs();
        const updated = [svgContent, ...recent.filter(svg => svg !== svgContent)].slice(0, MAX_RECENT_SVGS);
        localStorage.setItem(RECENT_SVGS_KEY, JSON.stringify(updated));
    } catch {
        // Ignore localStorage errors
    }
};

const IconPickerModal: React.FC<IconPickerModalProps> = ({
    isOpen,
    onClose,
    onSelect,
    selectedIcon,
}) => {
    const { t } = useTranslation();
    const [activeTab, setActiveTab] = useState<TabType>('heroicons');
    const [searchTerm, setSearchTerm] = useState('');
    const [customSVG, setCustomSVG] = useState('');
    const [svgError, setSvgError] = useState<string | null>(null);
    const [recentSVGs, setRecentSVGs] = useState<string[]>([]);

    // Debounced SVG for validation (300ms delay)
    const debouncedSVG = useDebounce(customSVG, 300);

    // Determine if current selected icon is an SVG
    const isSelectedIconSVG = useMemo(() => {
        return selectedIcon && selectedIcon.startsWith('<svg');
    }, [selectedIcon]);

    // Set initial tab and SVG content based on selected icon
    useEffect(() => {
        if (isOpen) {
            if (isSelectedIconSVG) {
                setActiveTab('svg');
                setCustomSVG(selectedIcon || '');
            } else {
                setActiveTab('heroicons');
                setCustomSVG('');
            }
            setSearchTerm('');
            setSvgError(null);
            // Load recent SVGs
            setRecentSVGs(getRecentSVGs());
        }
    }, [isOpen, isSelectedIconSVG, selectedIcon]);

    // Validate SVG content when debounced value changes
    const svgValidation = useMemo(() => {
        if (!debouncedSVG.trim()) {
            setSvgError(null);
            return { isValid: false };
        }

        const result = validateSVG(debouncedSVG);
        setSvgError(result.error || null);
        return result;
    }, [debouncedSVG]);

    // Filter HeroIcons based on search term
    const filteredIcons = useMemo(() => {
        if (!searchTerm) return iconNames;
        return iconNames.filter(name =>
            name.toLowerCase().includes(searchTerm.toLowerCase())
        );
    }, [searchTerm]);

    const handleIconSelect = (iconName: string) => {
        onSelect(iconName);
        onClose();
    };

    const handleSVGSelect = () => {
        if (svgValidation.isValid && customSVG.trim()) {
            const trimmedSVG = customSVG.trim();
            addRecentSVG(trimmedSVG);
            onSelect(trimmedSVG);
            onClose();
        }
    };

    const handleClearSelection = () => {
        onSelect('');
        onClose();
    };

    const clearSVGInput = () => {
        setCustomSVG('');
        setSvgError(null);
    };

    // Calculate SVG size in KB
    const svgSizeKB = useMemo(() => {
        return (customSVG.length / 1024).toFixed(1);
    }, [customSVG]);

    return (
        <Modal
            isOpen={isOpen}
            onRequestClose={onClose}
            contentLabel={t('admin.components.iconPicker.title')}
            className="max-w-5xl mx-auto mt-8 bg-white rounded-lg shadow-xl outline-none"
            closeTimeoutMS={200}
            ariaHideApp={true}
        >
            {/* Header */}
            <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">
                        {t('admin.components.iconPicker.title')}
                    </h3>
                    <button
                        onClick={onClose}
                        className="bg-white rounded-md text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                        <span className="sr-only">{t('admin.components.iconPicker.close')}</span>
                        <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>

                {/* Tab Navigation */}
                <div className="flex border-b border-gray-200 mb-4">
                    <button
                        onClick={() => setActiveTab('heroicons')}
                        className={`px-4 py-2 text-sm font-medium border-b-2 ${activeTab === 'heroicons'
                            ? 'border-blue-500 text-blue-600'
                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                            }`}
                    >
                        {t('admin.components.iconPicker.heroIcons')} ({iconNames.length})
                    </button>
                    <button
                        onClick={() => setActiveTab('svg')}
                        className={`px-4 py-2 text-sm font-medium border-b-2 ${activeTab === 'svg'
                            ? 'border-blue-500 text-blue-600'
                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                            }`}
                    >
                        {t('admin.components.iconPicker.customSvg')}
                    </button>
                </div>

                {/* Current Selection Display */}
                {selectedIcon && (
                    <div className="mb-4 p-3 bg-blue-50 rounded-md">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center">
                                <DynamicHeroIcon iconName={selectedIcon} className="w-5 h-5 mr-2 text-blue-600" />
                                <div className="flex flex-col">
                                    <span className="text-sm font-medium text-blue-900">
                                        {t('admin.components.iconPicker.current')}: {isSelectedIconSVG ? t('admin.components.iconPicker.customSvg') : selectedIcon}
                                    </span>
                                    {isSelectedIconSVG && (
                                        <span className="text-xs text-blue-700">
                                            {Math.round((selectedIcon?.length || 0) / 1024 * 10) / 10}KB
                                        </span>
                                    )}
                                </div>
                            </div>
                            <button
                                type="button"
                                onClick={handleClearSelection}
                                className="text-xs text-blue-600 hover:text-blue-800 underline"
                            >
                                {t('admin.components.iconPicker.clear')}
                            </button>
                        </div>
                    </div>
                )}

                {/* HeroIcons Tab Content */}
                {activeTab === 'heroicons' && (
                    <>
                        {/* Search */}
                        <div className="mb-4">
                            <input
                                type="text"
                                placeholder={t('admin.components.iconPicker.searchPlaceholder')}
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                autoFocus
                            />
                        </div>

                        {/* Icons grid */}
                        <div className="max-h-96 overflow-y-auto">
                            <div className="grid grid-cols-6 sm:grid-cols-8 md:grid-cols-10 lg:grid-cols-12 xl:grid-cols-14 gap-2">
                                {filteredIcons.map((iconName) => {
                                    const IconComponent = allHeroIcons[iconName];
                                    const isSelected = selectedIcon === iconName && !isSelectedIconSVG;

                                    return (
                                        <button
                                            key={iconName}
                                            onClick={() => handleIconSelect(iconName)}
                                            className={`p-3 rounded-md border-2 transition-all hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${isSelected
                                                ? 'border-blue-500 bg-blue-50'
                                                : 'border-gray-200 hover:border-gray-300'
                                                }`}
                                            title={iconName}
                                        >
                                            <IconComponent className="w-6 h-6 text-gray-700" />
                                        </button>
                                    );
                                })}
                            </div>
                        </div>

                        {/* Results count */}
                        <div className="mt-4 text-sm text-gray-500 text-center">
                            {filteredIcons.length} {t('admin.components.iconPicker.iconsFound')}
                        </div>
                    </>
                )}

                {/* Custom SVG Tab Content */}
                {activeTab === 'svg' && (
                    <div className="space-y-4">
                        {/* Recent SVGs */}
                        {recentSVGs.length > 0 && (
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    {t('admin.components.iconPicker.recentCustomSvgs')}
                                </label>
                                <div className="flex flex-wrap gap-2">
                                    {recentSVGs.map((recentSVG, index) => (
                                        <button
                                            key={index}
                                            onClick={() => setCustomSVG(recentSVG)}
                                            className="p-2 border border-gray-200 rounded-md hover:border-gray-300 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
                                            title={`Recent SVG ${index + 1} (${Math.round(recentSVG.length / 1024 * 10) / 10}KB)`}
                                        >
                                            <DynamicHeroIcon iconName={recentSVG} className="w-5 h-5 text-gray-600" />
                                        </button>
                                    ))}
                                </div>
                            </div>
                        )}

                        {/* SVG Input */}
                        <div>
                            <div className="flex items-center justify-between mb-2">
                                <label className="block text-sm font-medium text-gray-700">
                                    {t('admin.components.iconPicker.svgCode')}
                                </label>
                                <div className="flex items-center space-x-2 text-xs text-gray-500">
                                    <span>{svgSizeKB}KB / 10KB</span>
                                    {customSVG && (
                                        <button
                                            type="button"
                                            onClick={clearSVGInput}
                                            className="text-red-600 hover:text-red-800 underline"
                                        >
                                            {t('admin.components.iconPicker.clear')}
                                        </button>
                                    )}
                                </div>
                            </div>
                            <textarea
                                value={customSVG}
                                onChange={(e) => setCustomSVG(e.target.value)}
                                placeholder="Paste your SVG code here...&#10;&#10;Example:&#10;<svg viewBox='0 0 24 24' fill='none'>&#10;  <path d='M12 2L2 7h10v10h10V7L12 2z' stroke='currentColor' stroke-width='2'/>&#10;</svg>"
                                className="w-full h-40 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm font-mono"
                                spellCheck={false}
                            />
                            {svgError && (
                                <p className="mt-1 text-sm text-red-600">{svgError}</p>
                            )}
                        </div>

                        {/* SVG Preview */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                {t('admin.components.iconPicker.preview')}
                            </label>
                            <div className="flex items-center justify-center h-24 border-2 border-dashed border-gray-300 rounded-md bg-gray-50">
                                {customSVG.trim() && svgValidation.isValid ? (
                                    <div className="flex items-center space-x-4">
                                        <DynamicHeroIcon iconName={customSVG} className="w-8 h-8 text-gray-700" />
                                        <DynamicHeroIcon iconName={customSVG} className="w-6 h-6 text-blue-600" />
                                        <DynamicHeroIcon iconName={customSVG} className="w-4 h-4 text-green-600" />
                                    </div>
                                ) : (
                                    <span className="text-gray-400 text-sm">
                                        {customSVG.trim() ? t('admin.components.iconPicker.invalidSvg') : t('admin.components.iconPicker.svgPreviewText')}
                                    </span>
                                )}
                            </div>
                        </div>

                        {/* SVG Actions */}
                        <div className="flex justify-end">
                            <button
                                type="button"
                                onClick={handleSVGSelect}
                                disabled={!svgValidation.isValid || !customSVG.trim()}
                                className="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                {t('admin.components.iconPicker.useThisSvg')}
                            </button>
                        </div>
                    </div>
                )}
            </div>

            {/* Footer */}
            <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                    type="button"
                    onClick={onClose}
                    className="w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
                >
                    {t('admin.components.iconPicker.cancel')}
                </button>
            </div>
        </Modal>
    );
};

export default IconPickerModal; 
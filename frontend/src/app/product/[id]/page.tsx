'use client';

import { useParams, notFound } from 'next/navigation';
import { useQuery } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';
import { SKU } from '../../types/sku';
import ProductImageGallery from '../../components/ProductDetail/ProductImageGallery';
import ProductInformation from '../../components/ProductDetail/ProductInformation';
import Breadcrumbs from '../../components/Breadcrumbs';
import { getSkuById } from '../../services/skuService';
import type { LocalizedName } from '@/app/types/common';

export default function ProductPage() {
    const params = useParams();
    const productId = parseInt(params.id as string, 10);
    const { t, i18n } = useTranslation();
    const currentLanguage = i18n.language as keyof LocalizedName;

    const {
        data: sku,
        isLoading: loading,
        isError: error
    } = useQuery<SKU | null, Error>({
        queryKey: ['product', productId],
        queryFn: () => {
            if (isNaN(productId)) {
                throw new Error('Invalid product ID');
            }
            return getSkuById(productId);
        },
        enabled: !isNaN(productId),
        retry: false
    });

    // Prepare breadcrumbs
    const breadcrumbItems = [
        { name: t('breadcrumbs.home', 'Home'), href: '/' }
    ];

    // Add product name to breadcrumbs if available
    if (sku) {
        const productDisplayName = sku.name?.[currentLanguage] || sku.name?.en || t('common.unnamedProduct', 'Unnamed Product');
        breadcrumbItems.push({ name: productDisplayName, href: `/product/${productId}` });
    }

    if (loading) {
        return (
            <div className="container mx-auto px-4 py-8">
                {/* Show breadcrumbs with loading state */}
                <div className="mb-6">
                    <Breadcrumbs items={[
                        { name: t('breadcrumbs.home', 'Home'), href: '/' },
                        { name: t('product.page.loading', 'Loading...'), href: `/product/${productId}` }
                    ]} />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8 lg:gap-12">
                    <div className="bg-gray-200 animate-pulse rounded-lg h-96"></div>
                    <div className="space-y-4">
                        <div className="bg-gray-200 animate-pulse rounded h-8 w-3/4"></div>
                        <div className="bg-gray-200 animate-pulse rounded h-4 w-1/2"></div>
                        <div className="bg-gray-200 animate-pulse rounded h-6 w-1/4"></div>
                        <div className="bg-gray-200 animate-pulse rounded h-10 w-full"></div>
                    </div>
                </div>
            </div>
        );
    }

    if (error || !sku || isNaN(productId)) {
        notFound();
    }

    return (
        <div className="container mx-auto px-4 py-8">
            <Breadcrumbs items={breadcrumbItems} />
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 lg:gap-12 mt-6 mb-20">
                <div>
                    <ProductImageGallery
                        productName={sku.name.en}
                        primaryImageUrl={sku.imageUrl}
                        additionalImages={sku.images}
                    />
                </div>
                <div>
                    <ProductInformation sku={sku} />
                </div>
            </div>
        </div>
    );
}
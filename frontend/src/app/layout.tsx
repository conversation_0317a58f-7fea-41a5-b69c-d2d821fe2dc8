
import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "@/app/globals.css";
import { ClientProviders } from "@/app/ClientProviders";

// Font setup (server component)
const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

// Metadata must be in a server component
export const metadata: Metadata = {
  title: "WoW Online",
  description: "2500+ products @ Wholesale Rates",
};

// Root layout (server component)
export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className='antialiased bg-gray-50 text-gray-800'>
        <ClientProviders>
          {children}
        </ClientProviders>
      </body>
    </html>
  );
}

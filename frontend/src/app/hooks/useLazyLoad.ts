import { useEffect } from 'react';
import { useIntersectionObserver } from './useIntersectionObserver';

interface UseLazyLoadOptions {
    threshold?: number;
    rootMargin?: string;
    enabled?: boolean;
}

export function useLazyLoad(
    onIntersect: () => void,
    options: UseLazyLoadOptions = {}
) {
    const {
        threshold = 0.1,
        rootMargin = '100px',
        enabled = true
    } = options;

    const { targetRef, isIntersecting, hasTriggered } = useIntersectionObserver({
        threshold,
        rootMargin,
        triggerOnce: true
    });

    useEffect(() => {
        if (enabled && isIntersecting) {
            onIntersect();
        }
    }, [enabled, isIntersecting, onIntersect]);

    return {
        targetRef,
        isIntersecting,
        hasTriggered
    };
} 
'use client';

import { useState, useCallback } from 'react';
import { useSession } from '../context/SessionContext';

interface UseSessionCheckReturn {
    checkSessionAndExecute: (callback: () => void) => void;
    showSessionModal: boolean;
    setShowSessionModal: (show: boolean) => void;
    handleSessionCreated: (sessionData: { customerName: string; customerPhone?: string; location?: { lat: number; lng: number } }) => void;
}

export function useSessionCheck(): UseSessionCheckReturn {
    const { hasActiveCustomerSession, startOrActivateCustomerSession, activeSessionId, sessions } = useSession();
    const [showSessionModal, setShowSessionModal] = useState(false);
    const [pendingCallback, setPendingCallback] = useState<(() => void) | null>(null);

    const checkSessionAndExecute = useCallback((callback: () => void) => {

        if (!hasActiveCustomerSession) {
            // Store the callback to execute after session creation
            setPendingCallback(() => callback);
            setShowSessionModal(true);
            return;
        }
        // Execute immediately if session exists
        callback();
    }, [hasActiveCustomerSession, activeSessionId, sessions]);

    const handleSessionCreated = useCallback((sessionData: { customerName: string; customerPhone?: string; location?: { lat: number; lng: number } }) => {
        // Create the session
        startOrActivateCustomerSession(sessionData);

        // Close the modal
        setShowSessionModal(false);

        // Execute the pending callback after a short delay to ensure session is created
        if (pendingCallback) {
            setTimeout(() => {
                pendingCallback();
                setPendingCallback(null);
            }, 100);
        } else {
            console.log('useSessionCheck: No pending callback to execute');
        }
    }, [startOrActivateCustomerSession, pendingCallback]);

    const handleModalClose = useCallback(() => {
        setShowSessionModal(false);
        setPendingCallback(null);
    }, []);

    return {
        checkSessionAndExecute,
        showSessionModal,
        setShowSessionModal: handleModalClose,
        handleSessionCreated
    };
} 
import { useEffect, useRef, useState } from 'react';

interface UseIntersectionObserverOptions {
    threshold?: number;
    rootMargin?: string;
    triggerOnce?: boolean;
}

export function useIntersectionObserver(
    options: UseIntersectionObserverOptions = {}
) {
    const {
        threshold = 0.1,
        rootMargin = '100px',
        triggerOnce = true
    } = options;

    const [isIntersecting, setIsIntersecting] = useState(false);
    const [hasTriggered, setHasTriggered] = useState(false);
    const targetRef = useRef<HTMLElement>(null);

    useEffect(() => {
        const target = targetRef.current;
        if (!target) return;

        // If triggerOnce is true and we've already triggered, don't observe
        if (triggerOnce && hasTriggered) return;

        const observer = new IntersectionObserver(
            ([entry]) => {
                const isCurrentlyIntersecting = entry.isIntersecting;
                setIsIntersecting(isCurrentlyIntersecting);

                if (isCurrentlyIntersecting && triggerOnce) {
                    setHasTriggered(true);
                }
            },
            {
                threshold,
                rootMargin
            }
        );

        observer.observe(target);

        return () => {
            observer.unobserve(target);
        };
    }, [threshold, rootMargin, triggerOnce, hasTriggered]);

    return {
        targetRef,
        isIntersecting: triggerOnce ? (hasTriggered || isIntersecting) : isIntersecting,
        hasTriggered
    };
} 
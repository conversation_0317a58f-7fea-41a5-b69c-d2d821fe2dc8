import { useCallback } from 'react';
import { useAuth } from '../context/AuthContext';
import { AdminPermissions } from '../types/auth';
import { logger } from '@/lib/logger';

/**
 * Custom hook for admin permissions and roles management
 * Provides easy access to permission checking and admin roles functionality
 */
export const useAdminPermissions = () => {
    const {
        adminRoles,
        isLoadingAdminRoles,
        adminRolesChecked,
        loadAdminRoles,
        hasPermission,
        hasRole,
        clearAdminRoles
    } = useAuth();

    /**
     * Check if user has admin access (has any admin role)
     */
    const isAdmin = (): boolean => {
        return (adminRoles?.roleNames?.length ?? 0) > 0;
    };

    /**
     * Check if user has specific admin role
     */
    const checkRole = (roleName: string): boolean => {
        return hasRole(roleName);
    };

    /**
     * Check if user has specific permission
     */
    const checkPermission = (permission: keyof AdminPermissions): boolean => {
        if (isAdmin() && permission !== 'systemAdmin') {
            return true;
        }

        return hasPermission(permission);
    };

    /**
     * Check if user has specific permission without admin bypass
     * This function strictly checks permissions regardless of admin status
     */
    const checkPermissionStrict = (permission: keyof AdminPermissions): boolean => {
        const hasStrictPermission = hasPermission(permission);

        // Log permission access attempts
        logger.info('Permission access attempt', {
            permission,
            hasPermission: hasStrictPermission,
            isAdmin: isAdmin(),
            userRoles: getUserRoles(),
            userId: adminRoles?.roleNames?.[0] || 'unknown'
        });

        return hasStrictPermission;
    };

    /**
     * Check multiple permissions (user must have ALL permissions)
     */
    const hasAllPermissions = (permissions: (keyof AdminPermissions)[]): boolean => {
        return permissions.every(permission => hasPermission(permission));
    };

    /**
     * Check multiple permissions (user must have ANY of the permissions)
     */
    const hasAnyPermission = (permissions: (keyof AdminPermissions)[]): boolean => {
        return permissions.some(permission => hasPermission(permission));
    };

    /**
     * Get all user's permissions as an array of strings
     */
    const getUserPermissions = (): string[] => {
        if (!adminRoles?.permissions) return [];

        return Object.entries(adminRoles.permissions)
            .filter(([, value]) => value === true)
            .map(([key]) => key);
    };

    /**
     * Get all user's roles as an array of strings
     */
    const getUserRoles = (): string[] => {
        return adminRoles?.roleNames || [];
    };

    /**
     * Force load admin roles regardless of current admin status
     * Useful as a fallback mechanism, but only if we haven't already checked
     */
    const forceLoadAdminRoles = useCallback(async (): Promise<boolean> => {
        if (adminRolesChecked) {
            logger.debug('[useAdminPermissions] Admin roles already checked, skipping force load');
            return adminRoles !== null;
        }

        logger.debug('[useAdminPermissions] Force loading admin roles...');
        return await loadAdminRoles();
    }, [adminRolesChecked, adminRoles, loadAdminRoles]);

    return {
        // State
        adminRoles,
        isLoadingAdminRoles,
        adminRolesChecked,

        // Actions
        loadAdminRoles,
        forceLoadAdminRoles,
        clearAdminRoles,

        // Permission checks
        isAdmin,
        checkRole,
        checkPermission,
        checkPermissionStrict,
        hasAllPermissions,
        hasAnyPermission,

        // Data getters
        getUserPermissions,
        getUserRoles,
    };
}; 
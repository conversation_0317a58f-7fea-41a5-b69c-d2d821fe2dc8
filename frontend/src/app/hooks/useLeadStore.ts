import { create } from 'zustand';
import { Lead } from '../types/lead';

interface LeadStore {
    // UI State
    selectedLeads: Lead[];
    viewMode: 'table' | 'cards';
    isExporting: boolean;

    // Actions
    setSelectedLeads: (leads: Lead[]) => void;
    addSelectedLead: (lead: Lead) => void;
    removeSelectedLead: (leadId: number) => void;
    clearSelectedLeads: () => void;
    toggleLeadSelection: (lead: Lead) => void;
    setViewMode: (mode: 'table' | 'cards') => void;
    setIsExporting: (isExporting: boolean) => void;

    // Bulk operations
    selectAllLeads: (leads: Lead[]) => void;
    isLeadSelected: (leadId: number) => boolean;
}

export const useLeadStore = create<LeadStore>((set, get) => ({
    // Initial state
    selectedLeads: [],
    viewMode: 'table',
    isExporting: false,

    // Actions
    setSelectedLeads: (leads: Lead[]) => {
        set({ selectedLeads: leads });
    },

    addSelectedLead: (lead: Lead) => {
        const { selectedLeads } = get();
        const isAlreadySelected = selectedLeads.some(selected => selected.id === lead.id);

        if (!isAlreadySelected) {
            set({ selectedLeads: [...selectedLeads, lead] });
        }
    },

    removeSelectedLead: (leadId: number) => {
        const { selectedLeads } = get();
        set({
            selectedLeads: selectedLeads.filter(lead => lead.id !== leadId)
        });
    },

    clearSelectedLeads: () => {
        set({ selectedLeads: [] });
    },

    toggleLeadSelection: (lead: Lead) => {
        const { selectedLeads } = get();
        const isSelected = selectedLeads.some(selected => selected.id === lead.id);

        if (isSelected) {
            set({
                selectedLeads: selectedLeads.filter(selected => selected.id !== lead.id)
            });
        } else {
            set({
                selectedLeads: [...selectedLeads, lead]
            });
        }
    },

    setViewMode: (mode: 'table' | 'cards') => {
        set({ viewMode: mode });
    },

    setIsExporting: (isExporting: boolean) => {
        set({ isExporting });
    },

    // Bulk operations
    selectAllLeads: (leads: Lead[]) => {
        set({ selectedLeads: [...leads] });
    },

    isLeadSelected: (leadId: number) => {
        const { selectedLeads } = get();
        return selectedLeads.some(lead => lead.id === leadId);
    },
})); 
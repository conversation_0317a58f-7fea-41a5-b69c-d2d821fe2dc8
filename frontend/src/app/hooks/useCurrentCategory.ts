import { usePathname } from 'next/navigation';
import { useMemo } from 'react';

/**
 * Custom hook to extract the current category ID from the pathname
 * Returns the category ID if on a category page, null otherwise
 */
export function useCurrentCategory(): string | null {
    const pathname = usePathname();

    const currentCategoryId = useMemo(() => {
        // Check if we're on a category page: /categories/[categoryId]
        const categoryMatch = pathname.match(/^\/categories\/([^\/]+)$/);
        return categoryMatch ? categoryMatch[1] : null;
    }, [pathname]);

    return currentCategoryId;
} 
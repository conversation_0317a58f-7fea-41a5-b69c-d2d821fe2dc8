import { useSearch<PERSON><PERSON><PERSON>, useRouter, usePathname } from 'next/navigation';
import { useCallback, useMemo } from 'react';
import { OrderFilters } from '../types/order';
import { getDefaultDateRange } from '../services/orderService';
import { logger } from '@/lib/logger';

/**
 * Convert YYYY-MM-DD to dd-MM-yyyy for URL display
 */
const formatDateForURL = (apiDate: string): string => {
    if (!apiDate) return '';
    const [year, month, day] = apiDate.split('-');
    return `${day}-${month}-${year}`;
};

/**
 * Convert dd-MM-yyyy from URL to YYYY-MM-DD for API
 * Also handles YYYY-MM-DD format for backward compatibility
 */
const parseDateFromURL = (urlDate: string): string => {
    if (!urlDate) return '';

    const parts = urlDate.split('-');
    if (parts.length !== 3) return '';

    // Check if it's already in YYYY-MM-DD format (year is 4 digits and first)
    if (parts[0].length === 4) {
        // Already in YYYY-MM-DD format, return as is
        return urlDate;
    }

    // Convert from dd-MM-yyyy to YYYY-MM-DD
    if (parts[2].length === 4) {
        const [day, month, year] = parts;
        return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
    }

    // Invalid format
    return '';
};

interface UseOrderFiltersReturn {
    filters: OrderFilters;
    updateFilter: (key: keyof OrderFilters, value: string) => void;
    resetFilters: () => void;
    clearStatusAndFacility: () => void;
}

/**
 * Hook to manage order filters with URL query parameter synchronization
 * URL parameters take priority over default values
 */
export const useOrderFilters = (): UseOrderFiltersReturn => {
    const searchParams = useSearchParams();
    const router = useRouter();
    const pathname = usePathname();

    // Get default date range for fallback
    const defaultDateRange = useMemo(() => getDefaultDateRange(), []);

    // Parse filters from URL with fallbacks to defaults
    const filters = useMemo((): OrderFilters => {
        // Parse dates from URL (supporting both dd-MM-yyyy and YYYY-MM-DD formats)
        const urlDateFrom = searchParams.get('dateFrom');
        const urlDateTo = searchParams.get('dateTo');

        const dateFrom = urlDateFrom ? parseDateFromURL(urlDateFrom) : defaultDateRange.dateFrom;
        const dateTo = urlDateTo ? parseDateFromURL(urlDateTo) : defaultDateRange.dateTo;
        const status = searchParams.get('status') || '';
        const facilityKey = searchParams.get('facilityKey') || '';
        const mobile = searchParams.get('mobile') || '';

        logger.debug('OrderFilters: Parsed from URL', {
            dateFrom,
            dateTo,
            status,
            facilityKey,
            mobile,
            fromURL: {
                dateFrom: urlDateFrom,
                dateTo: urlDateTo,
                status: searchParams.get('status'),
                facilityKey: searchParams.get('facilityKey'),
                mobile: searchParams.get('mobile')
            }
        });

        return {
            dateFrom,
            dateTo,
            status,
            facilityKey,
            mobile
        };
    }, [searchParams, defaultDateRange]);

    /**
     * Update URL with new filter values
     */
    const updateURL = useCallback((newFilters: OrderFilters) => {
        const params = new URLSearchParams();

        // Only add non-empty parameters to keep URL clean
        // Format dates as dd-MM-yyyy for URL display
        if (newFilters.dateFrom) params.set('dateFrom', formatDateForURL(newFilters.dateFrom));
        if (newFilters.dateTo) params.set('dateTo', formatDateForURL(newFilters.dateTo));
        if (newFilters.status) params.set('status', newFilters.status);
        if (newFilters.facilityKey) params.set('facilityKey', newFilters.facilityKey);
        if (newFilters.mobile) params.set('mobile', newFilters.mobile);

        const newURL = `${pathname}?${params.toString()}`;

        logger.debug('OrderFilters: Updating URL', { newFilters, newURL });

        // Use replace to avoid polluting browser history
        router.replace(newURL);
    }, [pathname, router]);

    /**
     * Update a single filter value
     */
    const updateFilter = useCallback((key: keyof OrderFilters, value: string) => {
        const newFilters = { ...filters, [key]: value };
        updateURL(newFilters);
    }, [filters, updateURL]);

    /**
     * Reset all filters to defaults
     */
    const resetFilters = useCallback(() => {
        const newFilters: OrderFilters = {
            dateFrom: defaultDateRange.dateFrom,
            dateTo: defaultDateRange.dateTo,
            status: '',
            facilityKey: ''
        };
        updateURL(newFilters);
    }, [defaultDateRange, updateURL]);

    /**
     * Clear only status, facility, and mobile filters, keep date range
     */
    const clearStatusAndFacility = useCallback(() => {
        const newFilters = {
            ...filters,
            status: '',
            facilityKey: '',
            mobile: ''
        };
        updateURL(newFilters);
    }, [filters, updateURL]);

    return {
        filters,
        updateFilter,
        resetFilters,
        clearStatusAndFacility
    };
}; 
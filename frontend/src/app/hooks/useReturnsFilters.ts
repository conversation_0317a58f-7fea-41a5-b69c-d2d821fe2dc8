import { useSearch<PERSON>ara<PERSON>, useRouter, usePathname } from 'next/navigation';
import { useCallback, useMemo } from 'react';
import { getTodayDateRange } from '../services/orderService';
import { logger } from '@/lib/logger';

/**
 * Convert YYYY-MM-DD to dd-MM-yyyy for URL display
 */
const formatDateForURL = (apiDate: string): string => {
    if (!apiDate) return '';
    const [year, month, day] = apiDate.split('-');
    return `${day}-${month}-${year}`;
};

/**
 * Convert dd-MM-yyyy from URL to YYYY-MM-DD for API
 * Also handles YYYY-MM-DD format for backward compatibility
 */
const parseDateFromURL = (urlDate: string): string => {
    if (!urlDate) return '';

    // If already in YYYY-MM-DD format, return as-is
    if (urlDate.match(/^\d{4}-\d{2}-\d{2}$/)) {
        return urlDate;
    }

    // Convert dd-MM-yyyy to YYYY-MM-DD
    if (urlDate.match(/^\d{2}-\d{2}-\d{4}$/)) {
        const [day, month, year] = urlDate.split('-');
        return `${year}-${month}-${day}`;
    }

    return '';
};

interface UseReturnsFiltersReturn {
    dateFrom: string;
    dateTo: string;
    updateDateFilter: (field: 'dateFrom' | 'dateTo', value: string) => void;
    resetDateFilters: () => void;
}

/**
 * Hook to manage returns date filters with URL query parameter synchronization
 * Simplified version focused only on date range filtering for returns workflow
 * Uses today-only date range as default (getTodayDateRange)
 * URL parameters take priority over default values
 */
export const useReturnsFilters = (): UseReturnsFiltersReturn => {
    const searchParams = useSearchParams();
    const router = useRouter();
    const pathname = usePathname();

    // Get default date range for fallback (today only)
    const defaultDateRange = useMemo(() => getTodayDateRange(), []);

    // Parse date filters from URL with fallbacks to defaults
    const { dateFrom, dateTo } = useMemo(() => {
        const urlDateFrom = searchParams.get('dateFrom');
        const urlDateTo = searchParams.get('dateTo');

        const dateFrom = urlDateFrom ? parseDateFromURL(urlDateFrom) : defaultDateRange.dateFrom;
        const dateTo = urlDateTo ? parseDateFromURL(urlDateTo) : defaultDateRange.dateTo;

        logger.debug('ReturnsFilters: Parsed from URL', {
            dateFrom,
            dateTo,
            fromURL: {
                dateFrom: urlDateFrom,
                dateTo: urlDateTo
            }
        });

        return { dateFrom, dateTo };
    }, [searchParams, defaultDateRange]);

    /**
     * Update URL with new date filter values
     */
    const updateURL = useCallback((newDateFrom: string, newDateTo: string) => {
        const params = new URLSearchParams();

        // Only add non-empty parameters to keep URL clean
        // Format dates as dd-MM-yyyy for URL display
        if (newDateFrom) params.set('dateFrom', formatDateForURL(newDateFrom));
        if (newDateTo) params.set('dateTo', formatDateForURL(newDateTo));

        const newURL = `${pathname}?${params.toString()}`;

        logger.debug('ReturnsFilters: Updating URL', {
            newDateFrom,
            newDateTo,
            newURL
        });

        // Use replace to avoid polluting browser history
        router.replace(newURL);
    }, [pathname, router]);

    /**
     * Update a single date filter value
     */
    const updateDateFilter = useCallback((field: 'dateFrom' | 'dateTo', value: string) => {
        const newDateFrom = field === 'dateFrom' ? value : dateFrom;
        const newDateTo = field === 'dateTo' ? value : dateTo;

        updateURL(newDateFrom, newDateTo);
    }, [dateFrom, dateTo, updateURL]);

    /**
     * Reset date filters to defaults (today only)
     */
    const resetDateFilters = useCallback(() => {
        updateURL(defaultDateRange.dateFrom, defaultDateRange.dateTo);
    }, [defaultDateRange, updateURL]);

    return {
        dateFrom,
        dateTo,
        updateDateFilter,
        resetDateFilters
    };
}; 
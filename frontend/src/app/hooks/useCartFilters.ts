import { useSearch<PERSON>ara<PERSON>, useRouter, usePathname } from 'next/navigation';
import { useCallback, useMemo } from 'react';
import { AdminCartFilters } from '../types/session';
import { getDefaultCartDateRange } from '../services/cartService';
import { logger } from '@/lib/logger';

interface UseCartFiltersReturn {
    filters: AdminCartFilters;
    currentPage: number;
    limit: number;
    updateFilter: (key: keyof AdminCartFilters, value: string | number) => void;
    updatePage: (page: number) => void;
    resetFilters: () => void;
    clearValueFilters: () => void;
    getServerFilters: () => {
        status?: string;
        dateFrom?: string; // YYYY-MM-DD format
        dateTo?: string; // YYYY-MM-DD format
    };
    getClientFilters: () => {
        customerSearch: string;
        minValue: number;
        maxValue: number;
    };
}

/**
 * Convert YYYY-MM-DD to dd-MM-yyyy for URL display
 */
const formatDateForURL = (apiDate: string): string => {
    if (!apiDate) return '';
    const [year, month, day] = apiDate.split('-');
    return `${day}-${month}-${year}`;
};

/**
 * Convert dd-MM-yyyy from URL to YYYY-MM-DD for API
 * Also handles YYYY-MM-DD format for backward compatibility
 */
const parseDateFromURL = (urlDate: string): string => {
    if (!urlDate) return '';

    // If already in YYYY-MM-DD format, return as-is
    if (urlDate.match(/^\d{4}-\d{2}-\d{2}$/)) {
        return urlDate;
    }

    // Convert dd-MM-yyyy to YYYY-MM-DD
    if (urlDate.match(/^\d{2}-\d{2}-\d{4}$/)) {
        const [day, month, year] = urlDate.split('-');
        return `${year}-${month}-${day}`;
    }

    return '';
};

/**
 * Hook to manage cart filters with URL query parameter synchronization
 * URL parameters take priority over default values
 */
export const useCartFilters = (): UseCartFiltersReturn => {
    const searchParams = useSearchParams();
    const router = useRouter();
    const pathname = usePathname();

    // Get default values
    const defaultDateRange = useMemo(() => getDefaultCartDateRange(), []);
    const defaultLimit = 20;

    // Parse filters from URL with fallbacks to defaults
    const filters = useMemo((): AdminCartFilters => {
        // URL parameters take priority, fallback to defaults
        const dateFromURL = searchParams.get('dateFrom');
        const dateToURL = searchParams.get('dateTo');

        const dateFrom = dateFromURL ? parseDateFromURL(dateFromURL) : defaultDateRange.dateFrom;
        const dateTo = dateToURL ? parseDateFromURL(dateToURL) : defaultDateRange.dateTo;
        const status = searchParams.get('status') || '';
        const customerSearch = searchParams.get('customerSearch') || '';
        const minValue = parseInt(searchParams.get('minValue') || '0') || 0;
        const maxValue = parseInt(searchParams.get('maxValue') || '0') || 0;

        logger.debug('CartFilters: Parsed from URL', {
            dateFrom,
            dateTo,
            status,
            customerSearch,
            minValue,
            maxValue,
            fromURL: {
                dateFrom: searchParams.get('dateFrom'),
                dateTo: searchParams.get('dateTo'),
                status: searchParams.get('status'),
                customerSearch: searchParams.get('customerSearch'),
                minValue: searchParams.get('minValue'),
                maxValue: searchParams.get('maxValue')
            }
        });

        return {
            dateFrom,
            dateTo,
            status,
            customerSearch,
            minValue,
            maxValue
        };
    }, [searchParams, defaultDateRange]);

    // Parse pagination from URL
    const currentPage = useMemo(() => {
        const pageParam = searchParams.get('page');
        return pageParam ? Math.max(1, parseInt(pageParam)) : 1;
    }, [searchParams]);

    const limit = useMemo(() => {
        const limitParam = searchParams.get('limit');
        return limitParam ? Math.max(1, parseInt(limitParam)) : defaultLimit;
    }, [searchParams, defaultLimit]);

    /**
     * Update URL with new filter values and pagination
     */
    const updateURL = useCallback((newFilters: AdminCartFilters, newPage?: number, newLimit?: number) => {
        const params = new URLSearchParams();

        // Add filters - only non-empty/non-zero values to keep URL clean
        if (newFilters.dateFrom) params.set('dateFrom', formatDateForURL(newFilters.dateFrom));
        if (newFilters.dateTo) params.set('dateTo', formatDateForURL(newFilters.dateTo));
        if (newFilters.status) params.set('status', newFilters.status);
        if (newFilters.customerSearch) params.set('customerSearch', newFilters.customerSearch);
        if (newFilters.minValue > 0) params.set('minValue', newFilters.minValue.toString());
        if (newFilters.maxValue > 0) params.set('maxValue', newFilters.maxValue.toString());

        // Add pagination
        const finalPage = newPage ?? currentPage;
        const finalLimit = newLimit ?? limit;
        if (finalPage > 1) params.set('page', finalPage.toString());
        if (finalLimit !== defaultLimit) params.set('limit', finalLimit.toString());

        const newURL = `${pathname}?${params.toString()}`;

        logger.debug('CartFilters: Updating URL', { newFilters, newPage, newLimit, newURL });

        // Use replace to avoid polluting browser history
        router.replace(newURL);
    }, [pathname, router, currentPage, limit, defaultLimit]);

    /**
     * Update a single filter field
     */
    const updateFilter = useCallback((key: keyof AdminCartFilters, value: string | number) => {
        const newFilters = { ...filters, [key]: value };

        // Reset to page 1 when filters change
        updateURL(newFilters, 1);
    }, [filters, updateURL]);

    /**
     * Update pagination without changing filters
     */
    const updatePage = useCallback((page: number) => {
        updateURL(filters, page);
    }, [filters, updateURL]);

    /**
     * Reset all filters to defaults
     */
    const resetFilters = useCallback(() => {
        const defaultFilters: AdminCartFilters = {
            status: '',
            dateFrom: defaultDateRange.dateFrom,
            dateTo: defaultDateRange.dateTo,
            customerSearch: '',
            minValue: 0,
            maxValue: 0
        };

        updateURL(defaultFilters, 1);
    }, [defaultDateRange, updateURL]);

    /**
     * Clear only value-based filters (status, customer search, min/max values)
     */
    const clearValueFilters = useCallback(() => {
        const clearedFilters: AdminCartFilters = {
            ...filters,
            status: '',
            customerSearch: '',
            minValue: 0,
            maxValue: 0
        };

        updateURL(clearedFilters, 1);
    }, [filters, updateURL]);

    /**
     * Get server-side filters (sent to API for backend filtering)
     */
    const getServerFilters = useCallback(() => {
        const serverFilters: {
            status?: string;
            dateFrom?: string;
            dateTo?: string;
        } = {};

        // Add status only if not empty (omit for "All Statuses")
        if (filters.status && filters.status.trim() !== '') {
            serverFilters.status = filters.status;
        }

        // Add date filters (always available in UI)
        if (filters.dateFrom && filters.dateTo) {
            serverFilters.dateFrom = filters.dateFrom;
            serverFilters.dateTo = filters.dateTo;
        }

        logger.debug('CartFilters: Generated server filters', { serverFilters, originalFilters: filters });
        return serverFilters;
    }, [filters]);

    /**
     * Get client-side filters (for frontend filtering after API call)
     */
    const getClientFilters = useCallback(() => {
        const clientFilters = {
            customerSearch: filters.customerSearch,
            minValue: filters.minValue,
            maxValue: filters.maxValue
        };

        logger.debug('CartFilters: Generated client filters', { clientFilters, originalFilters: filters });
        return clientFilters;
    }, [filters]);

    return {
        filters,
        currentPage,
        limit,
        updateFilter,
        updatePage,
        resetFilters,
        clearValueFilters,
        getServerFilters,
        getClientFilters
    };
}; 
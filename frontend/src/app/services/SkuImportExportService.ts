import type { SKU, FlattenedSKU } from '@/app/types/sku';
import { logger } from '@/lib/logger';

/**
 * SkuImportExportService - Handles CSV import/export operations for SKUs
 * 
 * Responsibilities:
 * - SKU data flattening for CSV export
 * - SKU data nesting from CSV import  
 * - Data validation for import/export
 * - CSV text sanitization
 * 
 * Separated from core SkuService for better separation of concerns
 */
export class SkuImportExportService {

    /**
     * Sanitizes text for CSV export by removing line breaks and extra whitespace
     */
    private static sanitizeForCSV(text: string): string {
        if (!text) return '';
        return text
            .replace(/\r\n/g, ' ') // Replace Windows line breaks
            .replace(/\n/g, ' ')   // Replace Unix line breaks
            .replace(/\r/g, ' ')   // Replace Mac line breaks
            .replace(/\s+/g, ' ')  // Replace multiple spaces with single space
            .trim();               // Remove leading/trailing whitespace
    }

    /**
     * Flattens nested SKU data for export to CSV
     */
    public static flattenSkusForExport(skus: SKU[]): FlattenedSKU[] {
        const flattened: FlattenedSKU[] = [];

        for (const sku of skus) {
            // Use the SKU's own type field directly (no need to check variants)
            const skuType = sku.type || 'child'; // Default to 'child' if not specified

            // Ensure we have a valid name for all SKUs
            let nameEn = sku.name?.en || '';
            const nameTa = sku.name?.ta || '';

            if (!nameEn || !nameEn.trim()) {
                nameEn = `Product ${sku.skuId}`;
            }

            // Add the SKU as a flattened entry
            flattened.push({
                skuId: sku.skuId,
                nameEn: this.sanitizeForCSV(nameEn),
                nameTa: this.sanitizeForCSV(nameTa),
                costPrice: skuType === 'parent' ? undefined : (sku.costPrice || 0),
                sellingPrice: skuType === 'parent' ? undefined : (sku.sellingPrice || 0),
                mrp: skuType === 'parent' ? undefined : (sku.mrp || 0),
                imageUrl: sku.imageUrl || '',
                images: (sku.images || []).join(','),
                descriptionEn: this.sanitizeForCSV(sku.description?.en || ''),
                descriptionTa: this.sanitizeForCSV(sku.description?.ta || ''),
                type: skuType,
                parentId: sku.parentId,
                status: (sku.isActive === 1 || sku.isActive === undefined) ? 'active' : 'inactive', // Convert number to string
                // Add variantName fields for child SKUs
                variantNameEn: skuType === 'child' ? this.sanitizeForCSV(sku.variantName?.en || '') : undefined,
                variantNameTa: skuType === 'child' ? this.sanitizeForCSV(sku.variantName?.ta || '') : undefined,
            });

            // ✅ NO VARIANTS PROCESSING - variants are already independent SKUs in the admin interface
            // This eliminates the duplication issue where child SKUs were being exported twice:
            // 1. Once as standalone SKUs (correct)
            // 2. Again from parent's variants array (incorrect - causing duplicates)
        }

        logger.log(`SkuImportExportService: Flattened ${skus.length} SKUs for export`);
        return flattened;
    }

    /**
     * Nests flattened SKU data for storage and frontend consumption
     */
    public static nestSkusFromImport(flattenedSkus: FlattenedSKU[]): SKU[] {
        const skuMap = new Map<number, SKU>();
        const parentChildMap = new Map<number, FlattenedSKU[]>();

        // First normalize the data (same as in validation)
        const normalizedSkus = flattenedSkus.map(sku => ({
            ...sku,
            type: sku.type || 'child', // Default to 'child' if not specified
            nameEn: sku.nameEn || '',
            nameTa: sku.nameTa || '',
            descriptionEn: sku.descriptionEn || '',
            descriptionTa: sku.descriptionTa || '',
            imageUrl: sku.imageUrl || '',
            images: sku.images || '',
            status: sku.status || 'active', // Default to 'active' if not specified
            // Handle null values for numbers
            costPrice: sku.costPrice === null ? undefined : sku.costPrice,
            sellingPrice: sku.sellingPrice === null ? undefined : sku.sellingPrice,
            mrp: sku.mrp === null ? undefined : sku.mrp,
            parentId: sku.parentId === null ? undefined : sku.parentId
        }));

        // First pass: separate parents and children
        const parents: FlattenedSKU[] = [];
        const children: FlattenedSKU[] = [];

        for (const flatSku of normalizedSkus) {
            if (flatSku.type === 'parent') {
                parents.push(flatSku);
            } else {
                children.push(flatSku);

                // Group children by parent
                if (flatSku.parentId) {
                    if (!parentChildMap.has(flatSku.parentId)) {
                        parentChildMap.set(flatSku.parentId, []);
                    }
                    parentChildMap.get(flatSku.parentId)!.push(flatSku);
                }
            }
        }

        // Second pass: create parent SKUs
        for (const parentFlat of parents) {
            const parentSku: SKU = {
                skuId: parentFlat.skuId,
                name: {
                    en: (parentFlat.nameEn || '').trim(),
                    ta: (parentFlat.nameTa || '').trim()
                },
                costPrice: 0, // Parent doesn't have pricing
                sellingPrice: 0, // Parent doesn't have pricing
                mrp: 0, // Parent doesn't have pricing
                imageUrl: parentFlat.imageUrl,
                images: parentFlat.images ? parentFlat.images.split(',').filter(img => img.trim()) : [],
                description: {
                    en: (parentFlat.descriptionEn || '').trim(),
                    ta: (parentFlat.descriptionTa || '').trim()
                },
                variants: [],
                type: 'parent',
                parentId: parentFlat.parentId,
                isActive: (parentFlat.status === 'active' || parentFlat.status === undefined) ? 1 : 0 // Convert string to number
            };

            skuMap.set(parentSku.skuId, parentSku);
        }

        // Third pass: create child SKUs and add as variants to parents AND as standalone SKUs
        for (const childFlat of children) {
            const childSku: SKU = {
                skuId: childFlat.skuId,
                name: {
                    en: (childFlat.nameEn || '').trim(),
                    ta: (childFlat.nameTa || '').trim()
                },
                costPrice: childFlat.costPrice || 0,
                sellingPrice: childFlat.sellingPrice || 0,
                mrp: childFlat.mrp || 0,
                imageUrl: childFlat.imageUrl,
                images: childFlat.images ? childFlat.images.split(',').filter(img => img.trim()) : [],
                description: {
                    en: (childFlat.descriptionEn || '').trim(),
                    ta: (childFlat.descriptionTa || '').trim()
                },
                variants: [],
                type: 'child',
                parentId: childFlat.parentId,
                isActive: (childFlat.status === 'active' || childFlat.status === undefined) ? 1 : 0, // Convert string to number
                // Reconstruct variantName from flat strings
                variantName: (childFlat.variantNameEn?.trim() || childFlat.variantNameTa?.trim()) ? {
                    en: (childFlat.variantNameEn || '').trim(),
                    ta: (childFlat.variantNameTa || '').trim()
                } : undefined
            };

            // Always store child SKUs as separate entries for admin interface
            skuMap.set(childSku.skuId, childSku);

            // If it has a parent, also add as variant to parent
            if (childFlat.parentId) {
                const parent = skuMap.get(childFlat.parentId);
                if (parent) {
                    parent.variants?.push({
                        skuId: childSku.skuId,
                        name: childSku.name,
                        costPrice: childSku.costPrice || 0,
                        sellingPrice: childSku.sellingPrice || 0,
                        mrp: childSku.mrp || 0,
                        type: 'child',
                        variantName: childSku.variantName, // Include variantName in variant
                        imageUrl: childSku.imageUrl,
                        images: childSku.images,
                        description: childSku.description,
                        isActive: childSku.isActive ?? 1, // Include child SKU's active status
                    });
                } else {
                    logger.warn(`SkuImportExportService: Parent SKU ${childFlat.parentId} not found for child SKU ${childFlat.skuId}, but child stored as standalone`);
                }
            }
        }

        const result = Array.from(skuMap.values());
        logger.log(`SkuImportExportService: Nested ${flattenedSkus.length} flattened SKUs into ${result.length} structured SKUs`);
        return result;
    }

    /**
     * Validates source SKUs before export to ensure data integrity and prevent conflicts.
     *
     * VALIDATION FLOW:
     * 1. Duplicate Detection: Ensures each SKU ID appears only once in the main SKU list
     * 2. Self-Reference Check: Warns about variants that reference their own parent SKU ID
     * 3. Required Fields: Validates essential fields (name, imageUrl) are present
     *
     * @param skus Array of SKU objects to validate
     * @returns Validation result with errors and warnings
     */
    public static validateSourceSkus(skus: SKU[]): { isValid: boolean; errors: string[]; warnings: string[] } {
        const errors: string[] = [];
        const warnings: string[] = [];

        // Track all main SKU IDs to detect duplicates and variant conflicts
        const skuIds = new Set<number>();

        for (const sku of skus) {
            // STEP 1: Duplicate Detection
            // Check if this SKU ID has already been encountered in the main SKU list
            if (skuIds.has(sku.skuId)) {
                errors.push(`Duplicate SKU ID: ${sku.skuId}`);
            }
            // Register this SKU ID in our tracking system
            skuIds.add(sku.skuId);

            // STEP 2: Self-Reference Detection
            // Check if any variant within this SKU references the same SKU ID as its parent
            // This creates circular references and should be avoided
            if (sku.variants && sku.variants.length > 0) {
                for (const variant of sku.variants) {
                    if (variant.skuId === sku.skuId) {
                        warnings.push(`SKU ${sku.skuId}: Contains self-referencing variant - this variant will be skipped during export`);
                    }
                }
            }

            // STEP 3: Required Fields Validation
            // Ensure essential fields are present for proper SKU functionality
            if (!sku.name?.en?.trim()) {
                errors.push(`SKU ${sku.skuId}: English name is required`);
            }

            // STEP 4: Variant Validation
            // Validate variant IDs for uniqueness and potential conflicts
            if (sku.variants && sku.variants.length > 0) {
                const variantIds = new Set<number>();

                for (const variant of sku.variants) {
                    // Check for duplicate variant IDs within the same parent SKU
                    if (variantIds.has(variant.skuId)) {
                        errors.push(`SKU ${sku.skuId}: Duplicate variant SKU ID ${variant.skuId}`);
                    }
                    variantIds.add(variant.skuId);
                }
            }
        }

        logger.log(`SkuImportExportService: Validated ${skus.length} source SKUs - ${errors.length} errors, ${warnings.length} warnings`);
        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }

    /**
     * Validates flattened SKU data before import
     */
    public static validateFlattenedSkus(flattenedSkus: FlattenedSKU[]): { isValid: boolean; errors: string[] } {
        const errors: string[] = [];
        const skuIdCounts = new Map<number, { parent: number; child: number }>();
        const parentIds = new Set<number>();

        // First pass: normalize data and count SKU ID occurrences by type
        const normalizedSkus = flattenedSkus.map(sku => {
            // Normalize null values and set defaults
            const normalized = {
                ...sku,
                type: sku.type || 'child', // Default to 'child' if not specified
                nameEn: sku.nameEn || '',
                nameTa: sku.nameTa || '',
                descriptionEn: sku.descriptionEn || '',
                descriptionTa: sku.descriptionTa || '',
                imageUrl: sku.imageUrl || '',
                images: sku.images || '',
                status: sku.status || 'active', // Default to 'active' if not specified
                // Handle null values for numbers
                costPrice: sku.costPrice === null ? undefined : sku.costPrice,
                sellingPrice: sku.sellingPrice === null ? undefined : sku.sellingPrice,
                mrp: sku.mrp === null ? undefined : sku.mrp,
                parentId: sku.parentId === null ? undefined : sku.parentId
            };
            return normalized;
        });

        // Count SKU IDs by type to detect real duplicates
        for (const sku of normalizedSkus) {
            if (!skuIdCounts.has(sku.skuId)) {
                skuIdCounts.set(sku.skuId, { parent: 0, child: 0 });
            }

            const counts = skuIdCounts.get(sku.skuId)!;
            if (sku.type === 'parent') {
                counts.parent++;
                parentIds.add(sku.skuId);
            } else {
                counts.child++;
            }
        }

        // Check for actual duplicates (multiple entries of the same type for the same SKU ID)
        for (const [skuId, counts] of skuIdCounts.entries()) {
            if (counts.parent > 1) {
                errors.push(`Duplicate parent SKU ID: ${skuId} (found ${counts.parent} parent entries)`);
            }
            if (counts.child > 1) {
                errors.push(`Duplicate child SKU ID: ${skuId} (found ${counts.child} child entries)`);
            }
        }

        // Validate each SKU
        for (const sku of normalizedSkus) {
            // Validate required fields
            if (!sku.nameEn?.trim()) {
                errors.push(`SKU ${sku.skuId}: English name is required`);
            }

            // Only require imageUrl for parent SKUs, child SKUs can inherit from parent
            if (sku.type === 'parent' && !sku.imageUrl?.trim()) {
                errors.push(`SKU ${sku.skuId}: Primary image URL is required for parent SKUs`);
            }

            // ❌ Check for self-referencing (child pointing to itself as parent)
            if (sku.type === 'child' && sku.parentId === sku.skuId) {
                errors.push(`SKU ${sku.skuId}: Self-referencing detected - child SKU cannot be its own parent`);
            }

            // Validate type-specific requirements
            if (sku.type === 'child') {
                // Only validate parent ID if it's provided (some child SKUs might be standalone)
                if (sku.parentId && !parentIds.has(sku.parentId)) {
                    errors.push(`SKU ${sku.skuId}: Parent ID ${sku.parentId} not found in the data`);
                }

                // Only require pricing for child SKUs that have a parent (variants)
                if (sku.parentId && (sku.costPrice === undefined || sku.sellingPrice === undefined || sku.mrp === undefined)) {
                    errors.push(`SKU ${sku.skuId}: Child SKUs with parent must have cost price, selling price, and MRP`);
                }

                // Validate pricing logic only if all prices are provided
                if (sku.costPrice !== undefined && sku.sellingPrice !== undefined && sku.mrp !== undefined) {
                    if (sku.sellingPrice > sku.mrp) {
                        errors.push(`SKU ${sku.skuId}: Selling price (${sku.sellingPrice}) cannot be greater than MRP (${sku.mrp})`);
                    }
                }
            }

            if (sku.type === 'parent') {
                // Be more lenient - only error if pricing is explicitly set to non-zero values
                if ((sku.costPrice && sku.costPrice > 0) || (sku.sellingPrice && sku.sellingPrice > 0) || (sku.mrp && sku.mrp > 0)) {
                    errors.push(`SKU ${sku.skuId}: Parent SKUs should not have pricing information (cost: ${sku.costPrice}, selling: ${sku.sellingPrice}, mrp: ${sku.mrp})`);
                }
            }

            // Validate status field
            if (sku.status && !['active', 'inactive'].includes(sku.status)) {
                errors.push(`SKU ${sku.skuId}: Status must be 'active' or 'inactive', got '${sku.status}'`);
            }
        }

        logger.log(`SkuImportExportService: Validated ${flattenedSkus.length} flattened SKUs - ${errors.length} errors`);
        return {
            isValid: errors.length === 0,
            errors
        };
    }
} 
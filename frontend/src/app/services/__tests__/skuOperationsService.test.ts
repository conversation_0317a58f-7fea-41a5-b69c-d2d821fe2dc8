import { SkuOperationsService, BulkOperation } from '../skuOperationsService';
import { upsertSkus, getSkuById } from '../skuService';
import { getOrderedCategories, upsertCategories } from '../categoryService';
import type { SKU, SKUVariant } from '@/app/types/sku';
import type { Category } from '@/app/types/category';

// Mock dependencies
jest.mock('../skuService', () => ({
    upsertSkus: jest.fn(),
    getSkuById: jest.fn(),
    clearCachedSkus: jest.fn()
}));

jest.mock('../categoryService', () => ({
    getOrderedCategories: jest.fn(),
    upsertCategories: jest.fn()
}));

jest.mock('@/lib/logger', () => ({
    logger: {
        info: jest.fn(),
        error: jest.fn(),
        warn: jest.fn(),
        debug: jest.fn()
    }
}));

// Test data fixtures
const mockParentSku: SKU = {
    skuId: 100,
    type: 'parent',
    name: { en: 'Test Product', ta: 'டெஸ்ட் பொருள்' },
    description: { en: 'Test description', ta: 'சோதனை விளக்கம்' },
    imageUrl: 'https://example.com/parent.jpg',
    images: ['https://example.com/parent1.jpg'],
    isActive: 1,
    variants: [
        {
            skuId: 101,
            name: { en: 'Test Product', ta: 'டெஸ்ட் பொருள்' },
            variantName: { en: '500g', ta: '500g' },
            costPrice: 100,
            sellingPrice: 150,
            mrp: 200,
            type: 'child',
            isActive: 1
        }
    ]
};

const mockChildSku: SKU = {
    skuId: 101,
    type: 'child',
    parentId: 100,
    name: { en: 'Test Product', ta: 'டெஸ்ட் பொருள்' },
    description: { en: 'Test description', ta: 'சோதனை விளக்கம்' },
    variantName: { en: '500g', ta: '500g' },
    costPrice: 100,
    sellingPrice: 150,
    mrp: 200,
    imageUrl: 'https://example.com/child.jpg',
    images: ['https://example.com/child1.jpg'],
    isActive: 1
};

const mockOrphanChildSku: SKU = {
    skuId: 102,
    type: 'child',
    name: { en: 'Orphan Product', ta: 'அனாதை பொருள்' },
    description: { en: 'Orphan description', ta: 'அனாதை விளக்கம்' },
    costPrice: 120,
    sellingPrice: 180,
    mrp: 250,
    imageUrl: 'https://example.com/orphan.jpg',
    images: ['https://example.com/orphan1.jpg'],
    isActive: 1
};

const mockCategories: Category[] = [
    {
        id: 1,
        name: { en: 'Category 1', ta: 'வகை 1' },
        icon: 'FireIcon',
        orderNo: 1,
        skuIds: [100],
        isActive: 1,
        versionUuid: 'v1'
    },
    {
        id: 2,
        name: { en: 'Category 2', ta: 'வகை 2' },
        icon: 'ShoppingCartIcon',
        orderNo: 2,
        skuIds: [],
        isActive: 1,
        versionUuid: 'v2'
    }
];

describe('SkuOperationsService', () => {
    let mockUpsertSkus: jest.MockedFunction<typeof upsertSkus>;
    let mockGetSkuById: jest.MockedFunction<typeof getSkuById>;
    let mockGetOrderedCategories: jest.MockedFunction<typeof getOrderedCategories>;
    let mockUpsertCategories: jest.MockedFunction<typeof upsertCategories>;

    beforeEach(() => {
        jest.clearAllMocks();
        mockUpsertSkus = upsertSkus as jest.MockedFunction<typeof upsertSkus>;
        mockGetSkuById = getSkuById as jest.MockedFunction<typeof getSkuById>;
        mockGetOrderedCategories = getOrderedCategories as jest.MockedFunction<typeof getOrderedCategories>;
        mockUpsertCategories = upsertCategories as jest.MockedFunction<typeof upsertCategories>;

        mockUpsertSkus.mockResolvedValue('Success');
        mockGetOrderedCategories.mockResolvedValue(mockCategories);
        mockUpsertCategories.mockResolvedValue(undefined);
    });

    describe('saveSingleSku', () => {
        test('should save a standalone child SKU without parent synchronization', async () => {
            const selectedCategoryIds = [1, 2];

            await SkuOperationsService.saveSingleSku(mockOrphanChildSku, selectedCategoryIds);

            // Should call upsert with the child SKU only
            expect(mockUpsertSkus).toHaveBeenCalledWith([mockOrphanChildSku]);

            // Should update categories
            expect(mockUpsertCategories).toHaveBeenCalled();
        });

        test('should save child SKU and synchronize parent variants', async () => {
            mockGetSkuById.mockResolvedValue(mockParentSku);

            const selectedCategoryIds = [1];

            await SkuOperationsService.saveSingleSku(mockChildSku, selectedCategoryIds);

            expect(mockUpsertSkus).toHaveBeenCalledWith(
                expect.arrayContaining([
                    mockChildSku,
                    expect.objectContaining({
                        skuId: 100,
                        type: 'parent',
                        variants: expect.arrayContaining([
                            expect.objectContaining({
                                skuId: 101,
                                variantName: { en: '500g', ta: '500g' },
                                isActive: 1
                            })
                        ])
                    })
                ])
            );
        });

        test('should handle orphan conversion when parent is removed', async () => {
            mockGetSkuById.mockResolvedValue(mockParentSku);

            const originalChildWithParent = { ...mockChildSku, parentId: 100 };
            const updatedChildWithoutParent = { ...mockChildSku, parentId: undefined };

            await SkuOperationsService.saveSingleSku(
                updatedChildWithoutParent,
                [1],
                originalChildWithParent
            );

            // Should update parent to remove this child from variants
            expect(mockUpsertSkus).toHaveBeenCalledWith(
                expect.arrayContaining([
                    expect.objectContaining({
                        skuId: 100,
                        variants: expect.not.arrayContaining([
                            expect.objectContaining({ skuId: 101 })
                        ])
                    })
                ])
            );
        });

        test('should update category relationships bidirectionally', async () => {
            const selectedCategoryIds = [2]; // Moving from category 1 to category 2

            await SkuOperationsService.saveSingleSku(mockOrphanChildSku, selectedCategoryIds);

            expect(mockUpsertCategories).toHaveBeenCalledWith(
                expect.arrayContaining([
                    expect.objectContaining({
                        id: 2,
                        skuIds: expect.arrayContaining([102])
                    })
                ])
            );
        });

        test('should validate required fields for child SKUs', async () => {
            const invalidChildSku = {
                ...mockChildSku,
                name: { en: '', ta: '' }, // Missing required name
                variantName: undefined
            };

            await expect(
                SkuOperationsService.saveSingleSku(invalidChildSku, [])
            ).rejects.toThrow('Validation failed');
        });
    });

    describe('saveBulkSkus', () => {
        test('should handle bulk status change operations', async () => {
            const skusToUpdate = [mockChildSku, mockOrphanChildSku];
            const operation: BulkOperation = {
                type: 'statusChange',
                newStatus: 'inactive'
            };

            await SkuOperationsService.saveBulkSkus(skusToUpdate, operation);

            expect(mockUpsertSkus).toHaveBeenCalledWith(
                expect.arrayContaining([
                    expect.objectContaining({ skuId: 101, isActive: 0 }),
                    expect.objectContaining({ skuId: 102, isActive: 0 })
                ])
            );
        });

        test('should synchronize parent variants during bulk operations', async () => {
            mockGetSkuById.mockResolvedValue(mockParentSku);

            const skusToUpdate = [mockChildSku];
            const operation: BulkOperation = {
                type: 'statusChange',
                newStatus: 'inactive'
            };

            await SkuOperationsService.saveBulkSkus(skusToUpdate, operation);

            // Should update both child and parent
            expect(mockUpsertSkus).toHaveBeenCalledWith(
                expect.arrayContaining([
                    expect.objectContaining({ skuId: 101, isActive: 0 }),
                    expect.objectContaining({
                        skuId: 100,
                        variants: expect.arrayContaining([
                            expect.objectContaining({
                                skuId: 101,
                                isActive: 0 // Parent variant should reflect child's new status
                            })
                        ])
                    })
                ])
            );
        });

        test('should handle mixed parent and child SKUs in bulk operations', async () => {
            const skusToUpdate = [mockParentSku, mockChildSku];
            const operation: BulkOperation = {
                type: 'bulkEdit',
                fields: { isActive: 0 }
            };

            await SkuOperationsService.saveBulkSkus(skusToUpdate, operation);

            expect(mockUpsertSkus).toHaveBeenCalled();
        });

        test('should validate all SKUs in bulk before processing', async () => {
            const invalidSkus = [
                { ...mockChildSku, name: { en: '', ta: '' } },
                mockOrphanChildSku
            ];

            const operation: BulkOperation = {
                type: 'statusChange',
                newStatus: 'active'
            };

            await expect(
                SkuOperationsService.saveBulkSkus(invalidSkus, operation)
            ).rejects.toThrow('Bulk validation failed');
        });
    });

    describe('updateSkuStatus', () => {
        test('should update status and synchronize parent variants', async () => {
            // Mock getSkuById to return the child SKU when called with 101
            mockGetSkuById.mockImplementation((skuId: string | number) => {
                const id = typeof skuId === 'string' ? parseInt(skuId) : skuId;
                if (id === 101) return Promise.resolve(mockChildSku);
                if (id === 100) return Promise.resolve(mockParentSku);
                return Promise.resolve(null);
            });

            await SkuOperationsService.updateSkuStatus([101], 'inactive');

            expect(mockUpsertSkus).toHaveBeenCalledWith(
                expect.arrayContaining([
                    expect.objectContaining({ skuId: 101, isActive: 0 }),
                    expect.objectContaining({
                        skuId: 100,
                        variants: expect.arrayContaining([
                            expect.objectContaining({
                                skuId: 101,
                                isActive: 0
                            })
                        ])
                    })
                ])
            );
        });

        test('should handle multiple SKU status updates', async () => {
            // Mock getSkuById to return appropriate SKUs
            mockGetSkuById.mockImplementation((skuId: string | number) => {
                const id = typeof skuId === 'string' ? parseInt(skuId) : skuId;
                if (id === 101) return Promise.resolve(mockChildSku);
                if (id === 102) return Promise.resolve(mockOrphanChildSku);
                return Promise.resolve(null);
            });

            await SkuOperationsService.updateSkuStatus([101, 102], 'active');

            expect(mockUpsertSkus).toHaveBeenCalledWith(
                expect.arrayContaining([
                    expect.objectContaining({ skuId: 101, isActive: 1 }),
                    expect.objectContaining({ skuId: 102, isActive: 1 })
                ])
            );
        });
    });

    describe('internal validation methods', () => {
        test('validateSkuData should detect missing required fields', () => {
            const invalidSku = {
                ...mockChildSku,
                name: { en: '', ta: '' },
                variantName: undefined
            };

            expect(() => {
                (SkuOperationsService as any)['validateSkuData'](invalidSku);
            }).toThrow('English name is required');
        });

        test('validateSkuData should pass for valid SKUs', () => {
            expect(() => {
                (SkuOperationsService as any)['validateSkuData'](mockChildSku);
            }).not.toThrow();
        });

        test('transformForApi should include variantName for child SKUs', () => {
            const transformed = (SkuOperationsService as any)['transformForApi']([mockChildSku]);

            expect(transformed[0]).toEqual(
                expect.objectContaining({
                    skuId: 101,
                    type: 'child',
                    variantName: { en: '500g', ta: '500g' }
                })
            );
        });

        test('transformForApi should include variants for parent SKUs', () => {
            const transformed = (SkuOperationsService as any)['transformForApi']([mockParentSku]);

            expect(transformed[0]).toEqual(
                expect.objectContaining({
                    skuId: 100,
                    type: 'parent',
                    variants: expect.arrayContaining([
                        expect.objectContaining({
                            variantName: { en: '500g', ta: '500g' },
                            isActive: 1
                        })
                    ])
                })
            );
        });
    });

    describe('error handling', () => {
        test('should handle API failures gracefully', async () => {
            mockUpsertSkus.mockRejectedValue(new Error('API Error'));

            await expect(
                SkuOperationsService.saveSingleSku(mockOrphanChildSku, [])
            ).rejects.toThrow('API Error');
        });

        test('should continue if category update fails', async () => {
            mockUpsertCategories.mockRejectedValue(new Error('Category API Error'));

            // Should not throw, but log the error
            await expect(
                SkuOperationsService.saveSingleSku(mockOrphanChildSku, [1])
            ).resolves.toBeUndefined();

            expect(mockUpsertSkus).toHaveBeenCalled();
        });

        test('should handle parent sync failures gracefully', async () => {
            mockGetSkuById.mockRejectedValue(new Error('Parent not found'));

            // Should still save the child SKU
            await expect(
                SkuOperationsService.saveSingleSku(mockChildSku, [])
            ).resolves.toBeUndefined();

            expect(mockUpsertSkus).toHaveBeenCalledWith([mockChildSku]);
        });
    });

    describe('performance optimizations', () => {
        test('should batch parent updates with child updates', async () => {
            mockGetSkuById.mockResolvedValue(mockParentSku);

            await SkuOperationsService.saveSingleSku(mockChildSku, []);

            // Should call upsert only once with both child and parent
            expect(mockUpsertSkus).toHaveBeenCalledTimes(1);
            expect(mockUpsertSkus).toHaveBeenCalledWith(
                expect.arrayContaining([
                    expect.objectContaining({ skuId: 101 }),
                    expect.objectContaining({ skuId: 100 })
                ])
            );
        });

        test('should avoid unnecessary parent updates', async () => {
            // Child SKU without parent should not trigger parent operations
            await SkuOperationsService.saveSingleSku(mockOrphanChildSku, []);

            expect(mockUpsertSkus).toHaveBeenCalledWith([mockOrphanChildSku]);
        });
    });
});

// Integration test helpers
export const createMockSku = (overrides: Partial<SKU> = {}): SKU => ({
    skuId: 999,
    type: 'child',
    name: { en: 'Test SKU', ta: 'சோதனை' },
    description: { en: 'Test description', ta: 'சோதனை விளக்கம்' },
    imageUrl: 'https://example.com/test.jpg',
    images: ['https://example.com/test1.jpg'],
    costPrice: 100,
    sellingPrice: 150,
    mrp: 200,
    isActive: 1,
    ...overrides
});

export const createMockCategory = (overrides: Partial<Category> = {}): Category => ({
    id: 999,
    name: { en: 'Test Category', ta: 'சோதனை வகை' },
    icon: 'FireIcon',
    orderNo: 1,
    skuIds: [],
    isActive: 1,
    versionUuid: 'test-v1',
    ...overrides
}); 
import {
    UserDetails,
    SendOtpRequest,
    SendOtpResponse,
    VerifyOtpRequest,
    VerifyOtpResponse,
    AdminRolesData
} from '../types/auth';
import axiosInstance from '../../lib/axios';
import { formatIndianMobile, validateIndianMobile, maskIndianMobile } from '../../lib/utils';
import { logger } from '../../lib/logger';

export interface UserLoginResponse {
    token: string;
    user: UserDetails;
    expiresAt: number;
    refreshAfter: number;
}

/**
 * Sends an OTP to the user's mobile number
 * @param mobileNumber Mobile number (should include +91 prefix)
 * @param langCode Language code (default: 'en')
 * @returns Promise that resolves when OTP is sent successfully
 */
export const requestOtpAPI = async (mobileNumber: string, langCode: string = 'en'): Promise<void> => {
    logger.info('authService: Requesting OTP', { mobileNumber: maskIndianMobile(mobileNumber) });

    // Validate and format mobile number
    if (!validateIndianMobile(formatIndianMobile(mobileNumber))) {
        throw new Error('Invalid mobile number format');
    }

    const formattedMobile = formatIndianMobile(mobileNumber);

    const requestData: SendOtpRequest = {
        langCode,
        action: "sendOtp",
        mobile: formattedMobile
    };

    // axios handles response extraction and error handling
    await axiosInstance.post('/userApp-auth-sendOtp', requestData);

    logger.info('authService: OTP sent successfully', { mobileNumber: maskIndianMobile(formattedMobile) });
};

/**
 * Verifies the OTP with the backend
 * @param mobileNumber Mobile number (should include +91 prefix)
 * @param otp OTP code as string
 * @returns Promise that resolves with user login response
 */
export const verifyOtpAPI = async (mobileNumber: string, otp: string): Promise<UserLoginResponse> => {
    logger.info('authService: Verifying OTP', {
        mobileNumber: maskIndianMobile(mobileNumber),
        otpLength: otp.length
    });

    // Validate and format mobile number
    const formattedMobile = formatIndianMobile(mobileNumber);
    if (!validateIndianMobile(formattedMobile)) {
        throw new Error('Invalid mobile number format');
    }

    // Convert OTP string to number
    const otpNumber = parseInt(otp, 10);
    if (isNaN(otpNumber)) {
        throw new Error('Invalid OTP format');
    }

    const requestData: VerifyOtpRequest = {
        otp: otpNumber,
        mobile: formattedMobile
    };

    // axios extracts data automatically and handles errors
    const responseData: VerifyOtpResponse = await axiosInstance.post('/userApp-auth-verifyOtp', requestData);

    logger.info('authService: OTP verified successfully');

    // Transform the response to match our internal format
    const userLoginResponse: UserLoginResponse = {
        token: responseData.authInfo.token,
        user: {
            id: responseData.id,
            name: responseData.name,
            mobileNumber: formattedMobile
        },
        expiresAt: responseData.authInfo.expiresAt,
        refreshAfter: responseData.authInfo.refreshAfter
    };

    return userLoginResponse;
};

/**
 * Fetches admin roles and permissions for the authenticated user
 * This should only be called when user visits admin pages
 * @returns Promise that resolves with admin roles data
 */
export const getAdminRoles = async (): Promise<AdminRolesData> => {
    logger.info('authService: Fetching admin roles');

    // axios interceptor will automatically add the Bearer token
    const responseData: AdminRolesData = await axiosInstance.post('/userApp-user-getAdminRolesForUser');

    logger.info('authService: Admin roles fetched successfully');
    return responseData;
};
/**
 * Service Setup and Dependency Injection
 * This file handles the initialization of service dependencies
 */

import { setCategoryService } from './skuService';
import * as categoryService from './categoryService';

/**
 * Initialize all service dependencies
 * Should be called early in the application lifecycle
 */
export const initializeServices = (): void => {
  try {
    // Inject categoryService into skuService
    setCategoryService(categoryService);

    console.log('✅ Services initialized successfully');
  } catch (error) {
    console.error('❌ Failed to initialize services:', error);
    throw error;
  }
};

/**
 * Check if services are properly initialized
 */
export const verifyServiceInitialization = (): boolean => {
  try {
    // This will throw an error if categoryService is not injected
    setCategoryService(categoryService);
    return true;
  } catch (error) {
    console.warn('⚠️ Services not properly initialized:', error);
    return false;
  }
};
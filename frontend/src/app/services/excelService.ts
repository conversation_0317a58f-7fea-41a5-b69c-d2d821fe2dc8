import * as XLSX from 'xlsx';

export interface ExcelColumn {
    key: string;
    header: string;
    width?: number;
    type?: 'string' | 'number' | 'date' | 'boolean';
    required?: boolean;
    validation?: {
        min?: number;
        max?: number;
        pattern?: RegExp;
        options?: string[];
    };
}

export interface ExcelTemplate {
    name: string;
    columns: ExcelColumn[];
    sampleData?: any[];
    instructions?: string[];
}

export interface ParsedExcelData {
    data: any[];
    errors: Array<{
        row: number;
        column: string;
        message: string;
        value?: any;
    }>;
    warnings: string[];
}

class ExcelService {
    /**
     * Generate and download a CSV file with instructions
     */
    async generateCSV(
        data: any[],
        columns: ExcelColumn[],
        filename: string,
        options: {
            includeInstructions?: boolean;
            instructions?: string[];
        } = {}
    ): Promise<void> {
        const {
            includeInstructions = true,
            instructions = []
        } = options;

        const csvLines = [];

        // Add instructions as comments if requested
        if (includeInstructions && instructions.length > 0) {
            csvLines.push('# Export Instructions:');
            instructions.forEach((instruction, index) => {
                csvLines.push(`# ${index + 1}. ${instruction}`);
            });
            csvLines.push('#');
        }

        // Add column information as comments
        if (includeInstructions) {
            csvLines.push('# Column Information:');
            columns.forEach(col => {
                const required = col.required ? 'Required' : 'Optional';
                const type = col.type || 'string';
                const options = col.validation?.options ? ` (Options: ${col.validation.options.join(', ')})` : '';
                csvLines.push(`# ${col.header}: ${required}, Type: ${type}${options}`);
            });
            csvLines.push('#');
            csvLines.push('# This file was exported from the admin portal');
            csvLines.push('# You can modify the data and re-import it');
            csvLines.push('#');
        }

        // Add headers
        const headers = columns.map(col => col.header);
        csvLines.push(headers.join(','));

        // Add data rows
        data.forEach(row => {
            const rowData = columns.map(col => {
                const value = row[col.key];
                // Escape commas and quotes in CSV
                if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
                    return `"${value.replace(/"/g, '""')}"`;
                }
                return value || '';
            });
            csvLines.push(rowData.join(','));
        });

        const csvContent = csvLines.join('\n');
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        this.downloadFile(blob, filename, 'text/csv');
    }

    /**
     * Generate CSV template for import with instructions
     */
    async generateCSVTemplate(template: ExcelTemplate): Promise<void> {
        const headers = template.columns.map(col => col.header);

        // Create CSV content with instructions as comments and sample data
        const csvLines = [];

        // Add instructions as comments
        if (template.instructions && template.instructions.length > 0) {
            csvLines.push('# Import Instructions:');
            template.instructions.forEach((instruction, index) => {
                csvLines.push(`# ${index + 1}. ${instruction}`);
            });
            csvLines.push('#');
        }

        // Add column information
        csvLines.push('# Column Information:');
        template.columns.forEach(col => {
            const required = col.required ? 'Required' : 'Optional';
            const type = col.type || 'string';
            const options = col.validation?.options ? ` (Options: ${col.validation.options.join(', ')})` : '';
            csvLines.push(`# ${col.header}: ${required}, Type: ${type}${options}`);
        });
        csvLines.push('#');
        csvLines.push('# Remove all lines starting with # before importing');
        csvLines.push('#');

        // Add headers
        csvLines.push(headers.join(','));

        // Add sample data if provided
        if (template.sampleData && template.sampleData.length > 0) {
            template.sampleData.forEach(row => {
                const rowData = template.columns.map(col => {
                    const value = row[col.key];
                    if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
                        return `"${value.replace(/"/g, '""')}"`;
                    }
                    return value || '';
                });
                csvLines.push(rowData.join(','));
            });
        } else {
            // Add one empty row for user to start with
            const emptyRow = template.columns.map(() => '');
            csvLines.push(emptyRow.join(','));
        }

        const csvContent = csvLines.join('\n');
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        this.downloadFile(blob, `${template.name}_template.csv`, 'text/csv');
    }

    /**
     * Parse CSV file and validate data
     */
    async parseCSVFile(file: File, columns: ExcelColumn[]): Promise<ParsedExcelData> {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();

            reader.onload = (e) => {
                try {
                    const csvText = e.target?.result as string;

                    // Remove comment lines (lines starting with #)
                    const lines = csvText.split('\n').filter(line => !line.trim().startsWith('#'));

                    // Parse CSV manually (simple implementation)
                    const csvData = lines.map(line => {
                        const result = [];
                        let current = '';
                        let inQuotes = false;

                        for (let i = 0; i < line.length; i++) {
                            const char = line[i];

                            if (char === '"') {
                                if (inQuotes && line[i + 1] === '"') {
                                    current += '"';
                                    i++; // Skip next quote
                                } else {
                                    inQuotes = !inQuotes;
                                }
                            } else if (char === ',' && !inQuotes) {
                                result.push(current.trim());
                                current = '';
                            } else {
                                current += char;
                            }
                        }
                        result.push(current.trim());
                        return result;
                    });

                    // Parse and validate
                    const result = this.validateExcelData(csvData, columns);
                    resolve(result);
                } catch (error) {
                    reject(new Error(`Failed to parse CSV file: ${error}`));
                }
            };

            reader.onerror = () => {
                reject(new Error('Failed to read file'));
            };

            reader.readAsText(file);
        });
    }

    /**
     * Parse Excel or CSV file based on file type
     */
    async parseFile(file: File, columns: ExcelColumn[]): Promise<ParsedExcelData> {
        if (file.type === 'text/csv' || file.name.toLowerCase().endsWith('.csv')) {
            return this.parseCSVFile(file, columns);
        } else {
            return this.parseExcelFile(file, columns);
        }
    }

    /**
     * Generate and download an Excel file
     */
    async generateExcel(
        data: any[],
        columns: ExcelColumn[],
        filename: string,
        options: {
            includeHeaders?: boolean;
            includeInstructions?: boolean;
            instructions?: string[];
        } = {}
    ): Promise<void> {
        const {
            includeHeaders = true,
            includeInstructions = false,
            instructions = []
        } = options;

        // Create workbook
        const workbook = XLSX.utils.book_new();

        // Prepare data with headers
        const worksheetData: any[][] = [];

        // Add instructions if requested
        if (includeInstructions && instructions.length > 0) {
            worksheetData.push(['Instructions:']);
            instructions.forEach((instruction, index) => {
                worksheetData.push([`${index + 1}. ${instruction}`]);
            });
            worksheetData.push([]); // Empty row
        }

        // Add headers
        if (includeHeaders) {
            const headers = columns.map(col => col.header);
            worksheetData.push(headers);
        }

        // Add data rows
        data.forEach(row => {
            const rowData = columns.map(col => {
                const value = row[col.key];

                // Format based on column type
                switch (col.type) {
                    case 'date':
                        return value instanceof Date ? value.toISOString().split('T')[0] : value;
                    case 'boolean':
                        return value ? 'Yes' : 'No';
                    case 'number':
                        return typeof value === 'number' ? value : parseFloat(value) || 0;
                    default:
                        return value || '';
                }
            });
            worksheetData.push(rowData);
        });

        // Create worksheet
        const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);

        // Set column widths
        const columnWidths = columns.map(col => ({ wch: col.width || 15 }));
        worksheet['!cols'] = columnWidths;

        // Add worksheet to workbook
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Data');

        // Generate buffer and download
        const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
        this.downloadFile(excelBuffer, filename, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    }

    /**
     * Generate Excel template for import
     */
    async generateTemplate(template: ExcelTemplate): Promise<void> {
        const workbook = XLSX.utils.book_new();

        // Instructions sheet
        if (template.instructions && template.instructions.length > 0) {
            const instructionsData = [
                ['Import Instructions'],
                [''],
                ...template.instructions.map((instruction, index) => [`${index + 1}. ${instruction}`]),
                [''],
                ['Column Definitions:'],
                ['Column', 'Required', 'Type', 'Description'],
                ...template.columns.map(col => [
                    col.header,
                    col.required ? 'Yes' : 'No',
                    col.type || 'string',
                    col.validation?.options ? `Options: ${col.validation.options.join(', ')}` : ''
                ])
            ];

            const instructionsSheet = XLSX.utils.aoa_to_sheet(instructionsData);
            XLSX.utils.book_append_sheet(workbook, instructionsSheet, 'Instructions');
        }

        // Template sheet with headers and sample data
        const templateData: any[][] = [];

        // Headers
        const headers = template.columns.map(col => col.header);
        templateData.push(headers);

        // Sample data if provided
        if (template.sampleData && template.sampleData.length > 0) {
            template.sampleData.forEach(row => {
                const rowData = template.columns.map(col => row[col.key] || '');
                templateData.push(rowData);
            });
        } else {
            // Add one empty row for user to start with
            const emptyRow = template.columns.map(() => '');
            templateData.push(emptyRow);
        }

        const templateSheet = XLSX.utils.aoa_to_sheet(templateData);

        // Set column widths
        const columnWidths = template.columns.map(col => ({ wch: col.width || 20 }));
        templateSheet['!cols'] = columnWidths;

        XLSX.utils.book_append_sheet(workbook, templateSheet, 'Template');

        // Generate and download
        const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
        this.downloadFile(excelBuffer, `${template.name}_template.xlsx`, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    }

    /**
     * Parse Excel file and validate data
     */
    async parseExcelFile(file: File, columns: ExcelColumn[]): Promise<ParsedExcelData> {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();

            reader.onload = (e) => {
                try {
                    const data = new Uint8Array(e.target?.result as ArrayBuffer);
                    const workbook = XLSX.read(data, { type: 'array' });

                    // Get first worksheet
                    const firstSheetName = workbook.SheetNames[0];
                    const worksheet = workbook.Sheets[firstSheetName];

                    // Convert to JSON
                    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

                    // Parse and validate
                    const result = this.validateExcelData(jsonData as any[][], columns);
                    resolve(result);
                } catch (error) {
                    reject(new Error(`Failed to parse Excel file: ${error}`));
                }
            };

            reader.onerror = () => {
                reject(new Error('Failed to read file'));
            };

            reader.readAsArrayBuffer(file);
        });
    }

    /**
     * Validate parsed Excel data against column definitions
     */
    private validateExcelData(rawData: any[][], columns: ExcelColumn[]): ParsedExcelData {
        const errors: Array<{ row: number; column: string; message: string; value?: any }> = [];
        const warnings: string[] = [];
        const validData: any[] = [];

        if (rawData.length === 0) {
            errors.push({ row: 0, column: 'general', message: 'File is empty' });
            return { data: [], errors, warnings };
        }

        // Assume first row is headers
        const headers = rawData[0];
        const dataRows = rawData.slice(1);

        // Validate headers
        const headerMap: { [key: string]: number } = {};
        columns.forEach(col => {
            const headerIndex = headers.findIndex((h: string) =>
                h && h.toString().toLowerCase().trim() === col.header.toLowerCase().trim()
            );

            if (headerIndex === -1) {
                if (col.required) {
                    errors.push({
                        row: 1,
                        column: col.key,
                        message: `Required column "${col.header}" not found`
                    });
                } else {
                    warnings.push(`Optional column "${col.header}" not found`);
                }
            } else {
                headerMap[col.key] = headerIndex;
            }
        });

        // Validate data rows
        dataRows.forEach((row, rowIndex) => {
            const actualRowNumber = rowIndex + 2; // +2 because we skip header and arrays are 0-indexed
            const rowData: any = {};
            let hasData = false;

            columns.forEach(col => {
                const cellIndex = headerMap[col.key];
                const cellValue = cellIndex !== undefined ? row[cellIndex] : undefined;

                // Check if row has any data
                if (cellValue !== undefined && cellValue !== null && cellValue !== '') {
                    hasData = true;
                }

                // Validate required fields
                if (col.required && (cellValue === undefined || cellValue === null || cellValue === '')) {
                    errors.push({
                        row: actualRowNumber,
                        column: col.key,
                        message: `Required field "${col.header}" is empty`,
                        value: cellValue
                    });
                    return;
                }

                // Skip validation for empty optional fields
                if (!col.required && (cellValue === undefined || cellValue === null || cellValue === '')) {
                    rowData[col.key] = null;
                    return;
                }

                // Type validation and conversion
                let validatedValue = cellValue;

                switch (col.type) {
                    case 'number':
                        validatedValue = parseFloat(cellValue);
                        if (isNaN(validatedValue)) {
                            errors.push({
                                row: actualRowNumber,
                                column: col.key,
                                message: `"${col.header}" must be a number`,
                                value: cellValue
                            });
                            return;
                        }

                        // Min/max validation
                        if (col.validation?.min !== undefined && validatedValue < col.validation.min) {
                            errors.push({
                                row: actualRowNumber,
                                column: col.key,
                                message: `"${col.header}" must be at least ${col.validation.min}`,
                                value: cellValue
                            });
                            return;
                        }

                        if (col.validation?.max !== undefined && validatedValue > col.validation.max) {
                            errors.push({
                                row: actualRowNumber,
                                column: col.key,
                                message: `"${col.header}" must be at most ${col.validation.max}`,
                                value: cellValue
                            });
                            return;
                        }
                        break;

                    case 'boolean':
                        const boolStr = cellValue.toString().toLowerCase().trim();
                        if (['yes', 'true', '1', 'active'].includes(boolStr)) {
                            validatedValue = true;
                        } else if (['no', 'false', '0', 'inactive'].includes(boolStr)) {
                            validatedValue = false;
                        } else {
                            errors.push({
                                row: actualRowNumber,
                                column: col.key,
                                message: `"${col.header}" must be Yes/No, True/False, or Active/Inactive`,
                                value: cellValue
                            });
                            return;
                        }
                        break;

                    case 'date':
                        const dateValue = new Date(cellValue);
                        if (isNaN(dateValue.getTime())) {
                            errors.push({
                                row: actualRowNumber,
                                column: col.key,
                                message: `"${col.header}" must be a valid date`,
                                value: cellValue
                            });
                            return;
                        }
                        validatedValue = dateValue;
                        break;

                    default: // string
                        validatedValue = cellValue.toString().trim();

                        // Pattern validation
                        if (col.validation?.pattern && !col.validation.pattern.test(validatedValue)) {
                            errors.push({
                                row: actualRowNumber,
                                column: col.key,
                                message: `"${col.header}" format is invalid`,
                                value: cellValue
                            });
                            return;
                        }

                        // Options validation
                        if (col.validation?.options && !col.validation.options.includes(validatedValue)) {
                            errors.push({
                                row: actualRowNumber,
                                column: col.key,
                                message: `"${col.header}" must be one of: ${col.validation.options.join(', ')}`,
                                value: cellValue
                            });
                            return;
                        }
                        break;
                }

                rowData[col.key] = validatedValue;
            });

            // Only add rows that have some data
            if (hasData) {
                validData.push(rowData);
            }
        });

        return {
            data: validData,
            errors,
            warnings
        };
    }

    /**
     * Download file helper
     */
    private downloadFile(data: any, filename: string, mimeType: string): void {
        const blob = new Blob([data], { type: mimeType });
        const url = window.URL.createObjectURL(blob);

        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();

        // Cleanup
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
    }
}

export const excelService = new ExcelService(); 
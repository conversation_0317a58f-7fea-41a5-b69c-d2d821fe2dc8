import { logger } from '@/lib/logger';
import { formatIndianMobile } from '@/lib/utils';

/**
 * Formats a lead timestamp (milliseconds) to readable format
 * @param timestamp Timestamp in milliseconds from API
 * @returns Formatted date string in IST timezone
 */
export const formatLeadTimestamp = (timestamp: number): string => {
    return new Date(timestamp).toLocaleDateString('en-IN', {
        timeZone: 'Asia/Kolkata',
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
};

/**
 * Formats lead timestamp for display with relative time
 * @param timestamp Timestamp in milliseconds
 * @returns Relative time string (e.g., "2 hours ago", "3 days ago")
 */
export const formatLeadRelativeTime = (timestamp: number): string => {
    const now = Date.now();
    const diffMs = now - timestamp;
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMinutes < 60) {
        return diffMinutes <= 1 ? 'Just now' : `${diffMinutes} minutes ago`;
    } else if (diffHours < 24) {
        return diffHours === 1 ? '1 hour ago' : `${diffHours} hours ago`;
    } else if (diffDays < 7) {
        return diffDays === 1 ? '1 day ago' : `${diffDays} days ago`;
    } else {
        return formatLeadTimestamp(timestamp);
    }
};

/**
 * Gets the appropriate status color for lead status badges
 * @param status Lead status string
 * @returns Tailwind color class for the status
 */
export const getLeadStatusColor = (status: string): string => {
    const colors: Record<string, string> = {
        CONTACTED: 'yellow',
        INTERESTED: 'green',
        CONVERTED: 'emerald',
        LOST: 'red',
        PENDING: 'orange' // API default status
    };
    return colors[status] || 'gray';
};

/**
 * Formats lead mobile number for display (reusing existing utility)
 * @param mobile Mobile number from API in format "************"
 * @returns Formatted mobile number "+91 9791945220"
 */
export const formatLeadMobile = (mobile: string): string => {
    if (!mobile) return mobile;
    // Reuse existing utility but ensure proper formatting for display
    const formatted = formatIndianMobile(mobile);
    // Add space after +91 for better readability
    return formatted.replace('+91', '+91 ');
};

/**
 * Validates if a lead mobile number is in correct format
 * @param mobile Mobile number to validate
 * @returns Boolean indicating if mobile is valid
 */
export const validateLeadMobile = (mobile: string): boolean => {
    if (!mobile) return false;
    // API format: "************" (12 digits starting with 91)
    const apiFormatRegex = /^91[6-9]\d{9}$/;
    return apiFormatRegex.test(mobile);
};

/**
 * Converts display format mobile to API format
 * @param mobile Mobile in display format "+91 9791945220"
 * @returns Mobile in API format "************"
 */
export const convertMobileToApiFormat = (mobile: string): string => {
    if (!mobile) return mobile;
    // Remove +, spaces, and other formatting
    const cleaned = mobile.replace(/[\+\s\-\(\)]/g, '');
    return cleaned;
};

/**
 * Gets default date range for lead filtering (last 30 days)
 * @returns Object with dateFrom and dateTo in YYYY-MM-DD format
 */
export const getDefaultLeadDateRange = (): { dateFrom: string; dateTo: string } => {
    const today = new Date();
    const thirtyDaysAgo = new Date(today);
    thirtyDaysAgo.setDate(today.getDate() - 30);

    return {
        dateFrom: thirtyDaysAgo.toISOString().split('T')[0],
        dateTo: today.toISOString().split('T')[0]
    };
};

/**
 * Converts timestamp to YYYY-MM-DD format for date filtering
 * @param timestamp Timestamp in milliseconds
 * @returns Date string in YYYY-MM-DD format
 */
export const formatDateForLeadFilter = (timestamp: number): string => {
    try {
        return new Date(timestamp).toISOString().split('T')[0];
    } catch (error) {
        logger.error('leadUtils: Error formatting date for filter:', error);
        return new Date().toISOString().split('T')[0];
    }
}; 
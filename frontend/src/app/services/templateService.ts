import { ExcelTemplate, ExcelColumn } from './excelService';

export interface TemplateDefinition {
    id: string;
    name: string;
    description: string;
    category: 'categories' | 'skus' | 'variants' | 'mappings' | 'orders' | 'carts';
    columns: ExcelColumn[];
    sampleData?: any[];
    instructions?: string[];
}

class TemplateService {
    private templates: Map<string, TemplateDefinition> = new Map();

    constructor() {
        this.initializeTemplates();
    }

    private initializeTemplates() {
        // Category List Template
        this.templates.set('category-list', {
            id: 'category-list',
            name: 'Category List',
            description: 'Import/Export category information',
            category: 'categories',
            columns: [
                {
                    key: 'id',
                    header: 'Category ID',
                    type: 'number',
                    width: 12,
                    required: false // Not required for new categories
                },
                {
                    key: 'nameEn',
                    header: 'Name (English)',
                    type: 'string',
                    width: 25,
                    required: true
                },
                {
                    key: 'nameTa',
                    header: 'Name (Tamil)',
                    type: 'string',
                    width: 25,
                    required: false
                },
                {
                    key: 'icon',
                    header: 'Icon',
                    type: 'string',
                    width: 15,
                    required: false
                },
                {
                    key: 'background',
                    header: 'Background Color',
                    type: 'string',
                    width: 15,
                    required: false
                    // Removed strict validation for easier import
                },
                {
                    key: 'status',
                    header: 'Status',
                    type: 'string',
                    width: 12,
                    required: false, // Make optional for easier import
                    validation: {
                        options: ['active', 'inactive']
                    }
                },
                {
                    key: 'skuIds',
                    header: 'SKU IDs',
                    type: 'string',
                    width: 20,
                    required: false // Colon-separated list of SKU IDs
                }
            ],
            sampleData: [
                {
                    id: '',
                    nameEn: 'Electronics',
                    nameTa: 'மின்னணு',
                    icon: 'device-mobile',
                    background: 'transparent',
                    status: 'inactive',
                    skuIds: '101:102:103'
                }
            ],
            instructions: [
                'Required fields: Name (English) - must be provided for all categories',
                'Optional fields: Category ID, Name (Tamil), Icon, Background Color, Status, SKU IDs',
                'Leave Category ID empty for new categories (will be auto-generated)',
                'Name (Tamil) is optional - leave empty if not needed',
                'Icon is optional - leave empty for default icon (folder)',
                'Background Color: Use "transparent" for no background, or hex color codes (e.g., #3B82F6)',
                'Status is optional - defaults to inactive for new categories. Valid values: active, inactive',
                'SKU IDs: Colon-separated list of SKU IDs (e.g., "101:102:103") for category-SKU mappings',
                'Category order is managed through the admin UI drag-and-drop interface',
                'Remove all comment lines (starting with #) before importing'
            ]
        });

        // SKU Complete Template (Parent-Child Architecture)
        this.templates.set('sku-complete', {
            id: 'sku-complete',
            name: 'SKU Complete (Parent-Child)',
            description: 'Import/Export complete SKU data with parent-child relationships',
            category: 'skus',
            columns: [
                {
                    key: 'skuId',
                    header: 'SKU ID',
                    type: 'number',
                    width: 12,
                    required: false // Auto-generated for new SKUs
                },
                {
                    key: 'type',
                    header: 'Type',
                    type: 'string',
                    width: 10,
                    required: false, // Defaults to 'child' if not provided
                    validation: {
                        options: ['parent', 'child']
                    }
                },
                {
                    key: 'parentId',
                    header: 'Parent ID',
                    type: 'number',
                    width: 12,
                    required: false // Only required for child SKUs
                },
                {
                    key: 'nameEn',
                    header: 'Name (English)',
                    type: 'string',
                    width: 30,
                    required: true
                },
                {
                    key: 'nameTa',
                    header: 'Name (Tamil)',
                    type: 'string',
                    width: 30,
                    required: false
                },
                {
                    key: 'variantNameEn',
                    header: 'Variant Name (English)',
                    type: 'string',
                    width: 25,
                    required: false // Only required for child SKUs with variants
                },
                {
                    key: 'variantNameTa',
                    header: 'Variant Name (Tamil)',
                    type: 'string',
                    width: 25,
                    required: false // Only required for child SKUs with variants
                },
                {
                    key: 'descriptionEn',
                    header: 'Description (English)',
                    type: 'string',
                    width: 40,
                    required: false
                },
                {
                    key: 'descriptionTa',
                    header: 'Description (Tamil)',
                    type: 'string',
                    width: 40,
                    required: false
                },
                {
                    key: 'costPrice',
                    header: 'Cost Price',
                    type: 'number',
                    width: 12,
                    required: false, // Not required for parent SKUs
                    validation: {
                        min: 0
                    }
                },
                {
                    key: 'sellingPrice',
                    header: 'Selling Price',
                    type: 'number',
                    width: 12,
                    required: false, // Not required for parent SKUs
                    validation: {
                        min: 0
                    }
                },
                {
                    key: 'mrp',
                    header: 'MRP',
                    type: 'number',
                    width: 12,
                    required: false, // Not required for parent SKUs
                    validation: {
                        min: 0
                    }
                },
                {
                    key: 'imageUrl',
                    header: 'Primary Image URL',
                    type: 'string',
                    width: 50,
                    required: false
                },
                {
                    key: 'images',
                    header: 'Additional Images',
                    type: 'string',
                    width: 60,
                    required: false // Comma-separated URLs
                },
                {
                    key: 'status',
                    header: 'Status',
                    type: 'string',
                    width: 12,
                    required: false,
                    validation: {
                        options: ['active', 'inactive']
                    }
                }
            ],
            sampleData: [
                {
                    skuId: '',
                    type: 'parent',
                    parentId: '',
                    nameEn: 'Fresh Apples',
                    nameTa: 'புதிய ஆப்பிள்கள்',
                    variantNameEn: '', // Empty for parent SKUs
                    variantNameTa: '', // Empty for parent SKUs
                    descriptionEn: 'Fresh and crispy apples',
                    descriptionTa: 'புதிய மற்றும் மொறுமொறுப்பான ஆப்பிள்கள்',
                    costPrice: '',
                    sellingPrice: '',
                    mrp: '',
                    imageUrl: 'https://example.com/apple.jpg',
                    images: 'https://example.com/apple1.jpg,https://example.com/apple2.jpg',
                    status: 'active'
                },
                {
                    skuId: '',
                    type: 'child',
                    parentId: 100,
                    nameEn: 'Fresh Apples - 500g',
                    nameTa: 'புதிய ஆப்பிள்கள் - 500g',
                    variantNameEn: '500g Pack',
                    variantNameTa: '500g பேக்',
                    descriptionEn: 'Fresh apples approximately 500g (3-4 pieces)',
                    descriptionTa: 'புதிய ஆப்பிள்கள் தோராயமாக 500g (3-4 துண்டுகள்)',
                    costPrice: 80,
                    sellingPrice: 100,
                    mrp: 120,
                    imageUrl: '',
                    images: '',
                    status: 'active'
                }
            ],
            instructions: [
                'Required fields: Name (English) - must be provided for all SKUs',
                'Optional fields: SKU ID, Type, Parent ID, Name (Tamil), Variant Name (English/Tamil), Description (English/Tamil), Cost Price, Selling Price, MRP, Image URL, Images, Status',
                'Leave SKU ID empty for new SKUs (will be auto-generated)',
                'Type values: parent, child (defaults to child if not specified)',
                'Parent ID: Required for child SKUs that belong to a parent',
                'Variant Name: Use for child SKUs to specify variant details (e.g., "500g", "1kg", "Red Color")',
                'For parent SKUs: Leave Variant Name, Cost Price, Selling Price, and MRP empty',
                'For child SKUs: Provide Cost Price, Selling Price, and MRP',
                'Status values: active, inactive (defaults to active if not specified)',
                'Images: Comma-separated list of image URLs',
                'Remove all comment lines (starting with #) before importing'
            ]
        });

        // Cart List Template  
        this.templates.set('cart-list', {
            id: 'cart-list',
            name: 'Cart List',
            description: 'Export cart information with customer and SKU details',
            category: 'carts',
            columns: [
                {
                    key: 'cartId',
                    header: 'Cart ID',
                    type: 'string',
                    width: 15,
                    required: false
                },
                {
                    key: 'status',
                    header: 'Status',
                    type: 'string',
                    width: 15,
                    required: false
                },
                {
                    key: 'customerName',
                    header: 'Customer Name',
                    type: 'string',
                    width: 25,
                    required: false
                },
                {
                    key: 'customerMobile',
                    header: 'Customer Mobile',
                    type: 'string',
                    width: 15,
                    required: false
                },
                {
                    key: 'itemsCount',
                    header: 'Items Count',
                    type: 'number',
                    width: 12,
                    required: false
                },
                {
                    key: 'totalValue',
                    header: 'Total Value (₹)',
                    type: 'number',
                    width: 15,
                    required: false
                },
                {
                    key: 'createdAt',
                    header: 'Created At',
                    type: 'string',
                    width: 20,
                    required: false
                },
                {
                    key: 'skuDetails',
                    header: 'SKU Details',
                    type: 'string',
                    width: 60,
                    required: false
                },
                {
                    key: 'skuIds',
                    header: 'SKU IDs',
                    type: 'string',
                    width: 30,
                    required: false
                },
                {
                    key: 'customerLocation',
                    header: 'Customer Location',
                    type: 'string',
                    width: 30,
                    required: false
                },
                {
                    key: 'customerAddress',
                    header: 'Customer Address',
                    type: 'string',
                    width: 40,
                    required: false
                }
            ],
            instructions: [
                'Cart export contains current cart status and customer information',
                'SKU Details column shows all items in the cart with quantities and prices',
                'Location coordinates are in format: latitude, longitude',
                'Total Value is calculated using selling prices of items'
            ]
        });

        // Order List Template
        this.templates.set('order-list', {
            id: 'order-list',
            name: 'Order List',
            description: 'Export order information with customer and SKU details',
            category: 'orders',
            columns: [
                {
                    key: 'orderId',
                    header: 'Order ID',
                    type: 'number',
                    width: 12,
                    required: false
                },
                {
                    key: 'status',
                    header: 'Status',
                    type: 'string',
                    width: 15,
                    required: false
                },
                {
                    key: 'mobile',
                    header: 'Mobile',
                    type: 'string',
                    width: 15,
                    required: false
                },
                {
                    key: 'facilityKey',
                    header: 'Facility',
                    type: 'string',
                    width: 15,
                    required: false
                },
                {
                    key: 'itemsCount',
                    header: 'Items Count',
                    type: 'number',
                    width: 12,
                    required: false
                },
                {
                    key: 'totalValue',
                    header: 'Total Value (₹)',
                    type: 'number',
                    width: 15,
                    required: false
                },
                {
                    key: 'createdAt',
                    header: 'Created At',
                    type: 'string',
                    width: 20,
                    required: false
                },
                {
                    key: 'deliveryDate',
                    header: 'Delivery Date',
                    type: 'string',
                    width: 20,
                    required: false
                },
                {
                    key: 'skuDetails',
                    header: 'SKU Details',
                    type: 'string',
                    width: 60,
                    required: false
                },
                {
                    key: 'skuIds',
                    header: 'SKU IDs',
                    type: 'string',
                    width: 30,
                    required: false
                },
                {
                    key: 'deliveryLocation',
                    header: 'Delivery Location',
                    type: 'string',
                    width: 30,
                    required: false
                },
                {
                    key: 'orderComments',
                    header: 'Order Comments',
                    type: 'string',
                    width: 40,
                    required: false
                },
                {
                    key: 'deliveryInstructions',
                    header: 'Delivery Instructions',
                    type: 'string',
                    width: 40,
                    required: false
                }
            ],
            sampleData: [
                {
                    orderId: 12345,
                    status: 'CONFIRMED',
                    mobile: '+************',
                    facilityKey: 'FACILITY_001',
                    itemsCount: 3,
                    totalValue: 250.50,
                    createdAt: '15 Jan 2024, 10:30',
                    deliveryDate: '15 Jan 2024',
                    skuDetails: 'SKU#123: Rice 2kg (Qty: 2, ₹50 each), SKU#456: Oil 1L (Qty: 1, ₹80 each)',
                    skuIds: '123,456',
                    deliveryLocation: '12.9716,77.5946',
                    orderComments: 'Handle with care',
                    deliveryInstructions: 'Call before delivery'
                }
            ],
            instructions: [
                'This file contains exported order data from the admin portal',
                'Order data includes customer information, items, and delivery details',
                'SKU Details: Detailed breakdown of all items in the order with quantities and prices',
                'SKU IDs: Comma-separated list of SKU IDs for easy reference',
                'Mobile: Customer mobile number in Indian format (+91xxxxxxxxxx)',
                'Total Value: Sum of all items in the order (price × quantity)',
                'Created At: Order creation timestamp in IST timezone',
                'Delivery Date: Expected delivery date',
                'Delivery Location: GPS coordinates (latitude,longitude)',
                'All monetary values are in Indian Rupees (₹)',
                'Export includes both filtered and complete order information based on your selection'
            ]
        });
    }

    /**
     * Get template by ID
     */
    getTemplate(templateId: string): TemplateDefinition | undefined {
        return this.templates.get(templateId);
    }

    /**
     * Get all templates for a category
     */
    getTemplatesByCategory(category: 'categories' | 'skus' | 'variants' | 'mappings' | 'orders' | 'carts'): TemplateDefinition[] {
        return Array.from(this.templates.values()).filter(template => template.category === category);
    }

    /**
     * Get all templates
     */
    getAllTemplates(): TemplateDefinition[] {
        return Array.from(this.templates.values());
    }

    /**
     * Convert template to ExcelTemplate format
     */
    toExcelTemplate(templateId: string): ExcelTemplate | undefined {
        const template = this.getTemplate(templateId);
        if (!template) return undefined;

        return {
            name: template.name,
            columns: template.columns,
            sampleData: template.sampleData,
            instructions: template.instructions
        };
    }

    /**
     * Get template options for import/export dropdowns
     */
    getTemplateOptions(category: 'categories' | 'skus' | 'variants' | 'mappings' | 'orders' | 'carts') {
        return this.getTemplatesByCategory(category).map(template => ({
            id: template.id,
            label: template.name,
            description: template.description,
            templateType: template.id
        }));
    }
}

export const templateService = new TemplateService(); 
import { Category, CategoryMap, UpsertCategoriesRequest, UpsertCategoryPayload, CategoryApiResponseData } from '../types/category';
import { CategoryRepository } from '../repository/CategoryRepository';
import axiosInstance from '../../lib/axios';
import { logger } from '../../lib/logger';
import { v4 as uuidv4 } from 'uuid';

// ============================================================================
// TYPES FOR SERVICE OPTIONS
// ============================================================================

export interface CategoryServiceOptions {
    forceRefresh?: boolean;
    includeInactive?: boolean; // For admin pages that need inactive categories
}

// ============================================================================
// ENHANCED CATEGORY SERVICE METHODS
// ============================================================================

/**
 * Get all categories in hierarchical order (parent -> children) sorted by orderNo
 * Recommended method for displaying categories in UI
 * @param options Service options
 * @returns Promise that resolves to ordered categories array
 */
export const getOrderedCategories = async (options: CategoryServiceOptions = {}): Promise<Category[]> => {
    const repo = CategoryRepository.getInstance();
    let categories: CategoryMap | null = null;

    if (options.forceRefresh) {
        // Force refresh: Always fetch fresh data from API, bypass cache
        logger.info('CategoryService: Force refresh - fetching fresh data from API');
        const responseData = await axiosInstance.post('/userApp-infinity-getCategories') as CategoryApiResponseData;

        if (responseData && responseData.categories) {
            categories = responseData.categories;
            // Update cache with fresh data
            await repo.saveCategories(categories);
            logger.info('CategoryService: Fresh data fetched from API and cache updated');
        }
    } else {
        // Cache-first strategy: Try cache first, then API if cache miss
        categories = await repo.getAllCategories();
        if (categories) {
            logger.info('CategoryService: Categories loaded from cache');
        } else {
            // Cache miss - fetch from API
            logger.info('CategoryService: Cache miss. Fetching categories from API');
            const responseData = await axiosInstance.post('/userApp-infinity-getCategories') as CategoryApiResponseData;

            if (responseData && responseData.categories) {
                categories = responseData.categories;
                await repo.saveCategories(categories);
                logger.info('CategoryService: Categories fetched from API and cached');
            }
        }
    }

    // Handle null case
    if (!categories) return [];

    let categoriesArray = Object.values(categories);

    // Filter out inactive categories unless explicitly requested (for admin)
    if (!options.includeInactive) {
        categoriesArray = categoriesArray.filter(category => category.isActive === 1);
    }

    // Sort by orderNo to maintain display order
    return categoriesArray.sort((a, b) => (a.orderNo || 0) - (b.orderNo || 0));
};

/**
 * Get a category by its ID with caching
 * @param categoryId Category ID to fetch
 * @param options Service options
 * @returns Promise that resolves to category or null if not found
 */
export const getCategoryById = async (categoryId: string, options: CategoryServiceOptions = {}): Promise<Category | null> => {
    const repo = CategoryRepository.getInstance();

    // First try cache
    let category = await repo.getCategoryById(categoryId);
    if (category) {
        logger.debug(`CategoryService: Category ${categoryId} loaded from cache`);
        return category;
    }

    // Cache miss - ensure categories are loaded via getOrderedCategories (handles API fallback)
    logger.info(`CategoryService: Category ${categoryId} not in cache, loading all categories`);
    await getOrderedCategories(options);

    // Try cache again after ensuring data is loaded
    category = await repo.getCategoryById(categoryId);
    if (category) {
        logger.info(`CategoryService: Category ${categoryId} found after loading from API`);
    } else {
        logger.warn(`CategoryService: Category ${categoryId} not found even after API fetch`);
    }

    return category;
};

/**
 * Get categories as a map for efficient lookups
 * @param options Service options
 * @returns Promise that resolves to CategoryMap
 */
export const getCategoryMap = async (options: CategoryServiceOptions = {}): Promise<CategoryMap> => {
    const repo = CategoryRepository.getInstance();
    const categories = await repo.getAllCategories();
    return categories || {};
};

/**
 * Search categories by name (separate from SKU search for better separation of concerns)
 * @param query Search query
 * @param options Search options
 * @returns Promise that resolves to matching categories
 */
export const searchCategories = async (
    query: string,
    options?: { language?: string; maxResults?: number }
): Promise<Category[]> => {
    const categories = await getOrderedCategories();
    const searchQuery = query.toLowerCase().trim();
    if (!searchQuery) return [];

    const language = options?.language || 'en';
    const maxResults = options?.maxResults || 10;

    return categories
        .filter(category => {
            const nameInLang = category.name[language] || category.name.en || '';
            return nameInLang.toLowerCase().includes(searchQuery);
        })
        .slice(0, maxResults);
};

// ============================================================================
// ADMIN OPERATIONS (Single Upsert Endpoint)
// ============================================================================

/**
 * Transforms Category array to the API format required for upsert operation
 * @param categories Array of Category objects to transform
 * @returns Array in the format expected by the upsert API
 */
const transformCategoriesToUpsertFormat = (categories: Category[]): UpsertCategoriesRequest => {
    return categories.map((category, index): UpsertCategoryPayload => {
        return {
            ...(category.id > 0 && { id: category.id }), // Include ID for existing categories
            isActive: category.isActive,
            orderNo: index + 1, // Use array index for order (1-based)
            categoryJson: {
                icon: category.icon,
                name: category.name,
                skuIds: category.skuIds,
                background: category.background
            },
            versionUuid: category.versionUuid
        };
    });
};

/**
 * Upserts (creates, updates, or soft deletes) categories via the backend API
 * This is the ONLY admin method needed - handles all operations
 * @param categories Array of Category objects to upsert
 * @returns Promise that resolves when the operation is complete
 */
export const upsertCategories = async (categories: Category[]): Promise<void> => {
    // Transform categories to the required API format
    const requestPayload = transformCategoriesToUpsertFormat(categories);

    logger.info('CategoryService: Upserting categories', { count: categories.length });

    // Make the API call to upsert endpoint
    await axiosInstance.post('/userApp-infinity-upsertCategories', requestPayload);

    // Force refresh cache after upsert
    const repo = CategoryRepository.getInstance();
    await repo.clearAllCategories();

    logger.info('CategoryService: Categories upserted successfully');
};
// All deprecated methods have been removed as they are no longer used


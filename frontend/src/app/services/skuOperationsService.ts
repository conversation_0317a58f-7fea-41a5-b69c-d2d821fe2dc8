import { upsertSkus, getSkuById } from '@/app/services/skuService';
import { getOrderedCategories, upsertCategories } from '@/app/services/categoryService';
import type { SKU, SKUVariant } from '@/app/types/sku';
import type { Category } from '@/app/types/category';
import { logger } from '@/lib/logger';
import { v4 as uuidv4 } from 'uuid';

/**
 * Bulk operation types for different operations
 */
export interface BulkOperation {
    type: 'statusChange' | 'bulkEdit';
    newStatus?: 'active' | 'inactive';
    fields?: Partial<SKU>;
}

/**
 * Unified SKU Operations Service
 * 
 * This service consolidates all SKU save, update, and bulk operations to ensure:
 * - Consistent data transformation across all pages
 * - Proper parent-child synchronization
 * - Complete category relationship management
 * - No missing fields like variantName
 */
export class SkuOperationsService {

    /**
     * Saves a single SKU with complete synchronization
     * Used by edit/create pages and replaces SkuFormService.saveSku
     */
    static async saveSingleSku(
        skuData: SKU,
        selectedCategoryIds: number[],
        originalSku?: SKU
    ): Promise<void> {
        logger.info('SkuOperationsService: Starting single SKU save operation', {
            skuId: skuData.skuId,
            type: skuData.type,
            hasParent: !!skuData.parentId,
            categoryCount: selectedCategoryIds.length
        });

        try {
            // Step 1: Validate SKU data
            this.validateSkuData(skuData);

            // Step 2: Validate category requirements for discoverability
            this.validateCategoryRequirements(skuData, selectedCategoryIds);

            const skusToUpdate: SKU[] = [];
            const categoriesToUpdate: Category[] = [];

            // Step 3: Add the main SKU to update list
            skusToUpdate.push(skuData);

            // Step 4: Handle parent-child synchronization
            if (skuData.type === 'child' && skuData.parentId) {
                await this.syncParentChildRelations(skuData, skusToUpdate, 'update');
            }

            // Step 5: Handle orphan conversion (child SKU losing parent)
            if (originalSku && originalSku.parentId && (!skuData.parentId || skuData.parentId !== originalSku.parentId)) {
                await this.syncParentChildRelations(originalSku, skusToUpdate, 'remove');
            }

            // Step 6: Handle category updates
            await this.syncCategoryRelations(skuData, selectedCategoryIds, categoriesToUpdate);

            // Step 7: Execute all updates
            if (skusToUpdate.length > 0) {
                logger.info('SkuOperationsService: Executing SKU upsert', {
                    skuCount: skusToUpdate.length,
                    skuIds: skusToUpdate.map(s => s.skuId)
                });
                await upsertSkus(skusToUpdate);
            }

            if (categoriesToUpdate.length > 0) {
                logger.info('SkuOperationsService: Executing category updates', {
                    categoryCount: categoriesToUpdate.length,
                    categoryIds: categoriesToUpdate.map(c => c.id)
                });
                try {
                    await upsertCategories(categoriesToUpdate);
                } catch (error) {
                    logger.error('SkuOperationsService: Category update failed but continuing', {
                        error: error?.toString(),
                        categoryIds: categoriesToUpdate.map(c => c.id)
                    });
                    // Don't throw - SKU save is more critical than category sync
                }
            }

            logger.info('SkuOperationsService: Single SKU save completed successfully', {
                skuId: skuData.skuId,
                skusUpdated: skusToUpdate.length,
                categoriesUpdated: categoriesToUpdate.length
            });

        } catch (error) {
            logger.error('SkuOperationsService: Single SKU save failed', {
                skuId: skuData.skuId,
                error: error?.toString()
            });
            throw error;
        }
    }

    /**
     * Saves multiple SKUs with bulk synchronization
     * Used by homepage bulk operations
     */
    static async saveBulkSkus(skus: SKU[], operation: BulkOperation): Promise<void> {
        logger.info('SkuOperationsService: Starting bulk SKU operation', {
            skuCount: skus.length,
            operationType: operation.type,
            skuIds: skus.map(s => s.skuId)
        });

        try {
            // Step 1: Validate all SKUs in bulk
            const validationErrors: string[] = [];
            skus.forEach((sku, index) => {
                try {
                    this.validateSkuData(sku);
                } catch (error) {
                    validationErrors.push(`SKU ${sku.skuId} (index ${index}): ${error?.toString()}`);
                }
            });

            if (validationErrors.length > 0) {
                throw new Error(`Bulk validation failed: ${validationErrors.join(', ')}`);
            }

            // Step 2: Apply operation to SKUs
            const updatedSkus = this.applyBulkOperation(skus, operation);

            // Step 3: Identify parent SKUs that need variant updates
            const childSkusWithParents = updatedSkus.filter(sku => sku.type === 'child' && sku.parentId);
            const parentIdsToUpdate = new Set(childSkusWithParents.map(sku => sku.parentId!));

            // Step 4: Synchronize parent variants
            const parentUpdates: SKU[] = [];
            for (const parentId of parentIdsToUpdate) {
                try {
                    const parentSku = await getSkuById(parentId);
                    if (parentSku && parentSku.type === 'parent') {
                        const updatedParent = this.updateParentVariants(
                            parentSku,
                            childSkusWithParents.filter(child => child.parentId === parentId)
                        );
                        parentUpdates.push(updatedParent);
                    }
                } catch (error) {
                    logger.warn('SkuOperationsService: Failed to update parent during bulk operation', {
                        parentId,
                        error: error?.toString()
                    });
                }
            }

            // Step 5: Combine all updates
            const allSkusToUpdate = [...updatedSkus, ...parentUpdates];

            // Step 6: Execute bulk upsert
            if (allSkusToUpdate.length > 0) {
                logger.info('SkuOperationsService: Executing bulk upsert', {
                    totalSkus: allSkusToUpdate.length,
                    children: updatedSkus.length,
                    parents: parentUpdates.length
                });
                await upsertSkus(allSkusToUpdate);
            }

            logger.info('SkuOperationsService: Bulk operation completed successfully', {
                operationType: operation.type,
                skusProcessed: skus.length,
                totalUpdated: allSkusToUpdate.length
            });

        } catch (error) {
            logger.error('SkuOperationsService: Bulk operation failed', {
                operationType: operation.type,
                skuCount: skus.length,
                error: error?.toString()
            });
            throw error;
        }
    }

    /**
     * Updates SKU status with proper parent-child synchronization
     * Used for status change operations
     */
    static async updateSkuStatus(skuIds: number[], newStatus: 'active' | 'inactive'): Promise<void> {
        logger.info('SkuOperationsService: Starting status update operation', {
            skuIds,
            newStatus,
            count: skuIds.length
        });

        try {
            // Fetch all SKUs to update
            const skusToUpdate: SKU[] = [];
            for (const skuId of skuIds) {
                try {
                    const sku = await getSkuById(skuId);
                    if (sku) {
                        skusToUpdate.push({
                            ...sku,
                            isActive: newStatus === 'active' ? 1 : 0
                        });
                    }
                } catch (error) {
                    logger.warn('SkuOperationsService: Failed to fetch SKU for status update', {
                        skuId,
                        error: error?.toString()
                    });
                }
            }

            if (skusToUpdate.length === 0) {
                logger.warn('SkuOperationsService: No SKUs found to update status');
                return;
            }

            // Use bulk operation for consistency
            const operation: BulkOperation = {
                type: 'statusChange',
                newStatus
            };

            await this.saveBulkSkus(skusToUpdate, operation);

        } catch (error) {
            logger.error('SkuOperationsService: Status update failed', {
                skuIds,
                newStatus,
                error: error?.toString()
            });
            throw error;
        }
    }

    /**
     * PRIVATE METHODS
     */

    /**
     * Validates SKU data completeness
     */
    private static validateSkuData(sku: SKU): void {
        const errors: string[] = [];

        // Required fields for all SKUs
        if (!sku.name?.en?.trim()) {
            errors.push('English name is required');
        }

        // Type-specific validation
        if (sku.type === 'child') {
            // Child SKUs with parents should have variant names
            if (sku.parentId && !sku.variantName?.en?.trim()) {
                errors.push('Variant name is required for child SKUs with parents');
            }

            // Child SKUs should have pricing
            if (sku.costPrice === undefined || sku.sellingPrice === undefined || sku.mrp === undefined) {
                errors.push('Child SKUs must have cost price, selling price, and MRP');
            }
        } else if (sku.type === 'parent') {
            // Parent SKUs should not have pricing
            if (sku.costPrice || sku.sellingPrice || sku.mrp) {
                errors.push('Parent SKUs should not have pricing information');
            }
        }

        if (errors.length > 0) {
            throw new Error(`Validation failed: ${errors.join(', ')}`);
        }
    }

    /**
     * Validates category requirements for SKU discoverability
     */
    private static validateCategoryRequirements(sku: SKU, selectedCategoryIds: number[]): void {
        const errors: string[] = [];

        if (sku.type === 'parent' && selectedCategoryIds.length === 0) {
            errors.push('Parent SKUs must be assigned to at least one category for discoverability');
        }

        if (sku.type === 'child' && !sku.parentId && selectedCategoryIds.length === 0) {
            errors.push('Child SKUs without parents must be assigned to at least one category for discoverability');
        }

        if (errors.length > 0) {
            throw new Error(`Category validation failed: ${errors.join(', ')}`);
        }
    }

    /**
     * Handles parent-child relationship synchronization
     */
    private static async syncParentChildRelations(
        childSku: SKU,
        skusToUpdate: SKU[],
        operation: 'update' | 'remove'
    ): Promise<void> {
        if (!childSku.parentId) return;

        try {
            const parentSku = await getSkuById(childSku.parentId);
            if (!parentSku || parentSku.type !== 'parent') {
                logger.warn('SkuOperationsService: Parent SKU not found or invalid type', {
                    parentId: childSku.parentId,
                    childId: childSku.skuId
                });
                return;
            }

            let updatedVariants = [...(parentSku.variants || [])];

            if (operation === 'update') {
                // Create or update variant from child SKU
                const variant: SKUVariant = {
                    skuId: childSku.skuId,
                    name: childSku.name,
                    costPrice: childSku.costPrice || 0,
                    sellingPrice: childSku.sellingPrice || 0,
                    mrp: childSku.mrp || 0,
                    type: 'child',
                    variantName: childSku.variantName || childSku.name,
                    isActive: childSku.isActive ?? 1,
                    // Optional overrides (only include if child has different values from parent)
                    ...(childSku.imageUrl && childSku.imageUrl !== parentSku.imageUrl && { imageUrl: childSku.imageUrl }),
                    ...(childSku.images && childSku.images.length > 0 && { images: childSku.images }),
                    ...(childSku.description && (childSku.description.en !== parentSku.description?.en || childSku.description.ta !== parentSku.description?.ta) && { description: childSku.description }),
                };

                const existingVariantIndex = updatedVariants.findIndex(v => v.skuId === childSku.skuId);
                if (existingVariantIndex >= 0) {
                    updatedVariants[existingVariantIndex] = variant;
                } else {
                    updatedVariants.push(variant);
                }
            } else if (operation === 'remove') {
                // Remove variant from parent
                updatedVariants = updatedVariants.filter(v => v.skuId !== childSku.skuId);
            }

            // Add updated parent to the update list
            const updatedParent: SKU = {
                ...parentSku,
                variants: updatedVariants
            };

            // Check if parent is already in the update list and replace it
            const existingParentIndex = skusToUpdate.findIndex(s => s.skuId === parentSku.skuId);
            if (existingParentIndex >= 0) {
                skusToUpdate[existingParentIndex] = updatedParent;
            } else {
                skusToUpdate.push(updatedParent);
            }

        } catch (error) {
            logger.error('SkuOperationsService: Parent-child sync failed', {
                parentId: childSku.parentId,
                childId: childSku.skuId,
                operation,
                error: error?.toString()
            });
            // Don't throw - child save is more important than parent sync
        }
    }

    /**
     * Handles bidirectional category-SKU relationship updates
     */
    private static async syncCategoryRelations(
        skuData: SKU,
        selectedCategoryIds: number[],
        categoriesToUpdate: Category[]
    ): Promise<void> {
        try {
            const allCategories = await getOrderedCategories({ includeInactive: true });

            // Find categories that currently contain this SKU
            const currentCategories = allCategories.filter(cat => cat.skuIds.includes(skuData.skuId));
            const currentCategoryIds = currentCategories.map(cat => cat.id);

            // Find categories to add SKU to (newly selected)
            const categoriesToAddTo = selectedCategoryIds.filter(id => !currentCategoryIds.includes(id));

            // Find categories to remove SKU from (deselected)
            const categoriesToRemoveFrom = currentCategoryIds.filter(id => !selectedCategoryIds.includes(id));

            // Add SKU to new categories
            categoriesToAddTo.forEach(categoryId => {
                const category = allCategories.find(cat => cat.id === categoryId);
                if (category) {
                    const updatedCategory: Category = {
                        ...category,
                        skuIds: [...category.skuIds, skuData.skuId],
                        versionUuid: uuidv4() // Update version for tracking
                    };
                    categoriesToUpdate.push(updatedCategory);
                }
            });

            // Remove SKU from deselected categories
            categoriesToRemoveFrom.forEach(categoryId => {
                const category = allCategories.find(cat => cat.id === categoryId);
                if (category) {
                    const updatedCategory: Category = {
                        ...category,
                        skuIds: category.skuIds.filter(id => id !== skuData.skuId),
                        versionUuid: uuidv4() // Update version for tracking
                    };
                    categoriesToUpdate.push(updatedCategory);
                }
            });

        } catch (error) {
            logger.error('SkuOperationsService: Category sync failed', {
                skuId: skuData.skuId,
                selectedCategoryIds,
                error: error?.toString()
            });
            // Don't throw - SKU save is more important than category sync
        }
    }

    /**
     * Applies bulk operation to SKUs
     */
    private static applyBulkOperation(skus: SKU[], operation: BulkOperation): SKU[] {
        return skus.map(sku => {
            let updatedSku = { ...sku };

            switch (operation.type) {
                case 'statusChange':
                    updatedSku.isActive = operation.newStatus === 'active' ? 1 : 0;
                    break;
                case 'bulkEdit':
                    if (operation.fields) {
                        updatedSku = { ...updatedSku, ...operation.fields };
                    }
                    break;
            }

            return updatedSku;
        });
    }

    /**
     * Updates parent SKU variants based on child SKU changes
     */
    private static updateParentVariants(parentSku: SKU, childSkus: SKU[]): SKU {
        const updatedVariants = [...(parentSku.variants || [])];

        childSkus.forEach(childSku => {
            const variant: SKUVariant = {
                skuId: childSku.skuId,
                name: childSku.name,
                costPrice: childSku.costPrice || 0,
                sellingPrice: childSku.sellingPrice || 0,
                mrp: childSku.mrp || 0,
                type: 'child',
                variantName: childSku.variantName || childSku.name,
                isActive: childSku.isActive ?? 1,
                // Optional overrides
                ...(childSku.imageUrl && childSku.imageUrl !== parentSku.imageUrl && { imageUrl: childSku.imageUrl }),
                ...(childSku.images && childSku.images.length > 0 && { images: childSku.images }),
                ...(childSku.description && (childSku.description.en !== parentSku.description?.en || childSku.description.ta !== parentSku.description?.ta) && { description: childSku.description }),
            };

            const existingVariantIndex = updatedVariants.findIndex(v => v.skuId === childSku.skuId);
            if (existingVariantIndex >= 0) {
                updatedVariants[existingVariantIndex] = variant;
            } else {
                updatedVariants.push(variant);
            }
        });

        return {
            ...parentSku,
            variants: updatedVariants
        };
    }

    /**
     * Transforms SKUs for API compatibility with all required fields
     * This ensures no fields like variantName are missing
     */
    private static transformForApi(skus: SKU[]): SKU[] {
        return skus.map(sku => {
            const transformed = { ...sku };

            // Ensure all required fields are present
            if (sku.type === 'child') {
                // Ensure variantName is included for child SKUs
                if (!transformed.variantName && transformed.name) {
                    transformed.variantName = transformed.name;
                }
            }

            // Ensure variants have all required fields
            if (sku.type === 'parent' && sku.variants) {
                transformed.variants = sku.variants.map(variant => ({
                    ...variant,
                    variantName: variant.variantName || variant.name,
                    isActive: variant.isActive ?? 1
                }));
            }

            return transformed;
        });
    }
} 
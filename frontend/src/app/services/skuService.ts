import axiosInstance from '@/lib/axios';
import { SkuRepository } from '@/app/repository/SkuRepository';
import type { SKU, SKUMap, SKUApiResponseData, UpsertSKUsRequest, UpsertSKUPayload, SKUJsonPayload } from '@/app/types/sku';
import type { Category } from '@/app/types/category';
import { logger } from '@/lib/logger';

const SKUS_API_PATH = '/userApp-infinity-getSkus';

const skuRepository = SkuRepository.getInstance();

// Dependency injection for categoryService
interface CategoryServiceInterface {
  getOrderedCategories(): Promise<Category[]>;
  getCategoryById(id: string): Promise<Category | null>;
}

let categoryServiceInstance: CategoryServiceInterface | null = null;

export const setCategoryService = (categoryService: CategoryServiceInterface): void => {
  categoryServiceInstance = categoryService;
};

const getCategoryService = (): CategoryServiceInterface => {
  if (!categoryServiceInstance) {
    throw new Error('CategoryService not injected. Call setCategoryService() first.');
  }
  return categoryServiceInstance;
};

// Type definitions for new API
interface BaseOptions {
  forceRefresh?: boolean;
  language?: string;
  allowInactive?: boolean; // Client-side filtering for inactive SKUs (default: false)
}

interface GetSkusOptions extends BaseOptions {
  categoryIds?: number[];
  skuIds?: number[];
  limit?: number;
  allowInactive?: boolean; // Client-side filtering for inactive SKUs (default: false)
}

// Progressive search types
interface SearchProgress {
  skus: SKU[];
  isComplete: boolean;
  loadedCategories: number;
  totalCategories: number;
  message?: string;
}

type SearchProgressCallback = (progress: SearchProgress) => void;

/**
 * Convert backend API response to frontend format, handling duplicates
 */
function convertSkuApiToFrontend(backendSkus: Record<string, SKU>): SKUMap {
  const skuArray = Object.values(backendSkus);
  const skuMap = new Map<string, SKU>();

  // First pass: identify parent SKUs and their variants
  const parentSkus = skuArray.filter(sku => sku.type === 'parent');
  const childSkuIds = new Set<number>();

  // Collect all child SKU IDs that are variants of parents
  parentSkus.forEach(parent => {
    if (parent.variants && parent.variants.length > 0) {
      parent.variants.forEach(variant => {
        childSkuIds.add(variant.skuId);
      });
    }
  });

  // Second pass: convert SKUs, but skip standalone child SKUs that are already variants
  skuArray.forEach(backendSku => {
    // Skip standalone child SKUs that are already included as variants
    if (backendSku.type === 'child' && childSkuIds.has(backendSku.skuId)) {
      logger.log(`SkuService: Skipping standalone child SKU ${backendSku.skuId} as it's already a variant`);
      return;
    }

    const frontendSku = backendSku;
    skuMap.set(String(frontendSku.skuId), frontendSku);
  });

  // Third pass: For admin interface, expand variants into separate child SKU entries
  // This allows the admin page to display and manage parent-child relationships properly
  parentSkus.forEach(parent => {
    if (parent.variants && parent.variants.length > 0) {
      parent.variants.forEach(variant => {
        // Log variant name transformation for debugging
        if (variant.variantName && variant.name && variant.variantName.en !== variant.name.en) {
          logger.log(`SkuService: Preserving backend variantName for SKU ${variant.skuId}: "${variant.variantName.en}" (was going to use name: "${variant.name.en}")`);
        }
        // Create a separate child SKU entry for each variant
        const childSku: SKU = {
          skuId: variant.skuId,
          name: {
            en: variant.name?.en || '',
            ta: variant.name?.ta || ''
          },
          description: {
            en: parent.description?.en || '',
            ta: parent.description?.ta || ''
          },
          type: 'child',
          parentId: parent.skuId,
          isActive: variant.isActive ?? 1, // Preserve backend isActive value, default to 1 if undefined
          costPrice: variant.costPrice || 0,
          sellingPrice: variant.sellingPrice || 0,
          mrp: variant.mrp || 0,
          imageUrl: parent.imageUrl || '', // Inherit from parent
          images: parent.images || [], // Inherit from parent
          variants: [], // Child SKUs don't have variants
          variantName: variant.variantName || variant.name, // Preserve backend variantName, fallback to name
        };

        skuMap.set(String(childSku.skuId), childSku);
      });
    }
  });

  return Object.fromEntries(skuMap);
}

// ===================================================================
// HELPER FUNCTIONS
// ===================================================================

/**
 * Validates and normalizes SKU ID input
 */
function validateSkuId(skuId: unknown): number | null {
  if (!skuId || skuId === '0' || skuId === 0) return null;
  const id = typeof skuId === 'string' ? parseInt(skuId, 10) : Number(skuId);
  return isNaN(id) ? null : id;
}

/**
 * Calls SKU API with given parameters
 */
async function callSkuApi(params: Record<string, any>): Promise<Record<string, SKU> | null> {
  try {
    logger.log('SkuService: Making API call with params:', params);
    const response = await axiosInstance.post(SKUS_API_PATH, params) as SKUApiResponseData;

    if (response.skus) {
      return response.skus as Record<string, SKU>;
    }

    logger.warn('SkuService: API response missing skus data');
    return null;
  } catch (error) {
    logger.error('SkuService: API call failed:', error);
    return null;
  }
}

/**
 * Filters SKUs to only include active ones (client-side filtering)
 */
function filterActiveSkus(skus: SKU[]): SKU[] {
  return skus.filter(sku => sku.isActive === 1);
}

/**
 * Applies filtering based on options
 */
function applySkuFiltering(skus: SKU[], options?: GetSkusOptions): SKU[] {
  if (!options?.allowInactive) {
    return filterActiveSkus(skus);
  }
  return skus;
}

// ===================================================================
// PRIVATE FETCH FUNCTIONS - API-ONLY, NO CACHE LOGIC
// ===================================================================

/**
 * Private: Fetch SKUs from API with given parameters (NO CACHE LOGIC)
 * @param params Fetch parameters
 * @returns Promise resolving to SKUMap or null
 */
async function fetchSkusFromApi(params?: {
  categoryIds?: (number | string)[];
  skuIds?: (number | string)[];
}): Promise<SKUMap | null> {
  try {
    let apiParams: Record<string, any> = {};

    // No params = fetch all SKUs via all categories
    if (!params) {
      const categoryService = getCategoryService();
      const categories = await categoryService.getOrderedCategories();
      const categoryIds = categories.map(cat => cat.id);
      apiParams = { categoryIds };
    }
    // Category-specific fetch - USE BATCH API CALL
    else if (params.categoryIds && params.categoryIds.length > 0) {
      const validCategoryIds = params.categoryIds
        .map(id => typeof id === 'string' ? parseInt(id, 10) : Number(id))
        .filter(id => !isNaN(id));

      if (validCategoryIds.length === 0) return null;
      apiParams = { categoryIds: validCategoryIds };
    }
    // SKU ID-specific fetch
    else if (params.skuIds && params.skuIds.length > 0) {
      const validIds = params.skuIds
        .map(id => validateSkuId(id))
        .filter((id): id is number => id !== null);

      if (validIds.length === 0) return null;
      apiParams = { ids: validIds };
    }
    else {
      return null;
    }

    const backendSkus = await callSkuApi(apiParams);
    if (!backendSkus) return null;

    const skuMap = convertSkuApiToFrontend(backendSkus);
    logger.log(`SkuService: Fetched and converted ${Object.keys(skuMap).length} SKUs from API`);

    return skuMap;
  } catch (error) {
    logger.error('SkuService: Error in fetchSkusFromApi:', error);
    return null;
  }
}

/**
 * Private: Fetch all SKUs from API (NO CACHE LOGIC)
 */
async function fetchAllSkusFromApi(): Promise<SKUMap | null> {
  logger.log('SkuService: Fetching all SKUs from API');
  return await fetchSkusFromApi(); // No params = all categories
}

/**
 * Private: Fetch SKUs for specific categories from API (NO CACHE LOGIC)
 */
async function fetchSkusByCategoriesFromApi(categoryIds: (string | number)[]): Promise<SKUMap | null> {
  if (!categoryIds || categoryIds.length === 0) return null;

  logger.log(`SkuService: Fetching SKUs for ${categoryIds.length} categories from API`);
  return await fetchSkusFromApi({ categoryIds });
}

/**
 * Private: Fetch specific SKUs by their IDs from API (NO CACHE LOGIC)
 */
async function fetchSkusByIdsFromApi(skuIds: number[]): Promise<SKUMap | null> {
  if (!skuIds || skuIds.length === 0) return null;

  logger.log(`SkuService: Fetching ${skuIds.length} specific SKUs from API`);
  return await fetchSkusFromApi({ skuIds });
}



// ===================================================================
// EXPORTED SERVICE FUNCTIONS - CACHE STRATEGY + FETCH ORCHESTRATION
// ===================================================================

/**
 * Clears all SKUs from the local cache.
 */
export async function clearCachedSkus(): Promise<void> {
  try {
    await skuRepository.clearAllSkus();
    logger.log('SkuService: All SKUs cleared from cache.');
  } catch (error) {
    logger.error('SkuService: Error clearing cached SKUs:', error);
    throw error; // Re-throw to allow UI or calling code to handle it
  }
}

/**
 * Get all SKUs with cache-first strategy
 */
export const getSkus = async (options?: GetSkusOptions): Promise<SKU[]> => {
  logger.log('SkuService: getSkus called with options:', options);
  const startTime = Date.now();

  try {
    let skuMap: SKUMap | null = null;

    // Force refresh bypasses cache but respects specific requests
    if (options?.forceRefresh) {
      if (options.categoryIds && options.categoryIds.length > 0) {
        // User wants specific categories - only fetch those
        logger.log(`SkuService: Force refresh requested for ${options.categoryIds.length} specific categories`);
        skuMap = await fetchSkusByCategoriesFromApi(options.categoryIds);

        if (skuMap) {
          await skuRepository.saveOrUpdateSkus(skuMap, false); // Merge with existing
          await skuRepository.markCategoriesSkusLoaded(options.categoryIds.map(id => id.toString()));
        }
      } else if (options.skuIds && options.skuIds.length > 0) {
        // User wants specific SKU IDs - only fetch those
        logger.log(`SkuService: Force refresh requested for ${options.skuIds.length} specific SKU IDs`);
        skuMap = await fetchSkusByIdsFromApi(options.skuIds as number[]);

        if (skuMap) {
          await skuRepository.saveOrUpdateSkus(skuMap, false); // Merge with existing
          // Note: No category marking needed for specific SKU fetches
        }
      } else {
        // No specific request - fetch all SKUs
        logger.log('SkuService: Force refresh requested for all SKUs');
        skuMap = await fetchAllSkusFromApi();

        if (skuMap) {
          await skuRepository.saveOrUpdateSkus(skuMap, true); // Replace all

          // Mark all categories as loaded
          const categoryService = getCategoryService();
          const categories = await categoryService.getOrderedCategories();
          const categoryIds = categories.map(cat => cat.id.toString());
          await skuRepository.markCategoriesSkusLoaded(categoryIds);
        }
      }
    } else {
      // Cache-first strategy - also respects specific requests
      if (options?.categoryIds && options.categoryIds.length > 0) {
        // User wants specific categories - check if they're loaded
        const loadedCategoryIds = await skuRepository.getSkuLoadedCategories();
        const missingCategoryIds = options.categoryIds.filter(id => !loadedCategoryIds.has(id.toString()));

        if (missingCategoryIds.length > 0) {
          // Some categories missing - fetch them
          logger.log(`SkuService: Cache miss for ${missingCategoryIds.length} categories, fetching from API`);
          skuMap = await fetchSkusByCategoriesFromApi(missingCategoryIds);

          if (skuMap) {
            await skuRepository.saveOrUpdateSkus(skuMap, false); // Merge with existing
            await skuRepository.markCategoriesSkusLoaded(missingCategoryIds.map(id => id.toString()));
          }
        }

        // Return SKUs for requested categories from cache
        const cached = await skuRepository.getAllSkus();
        if (cached) {
          const categoryService = getCategoryService();
          const requestedSkus: SKU[] = [];

          for (const categoryId of options.categoryIds) {
            const category = await categoryService.getCategoryById(categoryId.toString());
            if (category?.skuIds) {
              const categorySkus = category.skuIds
                .map(skuId => cached[skuId.toString()])
                .filter(Boolean);
              requestedSkus.push(...categorySkus);
            }
          }

          const filteredSkus = applySkuFiltering(requestedSkus, options);
          const duration = Date.now() - startTime;

          logger.log(`SkuService: getSkus returned ${filteredSkus.length} SKUs for ${options.categoryIds.length} categories in ${duration}ms (filtered: ${!options?.allowInactive})`);
          return filteredSkus;
        }
      } else if (options?.skuIds && options.skuIds.length > 0) {
        // User wants specific SKU IDs - check cache first
        const cached = await skuRepository.getSkusByIds(options.skuIds as number[]);
        const cachedSkuIds = cached ? Object.keys(cached).map(id => parseInt(id, 10)) : [];
        const missingSkuIds = (options.skuIds as number[]).filter(id => !cachedSkuIds.includes(id));

        if (missingSkuIds.length > 0) {
          // Some SKUs missing - fetch them
          logger.log(`SkuService: Cache miss for ${missingSkuIds.length} SKUs, fetching from API`);
          skuMap = await fetchSkusByIdsFromApi(missingSkuIds);

          if (skuMap) {
            await skuRepository.saveOrUpdateSkus(skuMap, false); // Merge with existing
          }
        }

        // Return requested SKUs from cache
        const allCached = await skuRepository.getSkusByIds(options.skuIds as number[]);
        if (allCached) {
          const requestedSkus = Object.values(allCached);
          const filteredSkus = applySkuFiltering(requestedSkus, options);
          const duration = Date.now() - startTime;

          logger.log(`SkuService: getSkus returned ${filteredSkus.length} specific SKUs in ${duration}ms (filtered: ${!options?.allowInactive})`);
          return filteredSkus;
        }
      } else {
        // No specific request - get all SKUs (original cache-first logic)
        const cached = await skuRepository.getAllSkus();
        if (cached && Object.keys(cached).length > 0) {
          const skusArray = Object.values(cached);
          const filteredSkus = applySkuFiltering(skusArray, options);
          const duration = Date.now() - startTime;

          logger.log(`SkuService: getSkus returned ${filteredSkus.length} SKUs from cache in ${duration}ms (filtered: ${!options?.allowInactive})`);
          return filteredSkus;
        }

        // Cache miss - fetch all SKUs from API
        skuMap = await fetchAllSkusFromApi();

        if (skuMap) {
          await skuRepository.saveOrUpdateSkus(skuMap, true); // Replace all

          // Mark all categories as loaded
          const categoryService = getCategoryService();
          const categories = await categoryService.getOrderedCategories();
          const categoryIds = categories.map(cat => cat.id.toString());
          await skuRepository.markCategoriesSkusLoaded(categoryIds);
        }
      }
    }

    if (skuMap && Object.keys(skuMap).length > 0) {
      const skusArray = Object.values(skuMap);
      const filteredSkus = applySkuFiltering(skusArray, options);
      const duration = Date.now() - startTime;

      logger.log(`SkuService: getSkus returned ${filteredSkus.length} SKUs from API in ${duration}ms (filtered: ${!options?.allowInactive})`);
      return filteredSkus;
    }

    const duration = Date.now() - startTime;
    logger.log('SkuService: getSkus returned empty array');
    return [];

  } catch (error) {
    logger.error('SkuService: getSkus error:', error);
    throw error;
  }
};

/**
 * Get SKUs for specific category - delegates to getSkus for consistency
 */
export const getSkusByCategory = async (
  categoryId: string,
  options?: GetSkusOptions
): Promise<SKU[]> => {
  logger.log(`SkuService: getSkusByCategory called for category ${categoryId}`);

  // Convert categoryId to number and delegate to getSkus
  const categoryIdNum = parseInt(categoryId, 10);
  if (isNaN(categoryIdNum)) {
    logger.warn(`SkuService: Invalid category ID '${categoryId}' provided`);
    return [];
  }

  return await getSkus({
    ...options,
    categoryIds: [categoryIdNum]
  });
};

/**
 * Get single SKU by ID - delegates to getSkus for consistency  
 */
export const getSkuById = async (
  skuId: number | string,
  options?: BaseOptions
): Promise<SKU | null> => {
  const validId = validateSkuId(skuId);
  if (!validId) {
    logger.warn(`SkuService: Invalid SKU ID '${skuId}' provided`);
    return null;
  }

  logger.log(`SkuService: getSkuById called for SKU ${validId}`);

  // Delegate to getSkus with specific SKU ID
  const results = await getSkus({
    ...options,
    skuIds: [validId]
  });

  return results.length > 0 ? results[0] : null;
};

/**
 * Progressive search using callbacks - OPTIMIZED with batch loading
 * Emits results as they become available for optimal UX
 */
export const searchSkusProgressive = async (
  query: string,
  onProgress: SearchProgressCallback,
  options?: { language?: string; maxResults?: number; allowInactive?: boolean }
): Promise<void> => {
  const startTime = Date.now();
  let finalResultsCount = 0;
  let categoriesLoaded = 0;

  try {
    const searchQuery = query.toLowerCase().trim();
    if (!searchQuery) {
      onProgress({ skus: [], isComplete: true, loadedCategories: 0, totalCategories: 0 });
      return;
    }

    const language = options?.language || 'en';
    const maxResults = options?.maxResults || 50;

    // 1. Emit immediate cached results
    const allowInactive = options?.allowInactive || false;
    const cachedSkus = await searchCachedSkus(searchQuery, language, allowInactive);
    const loadedCategoryIds = await skuRepository.getSkuLoadedCategories();

    onProgress({
      skus: cachedSkus.slice(0, maxResults),
      isComplete: false,
      loadedCategories: loadedCategoryIds.size,
      totalCategories: 0,
      message: 'Searching cached data...'
    });

    // 2. Get all categories to determine what's missing
    const categoryService = getCategoryService();
    const allCategories = await categoryService.getOrderedCategories();
    const totalCategories = allCategories.length;

    const missingCategoryIds = allCategories
      .filter(cat => !loadedCategoryIds.has(cat.id.toString()))
      .map(cat => cat.id);

    if (missingCategoryIds.length === 0) {
      // All categories loaded, emit final results
      finalResultsCount = cachedSkus.slice(0, maxResults).length;
      const duration = Date.now() - startTime;

      onProgress({
        skus: cachedSkus.slice(0, maxResults),
        isComplete: true,
        loadedCategories: totalCategories,
        totalCategories,
        message: 'Search complete'
      });

      return;
    }

    // 3. Load missing categories using OPTIMIZED batch strategy
    const batchSize = 5; // Increased from 3 for better API utilization
    const batches: number[][] = [];
    for (let i = 0; i < missingCategoryIds.length; i += batchSize) {
      batches.push(missingCategoryIds.slice(i, i + batchSize));
    }

    let totalLoaded = loadedCategoryIds.size;

    // Execute batches in parallel using optimized API calls
    const batchPromises = batches.map(async (batch, batchIndex) => {
      try {
        const skusMap = await fetchSkusByCategoriesFromApi(batch);
        if (skusMap && Object.keys(skusMap).length > 0) {
          // Save to cache
          await skuRepository.saveOrUpdateSkus(skusMap, false); // Merge with existing
          await skuRepository.markCategoriesSkusLoaded(batch.map(id => id.toString()));

          return {
            success: true,
            batch,
            batchIndex,
            categoryIds: batch.map(id => id.toString())
          };
        } else {
          logger.warn(`SkuService: Empty response for category batch: ${batch}`);
          return {
            success: false,
            batch,
            batchIndex,
            error: 'Empty response'
          };
        }
      } catch (error) {
        logger.warn(`SkuService: Failed to load category batch: ${batch}`, error);
        return {
          success: false,
          batch,
          batchIndex,
          error: error?.toString() || 'Unknown error'
        };
      }
    });

    // Process batch results as they complete
    const batchResults = await Promise.allSettled(batchPromises);
    const failedCategoryIds: number[] = [];

    // Process successful results
    for (const result of batchResults) {
      if (result.status === 'fulfilled' && result.value.success) {
        const batchResult = result.value;
        if (batchResult.categoryIds) {
          totalLoaded += batchResult.categoryIds.length;
          logger.log(`SkuService: Batch ${batchResult.batchIndex + 1} completed: ${batchResult.categoryIds.length} categories`);
        }
      } else {
        // Handle failed batches
        const batchResult = result.status === 'fulfilled' ? result.value : { batch: [], error: 'Promise rejected' };
        failedCategoryIds.push(...batchResult.batch);
      }
    }

    categoriesLoaded = totalLoaded;

    // Search with updated cache and emit final progress
    const updatedResults = await searchCachedSkus(searchQuery, language, allowInactive);
    onProgress({
      skus: updatedResults.slice(0, maxResults),
      isComplete: true,
      loadedCategories: totalLoaded,
      totalCategories,
      message: failedCategoryIds.length > 0
        ? `Search complete (${failedCategoryIds.length} categories failed to load)`
        : 'Search complete'
    });

    // Final results
    finalResultsCount = updatedResults.slice(0, maxResults).length;
    const totalDuration = Date.now() - startTime;

    // Log any failed category loads
    if (failedCategoryIds.length > 0) {
      logger.warn(`SkuService: ${failedCategoryIds.length} categories failed to load`);
    }

  } catch (error) {
    logger.error('SkuService: Progressive search error:', error);
    onProgress({
      skus: [],
      isComplete: true,
      loadedCategories: 0,
      totalCategories: 0,
      message: 'Search failed'
    });
  }
};

/**
 * Search only cached SKUs (fast path)
 */
const searchCachedSkus = async (searchQuery: string, language: string, allowInactive: boolean = false): Promise<SKU[]> => {
  const allSkusMap = await skuRepository.getAllSkus();
  if (!allSkusMap) return [];

  const allSkus = Object.values(allSkusMap);

  // Apply filtering for inactive SKUs first
  const skusToSearch = allowInactive ? allSkus : filterActiveSkus(allSkus);

  return skusToSearch.filter(sku => {
    // Search in all languages
    const nameInLangs = Object.values(sku.name).map(name => (name || '').toLowerCase());
    return nameInLangs.some(name => name.includes(searchQuery));
  });
};

/**
 * Debug function to check what's currently in the SKU cache
 * @returns Information about cached SKUs
 */
export async function debugSkuCache(): Promise<{
  totalSkus: number;
  skuIds: string[];
  sampleSku?: SKU;
}> {
  try {
    const cachedSkus = await skuRepository.getAllSkus();
    if (!cachedSkus) {
      return { totalSkus: 0, skuIds: [] };
    }

    const skuIds = Object.keys(cachedSkus);
    const sampleSku = skuIds.length > 0 ? cachedSkus[skuIds[0]] : undefined;

    return {
      totalSkus: skuIds.length,
      skuIds: skuIds.slice(0, 10), // First 10 IDs for debugging
      sampleSku
    };
  } catch (error) {
    logger.error('SkuService: Error debugging cache:', error);
    return { totalSkus: 0, skuIds: [] };
  }
}

/**
 * Transforms SKU array to the API format required for upsert operation
 * @param skus Array of SKU objects to transform
 * @returns Object in the format expected by the upsert API
 */
function transformSkusToUpsertFormat(skus: SKU[]): UpsertSKUsRequest {
  const upsertRequest: UpsertSKUsRequest = {};

  for (const sku of skus) {
    const skuJsonPayload: SKUJsonPayload = {
      skuId: sku.skuId,
      type: sku.type
    };

    if (sku.type === 'parent') {
      // Parent SKUs: include full data, exclude pricing
      skuJsonPayload.name = sku.name;
      skuJsonPayload.description = sku.description;
      skuJsonPayload.imageUrl = sku.imageUrl;
      skuJsonPayload.images = sku.images;
      skuJsonPayload.variants = sku.variants;
    } else {
      // Child SKUs: always include pricing and parentId
      skuJsonPayload.costPrice = sku.costPrice;
      skuJsonPayload.sellingPrice = sku.sellingPrice;
      skuJsonPayload.mrp = sku.mrp;
      skuJsonPayload.parentId = sku.parentId;

      // CRITICAL: Include variantName for child SKUs (was missing before)
      if (sku.variantName && (sku.variantName.en || sku.variantName.ta)) {
        skuJsonPayload.variantName = sku.variantName;
      }

      // Conditionally include name, description, images if child has its own values
      if (sku.name && (sku.name.en || sku.name.ta)) {
        skuJsonPayload.name = sku.name;
      }
      if (sku.description && (sku.description.en || sku.description.ta)) {
        skuJsonPayload.description = sku.description;
      }
      if (sku.imageUrl && sku.imageUrl.trim()) {
        skuJsonPayload.imageUrl = sku.imageUrl;
      }
      if (sku.images && sku.images.length > 0) {
        skuJsonPayload.images = sku.images;
      }
    }

    const upsertPayload: UpsertSKUPayload = {
      skuId: sku.skuId,
      isActive: sku.isActive ?? 0, // Default to inactive for safety (new SKUs)
      skuJson: skuJsonPayload
    };

    upsertRequest[String(sku.skuId)] = upsertPayload;
  }

  return upsertRequest;
}

/**
 * Upserts (creates or updates) multiple SKUs via API
 * @param skus Array of SKU objects to upsert
 * @returns Promise resolving to success message or rejecting with error
 */
export async function upsertSkus(skus: SKU[]): Promise<string> {
  try {
    logger.log(`SkuService: Upserting ${skus.length} SKUs...`);

    // Transform the skus array to the API format required for upsert operation
    const upsertRequest = transformSkusToUpsertFormat(skus);

    logger.log('SkuService: Transformed upsert request:', {
      totalEntries: Object.keys(upsertRequest).length,
      skuIds: Object.keys(upsertRequest),
      sampleEntry: Object.values(upsertRequest)[0]
    });

    // Send the transformed request to the API
    const response = await axiosInstance.post('/userApp-infinity-upsertSkus', upsertRequest) as string;

    logger.log('SkuService: SKUs upserted successfully:', response);

    // Clear cache after successful upsert to ensure fresh data on next fetch
    await clearCachedSkus();

    return response || 'SKUs updated successfully';
  } catch (error) {
    logger.error('SkuService: Error upserting SKUs:', error);
    throw error;
  }
}
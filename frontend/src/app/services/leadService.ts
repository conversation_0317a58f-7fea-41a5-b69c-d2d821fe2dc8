import axiosInstance from '../../lib/axios';
import {
    Lead,
    GetLeadsRequest,
    GetLeadsResponse,
    GetLeadByIdRequest,
    GetLeadByIdResponse,
    LEAD_STATUSES,
    LeadMetadata
} from '../types/lead';
import {
    formatLeadTimestamp,
    getDefaultLeadDateRange
} from './leadUtils';
import { logger } from '@/lib/logger';

/**
 * Converts date string to epoch seconds
 * @param dateString Date string in YYYY-MM-DD format
 * @param isEndOfDay Whether to use end of day (23:59:59.999) or start of day (00:00:00.000)
 * @returns Epoch seconds (Unix timestamp)
 */
const dateToEpochSeconds = (dateString: string, isEndOfDay: boolean = false): number => {
    logger.debug('leadService: Converting date string to epoch seconds', { dateString, isEndOfDay });
    const date = new Date(dateString + (isEndOfDay ? 'T23:59:59.999' : 'T00:00:00.000'));
    return Math.floor(date.getTime() / 1000);
};

/**
 * Normalizes a lead object to handle null/undefined values and ensure type safety
 * @param lead Raw lead object from API
 * @returns Normalized lead object with safe defaults
 */
const normalizeLead = (lead: Record<string, unknown>): Lead => {
    // Validate and normalize status
    const rawStatus = lead.status as string;
    const status = LEAD_STATUSES.includes(rawStatus as Lead['status']) ? rawStatus as Lead['status'] : 'PENDING';

    // Safely parse metadata if present
    let normalizedMetadata: LeadMetadata | undefined = undefined;
    if (lead.metadata && typeof lead.metadata === 'object') {
        try {
            normalizedMetadata = lead.metadata as LeadMetadata;
        } catch (error) {
            logger.warn('leadService: Failed to parse lead metadata:', error);
            normalizedMetadata = undefined;
        }
    }

    const normalizedLead: Lead = {
        id: (lead.id as number) || 0,
        name: (lead.name as string) || '',
        type: (lead.type as Lead['type']) || 'CALLBACK_REQUESTED',
        status,
        row_updated_at: (lead.row_updated_at as number) || Date.now(),
        row_created_at: (lead.row_created_at as number) || Date.now(),
        leadSource: (lead.leadSource as Lead['leadSource']) || 'WHATSAPP',
        endCustomerMobile: (lead.endCustomerMobile as string) || '',
        conversionTime: (lead.conversionTime as number) || null,
        ...(normalizedMetadata && { metadata: normalizedMetadata }) // Only include if present
    };

    return normalizedLead;
};

/**
 * Fetches leads for admin with pagination and filtering
 * @param pageNumber Page number (default: 1)
 * @param limit Number of leads per page (default: 50)
 * @param filters Optional filters for leads
 * @returns Promise that resolves with leads array and pagination info
 */
export const getLeadsForAdmin = async (
    pageNumber: number = 1,
    limit: number = 50,
    filters?: {
        leadSource?: string;
        name?: string;
        type?: string;
        status?: string;
        endCustomerMobile?: string;
        dateFrom?: string; // YYYY-MM-DD format
        dateTo?: string; // YYYY-MM-DD format
    }
): Promise<{
    leads: Lead[];
    pagination: {
        currentPage: number;
        totalPages: number;
        totalRows: number;
        appliedLimit: number;
    };
}> => {
    try {
        logger.debug('leadService: Fetching leads', { pageNumber, limit, filters });

        const requestData: GetLeadsRequest = {
            limit,
            pageNumber,
            leadSource: filters?.leadSource,
            name: filters?.name,
            type: filters?.type,
            status: filters?.status,
            endCustomerMobile: filters?.endCustomerMobile
        };

        // Add date filters if provided (convert to epoch seconds for backend)
        if (filters?.dateFrom && filters?.dateTo) {
            const dateFromEpoch = dateToEpochSeconds(filters.dateFrom);
            const dateToEpoch = dateToEpochSeconds(filters.dateTo, true);

            logger.debug('leadService: Adding date filters', {
                dateFrom: filters.dateFrom,
                dateTo: filters.dateTo,
                dateFromEpoch,
                dateToEpoch
            });

            requestData.dateFrom = dateFromEpoch;
            requestData.dateTo = dateToEpoch;
        }

        // Remove undefined properties to keep request clean
        Object.keys(requestData).forEach(key => {
            if (requestData[key as keyof GetLeadsRequest] === undefined) {
                delete requestData[key as keyof GetLeadsRequest];
            }
        });

        // The axios interceptor returns the data directly, not the full response
        const responseData = await axiosInstance.post('/userApp-infinity-getInfinityLeads', requestData) as GetLeadsResponse;

        // Normalize leads to handle null/undefined values
        const normalizedLeads = responseData.leads.map((lead: unknown) =>
            normalizeLead(lead as Record<string, unknown>)
        );

        logger.debug('leadService: Successfully fetched leads', {
            count: normalizedLeads.length,
            totalRows: responseData.totalNumberOfRows
        });

        return {
            leads: normalizedLeads,
            pagination: {
                currentPage: responseData.currentPageNumber,
                totalPages: responseData.totalNumberOfPages,
                totalRows: responseData.totalNumberOfRows,
                appliedLimit: responseData.appliedLimit
            }
        };
    } catch (error) {
        logger.error('leadService: Failed to fetch leads:', error);

        // Handle specific error cases
        if (error && typeof error === 'object' && 'message' in error) {
            throw new Error(`Failed to fetch leads: ${(error as Error).message}`);
        } else if (typeof error === 'string') {
            throw new Error(`Failed to fetch leads: ${error}`);
        } else {
            throw new Error('Failed to fetch leads. Please try again.');
        }
    }
};

/**
 * Fetches a single lead by ID
 * @param id Lead ID
 * @returns Promise that resolves with the lead object
 */
export const getLeadById = async (id: number): Promise<Lead> => {
    try {
        logger.debug('leadService: Fetching lead by ID', { id });

        const requestData: GetLeadByIdRequest = { id };

        // The axios interceptor returns the data directly
        const responseData = await axiosInstance.post('/userApp-infinity-getInfinityLeadbyId', requestData) as GetLeadByIdResponse;

        // Normalize the lead object
        const normalizedLead = normalizeLead(responseData.infinityLead as unknown as Record<string, unknown>);

        logger.debug('leadService: Successfully fetched lead', { leadId: id, leadName: normalizedLead.name });

        return normalizedLead;
    } catch (error) {
        logger.error('leadService: Failed to fetch lead by ID:', { id, error });

        // Handle specific error cases
        if (error && typeof error === 'object' && 'message' in error) {
            throw new Error(`Failed to fetch lead: ${(error as Error).message}`);
        } else if (typeof error === 'string') {
            throw new Error(`Failed to fetch lead: ${error}`);
        } else {
            throw new Error('Failed to fetch lead. Please try again.');
        }
    }
};

/**
 * Updates a lead with new data
 * @param id Lead ID
 * @param updateData Partial lead data to update
 * @returns Promise that resolves with the updated lead object
 */
export const updateLead = async (id: number, updateData: Partial<Lead>): Promise<Lead> => {
    try {
        logger.debug('leadService: Updating lead', { id, updateData });

        const requestData = {
            id,
            ...updateData
        };

        // The axios interceptor returns the data directly
        const responseData = await axiosInstance.post('/userApp-infinity-updateInfinityLead', requestData);

        if (responseData) {
            return await getLeadById(id);
        } else {
            throw new Error('Failed to update lead. Please try again.');
        }

    } catch (error) {
        logger.error('leadService: Failed to update lead:', { id, error });

        // Handle specific error cases
        if (error && typeof error === 'object' && 'message' in error) {
            throw new Error(`Failed to update lead: ${(error as Error).message}`);
        } else if (typeof error === 'string') {
            throw new Error(`Failed to update lead: ${error}`);
        } else {
            throw new Error('Failed to update lead. Please try again.');
        }
    }
};

/**
 * Formats lead creation date for display
 * @param timestamp Timestamp in milliseconds from API
 * @returns Formatted date string
 */
export const formatLeadCreatedDate = (timestamp: number): string => {
    if (!timestamp) return 'No date';

    try {
        return formatLeadTimestamp(timestamp);
    } catch (error) {
        logger.error('leadService: Error formatting lead date:', error);
        return 'Invalid date';
    }
};

/**
 * Formats lead updated date for display
 * @param timestamp Timestamp in milliseconds from API
 * @returns Formatted date string
 */
export const formatLeadUpdatedDate = (timestamp: number): string => {
    if (!timestamp) return 'Never updated';

    try {
        return formatLeadTimestamp(timestamp);
    } catch (error) {
        logger.error('leadService: Error formatting lead updated date:', error);
        return 'Invalid date';
    }
};

/**
 * Gets unique lead sources from leads array
 * @param leads Array of leads
 * @returns Array of unique lead sources
 */
export const getUniqueLeadSources = (leads: Lead[]): string[] => {
    if (!leads || !Array.isArray(leads)) {
        return [];
    }

    const sources = leads
        .map(lead => lead.leadSource)
        .filter(source => source !== null && source !== undefined);

    return Array.from(new Set(sources)).sort();
};

/**
 * Gets unique lead types from leads array
 * @param leads Array of leads
 * @returns Array of unique lead types
 */
export const getUniqueLeadTypes = (leads: Lead[]): string[] => {
    if (!leads || !Array.isArray(leads)) {
        return [];
    }

    const types = leads
        .map(lead => lead.type)
        .filter(type => type !== null && type !== undefined);

    return Array.from(new Set(types)).sort();
};

/**
 * Gets unique lead statuses from leads array
 * @param leads Array of leads
 * @returns Array of unique lead statuses
 */
export const getUniqueLeadStatuses = (leads: Lead[]): string[] => {
    if (!leads || !Array.isArray(leads)) {
        return [];
    }

    const statuses = leads
        .map(lead => lead.status)
        .filter(status => status !== null && status !== undefined);

    return Array.from(new Set(statuses)).sort();
};



/**
 * Gets default date range for lead filtering (same as utility but service-specific)
 * @returns Object with dateFrom and dateTo in YYYY-MM-DD format
 */
export const getDefaultLeadFilterDateRange = (): { dateFrom: string; dateTo: string } => {
    return getDefaultLeadDateRange();
};



/**
 * Searches leads by name or mobile number (client-side search)
 * @param leads Array of leads
 * @param searchTerm Search term
 * @returns Filtered leads array
 */
export const searchLeads = (leads: Lead[], searchTerm: string): Lead[] => {
    if (!searchTerm || !searchTerm.trim()) return leads;

    const term = searchTerm.toLowerCase().trim();

    return leads.filter(lead => {
        const name = (lead.name || '').toLowerCase();
        const mobile = (lead.endCustomerMobile || '').toLowerCase();

        return name.includes(term) || mobile.includes(term);
    });
}; 
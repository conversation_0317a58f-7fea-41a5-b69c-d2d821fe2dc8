/**
 * Common types and interfaces shared across the rural market application.
 */

export interface LocalizedName {
    en: string; // English (required)
    ta?: string; // Tamil (optional)
    [key: string]: string | undefined; // Allows for future language extensions
}

// =============================================================================
// RURAL MARKET VALIDATION TYPES
// =============================================================================

/**
 * Phone number validation for Indian rural markets.
 * Ensures proper format with +91 prefix.
 */
export type IndianPhoneNumber = string; // Format: +91XXXXXXXXXX (10 digits after +91)

/**
 * Data size constraints for rural market optimization.
 * Helps track and manage the 10MB SKU data limit.
 */
export interface DataSizeInfo {
    sizeInBytes: number;
    sizeInMB: number;
    maxAllowedMB: number;
    compressionRatio?: number;
}

/**
 * Language preferences for rural users.
 */
export type SupportedLanguage = 'en' | 'ta';

/**
 * Utility type for creating localized content with required English fallback.
 */
export type LocalizedContent<T extends string = string> = {
    en: T;
    ta?: T;
}; 
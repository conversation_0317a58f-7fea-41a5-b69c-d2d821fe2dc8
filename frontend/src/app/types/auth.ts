export interface UserDetails {
    id: number; // Required field from API response
    name: string;
    mobileNumber: string; // Store mobile number for reference
}

// Token metadata for managing expiry and refresh
export interface TokenMetadata {
    token: string;
    expiresAt: number; // Timestamp in seconds since epoch
    refreshAfter: number; // Timestamp in seconds since epoch when refresh should start
}

// Complete auth data stored in repository
export interface AuthData {
    userDetails: UserDetails;
    tokenMetadata: TokenMetadata;
    adminRoles?: AdminRolesData; // Optional admin roles data
}

// API Request/Response interfaces
export interface SendOtpRequest {
    langCode: string;
    action: "sendOtp";
    mobile: string; // Should include +91 prefix
}

export interface SendOtpResponse {
    message: string;
}

export interface VerifyOtpRequest {
    otp: number;
    mobile: string; // Should include +91 prefix
}

export interface VerifyOtpResponse {
    jwt: string;
    authInfo: {
        token: string;
        expiresAt: number;
        refreshAfter: number;
    };
    id: number;
    name: string;
}

export interface RefreshTokenResponse {
    token: string;
    expiresAt: number;
    refreshAfter: number;
}

/**
 * Admin permissions interface based on API response
 */
export interface AdminPermissions {
    // System Administration
    systemAdmin?: boolean;

    // Category Management
    manageCategories?: boolean;

    // SKU Management (Future phases)
    manageSku?: boolean;
    manageSkuVariants?: boolean;

    // Cart Management (Future phases)
    viewAllCarts?: boolean;
    editCustomerCart?: boolean;

    // Order Management (Future phases)
    viewAllOrders?: boolean;
    manageOrders?: boolean;
    pickOrder?: boolean;

    // Returns Management
    returnOrders?: boolean;

    // Lead Management
    viewLeads?: boolean;
}

/**
 * Admin roles response from API
 */
export interface AdminRolesData {
    permissions: AdminPermissions;
    roleNames: string[];
}
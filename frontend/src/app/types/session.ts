import { LocalizedName } from "@/app/types/common";

export interface CartItem {
    skuId: number; // Product ID
    quantity: number;
    variantSkuId?: number; // Product variant ID
    addedAt?: number; // Timestamp when item was added

}

// Represents a session's core data, primarily used within the SessionContext
export interface Session {
    id: string;
    customerName?: string;
    customerPhone?: string;
    landmark?: string;
    lastActive: Date;
    cartItems: CartItem[]; // Array of actual cart items
    location?: {
        lat: number;
        lng: number;
    };
    cartSyncEnabled?: boolean; // Whether cart sync to backend is enabled for this session
}

// Represents a session tab as displayed in UI components like SessionManager
export interface SessionTabDisplay {
    id: string;
    customerName?: string;
    customerPhone?: string;
    lastActive: Date;
    cartItemsCount?: number; // Count of items in the cart
    location?: {
        lat: number;
        lng: number;
    };
}

// Define structure for detailed cart item for display
export interface DetailedCartItem {
    skuId: number;
    variantSkuId?: number;
    name: LocalizedName;
    variantName?: LocalizedName;
    imageUrl: string;
    quantity: number;
    pricePerUnit: number;
    mrpPerUnit: number;
}

// Define structure for the overall cart details
export interface CartDetails {
    detailedCartItems: DetailedCartItem[];
    itemsTotalPrice: number;
    itemsTotalMRP: number;
    totalSavings: number;
    totalCartQuantity: number;
}

// ==================== ADMIN CART TYPES ====================

// Admin cart item structure matching API skuItems
export interface AdminCartItem {
    mrp: number;
    skuId: number;
    quantity: number;
    costPrice: number;
    sellingPrice: number;
    skuName?: string;
    skuImage?: string;
}

// Admin cart customer structure from cartJson.customer
export interface AdminCartCustomer {
    name: string;
    mobile: string;
    address: string;
    location: {
        lat: string;
        lng: string;
    };
}

// Admin cart structure matching API response
export interface AdminCart {
    id: number;
    assisted_user_id: number | null;
    status: AdminCartStatus;
    is_processed: number;
    customerCode: string;
    userId: number;
    cartId: string;
    cartJson: {
        customer: AdminCartCustomer;
        skuItems: AdminCartItem[];
    };
    rowCreatedAt: number; // Epoch milliseconds
    createdBy: number;
    rowUpdatedAt: number;
    updatedBy: number;
}

// Admin cart status values
export const ADMIN_CART_STATUSES = [
    "CREATED",
    "ORDERED",
    "PROCESSED",
    "CANCELLED",
    "CONTACTED",
    "UNREACHABLE_1",
    "UNREACHABLE_2",
    "INTERESTED",
    "LOST"
] as const;

export type AdminCartStatus = typeof ADMIN_CART_STATUSES[number];

// Filters for admin cart list
export interface AdminCartFilters {
    status: string; // Empty string for all, or specific status
    dateFrom: string; // YYYY-MM-DD format
    dateTo: string; // YYYY-MM-DD format
    customerSearch: string; // Search in customer name/phone
    minValue: number; // Minimum cart value
    maxValue: number; // Maximum cart value
}

// API request for getting carts
export interface GetCartsForAdminRequest {
    limit: string; // API expects string
    pageNumber: number;
    status?: string; // Optional - omit for "All Statuses"
    fromDate?: number; // Optional - epoch seconds
    toDate?: number; // Optional - epoch seconds
}

// API response for getting carts
export interface GetCartsForAdminResponse {
    appliedLimit: string;
    currentPageNumber: number;
    totalNumberOfPages: number;
    totalNumberOfRows: number;
    carts: AdminCart[];
    status: boolean;
} 
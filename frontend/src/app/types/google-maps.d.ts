declare namespace google {
    namespace maps {
        class Map {
            constructor(mapDiv: Element, opts?: MapOptions);
            setCenter(latLng: LatLng | LatLngLiteral): void;
            setZoom(zoom: number): void;
            panTo(latLng: LatLng | LatLngLiteral): void;
            addListener(eventName: string, handler: Function): MapsEventListener;
        }

        class Marker {
            constructor(opts?: MarkerOptions);
            setPosition(latLng: LatLng | LatLngLiteral): void;
            getPosition(): LatLng;
            setMap(map: Map | null): void;
            addListener(eventName: string, handler: Function): MapsEventListener;
        }

        class LatLng {
            constructor(lat: number, lng: number, noWrap?: boolean);
            lat(): number;
            lng(): number;
            toString(): string;
        }

        interface MapOptions {
            center?: LatLng | LatLngLiteral;
            zoom?: number;
            mapTypeId?: string;
            mapTypeControl?: boolean;
            streetViewControl?: boolean;
            fullscreenControl?: boolean;
        }

        interface MarkerOptions {
            position?: LatLng | LatLngLiteral;
            map?: Map;
            title?: string;
            draggable?: boolean;
            animation?: number;
        }

        interface LatLngLiteral {
            lat: number;
            lng: number;
        }

        interface MapMouseEvent {
            latLng?: LatLng;
        }

        interface MapsEventListener {
            remove(): void;
        }
    }
} 
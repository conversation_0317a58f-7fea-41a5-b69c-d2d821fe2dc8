import type { LocalizedName } from '@/app/types/common';

export interface Category {
    id: number;
    name: LocalizedName;
    background?: string; // Optional, as not all categories might have it
    icon: string; // e.g., "FireIcon", assuming these map to actual icon components
    skuIds: number[];
    isActive: number; // 1 for active, 0 for inactive
    orderNo: number; // Display order
    versionUuid: string;
}

export interface CategoryMap {
    [id: string]: Category; // Keys are string representations of category IDs
}

// Type for CategoryMap with ordering information
export type OrderedCategoryMap = CategoryMap & {
    _orderKeys: string[]; // Ordered list of category IDs sorted by orderNo
};

export interface CategoryApiResponseData {
    categories: CategoryMap;
}

// New interfaces for upsert categories API
export interface CategoryJsonPayload {
    icon: string;
    name: LocalizedName;
    skuIds: number[];
    background?: string;
}

export interface UpsertCategoryPayload {
    id?: number; // Optional - omit for new categories
    isActive: number;
    orderNo: number;
    categoryJson: CategoryJsonPayload;
    versionUuid: string;
}

export type UpsertCategoriesRequest = UpsertCategoryPayload[]; 
/**
 * SKU (Stock Keeping Unit) Type Definitions
 * 
 * =============================================================================
 * ARCHITECTURE OVERVIEW - PARENT/CHILD SKU SYSTEM FOR RURAL MARKETS
 * =============================================================================
 * 
 * This system is optimized for rural markets with limited connectivity and older devices.
 * Key design decisions prioritize performance over storage efficiency.
 * 
 * CORE CONCEPTS:
 * --------------
 * 
 * 1. SKU TYPES:
 *    - Parent SKU: Container for variants, not directly purchasable, no prices
 *    - Child SKU: Actual purchasable items with prices
 * 
 * 2. CHILD SKU CATEGORIES:
 *    - Orphan Child: Standalone SKU with no parent (acts like independent product)
 *    - Non-Orphan Child: Has parent, appears as variant under parent SKU
 * 
 * 3. DATA STORAGE STRATEGY (Performance Optimized):
 *    - All SKUs stored flat in single SKUMap for O(1) lookup
 *    - Parent SKUs embed child data as "variants" for fast rendering
 *    - Child SKUs exist standalone for direct access and admin operations
 *    - This creates intentional data duplication for performance gains
 * 
 * EXAMPLE DATA STRUCTURE:
 * -----------------------
 * 
 * SKUMap = {
 *   "100": {  // Parent SKU
 *     skuId: 100,
 *     type: "parent",
 *     name: { en: "Fresh Apples", ta: "புதிய ஆப்பிள்கள்" },
 *     imageUrl: "apple.jpg",
 *     description: { en: "Crispy fresh apples...", ta: "..." },
 *     variants: [
 *       {  // Embedded child data for fast rendering
 *         skuId: 101,
 *         name: { en: "Fresh Apples", ta: "புதிய ஆப்பிள்கள்" },  // Copied from parent
 *         variantName: { en: "500g", ta: "500g" },  // Variant display label
 *         costPrice: 50,
 *         sellingPrice: 80,
 *         mrp: 100,
 *         type: "child"
 *         // imageUrl, images, description omitted → fallback to parent's data
 *       },
 *       {
 *         skuId: 102,
 *         name: { en: "Fresh Apples", ta: "புதிய ஆப்பிள்கள்" },  // Copied from parent
 *         variantName: { en: "1kg", ta: "1kg" },
 *         costPrice: 90,
 *         sellingPrice: 150,
 *         mrp: 180,
 *         imageUrl: "apple-1kg.jpg",  // Override parent's image
 *         type: "child"
 *       }
 *     ]
 *   },
 * 
 *   "101": {  // Child SKU (also exists standalone for O(1) lookup)
 *     skuId: 101,
 *     type: "child",
 *     name: { en: "Fresh Apples", ta: "புதிய ஆப்பிள்கள்" },
 *     variantName: { en: "500g", ta: "500g" },
 *     parentId: 100,
 *     costPrice: 50,
 *     sellingPrice: 80,
 *     mrp: 100,
 *     imageUrl: "apple.jpg",  // Inherited from parent
 *     description: { en: "Crispy fresh apples...", ta: "..." }  // Inherited from parent
 *   },
 * 
 *   "102": {  // Another child SKU
 *     skuId: 102,
 *     type: "child",
 *     name: { en: "Fresh Apples", ta: "புதிய ஆப்பிள்கள்" },
 *     variantName: { en: "1kg", ta: "1kg" },
 *     parentId: 100,
 *     costPrice: 90,
 *     sellingPrice: 150,
 *     mrp: 180,
 *     imageUrl: "apple-1kg.jpg",  // Overridden
 *     description: { en: "Crispy fresh apples...", ta: "..." }  // Inherited from parent
 *   },
 * 
 *   "203": {  // Orphan Child SKU (no parent)
 *     skuId: 203,
 *     type: "child",
 *     name: { en: "Organic Banana", ta: "இயற்கை வாழைப்பழம்" },
 *     // No variantName (orphan uses name for display)
 *     // No parentId (orphan)
 *     costPrice: 30,
 *     sellingPrice: 50,
 *     mrp: 60,
 *     imageUrl: "banana.jpg",
 *     description: { en: "Organic bananas...", ta: "..." }
 *   }
 * }
 * 
 * DISPLAY LOGIC:
 * --------------
 * 
 * 1. PARENT SKU DISPLAY:
 *    - Shows parent name: "Fresh Apples"
 *    - Lists variants using variantName: ["500g", "1kg"]
 *    - Uses variant prices for each option
 * 
 * 2. CHILD SKU DISPLAY (Standalone):
 *    - Orphan: Uses name "Organic Banana"
 *    - Non-Orphan: Uses variantName "500g" (when accessed directly)
 * 
 * 3. VARIANT RESOLUTION (Inheritance):
 *    - variant.imageUrl || parent.imageUrl
 *    - variant.images || parent.images  
 *    - variant.description || parent.description
 * 
 * RURAL MARKET OPTIMIZATIONS:
 * ----------------------------
 * 
 * 1. PERFORMANCE:
 *    - Pre-computed variants eliminate client-side joins
 *    - Flat storage enables fast category queries
 *    - O(1) SKU lookups for direct access
 * 
 * 2. DATA EFFICIENCY:
 *    - Partial variant data reduces bandwidth
 *    - Inheritance reduces storage redundancy
 *    - Single API call gets complete product + variants
 * 
 * 3. CONNECTIVITY:
 *    - Works offline once cached
 *    - No complex relationship queries needed
 *    - Simple sync via CSV export/import
 * 
 * ADMIN WORKFLOW:
 * ---------------
 * 
 * 1. DATA CONSISTENCY:
 *    - CSV export includes all SKU relationships
 *    - CSV import handles parent-child synchronization
 *    - No direct child updates (prevents orphan variant data)
 * 
 * 2. FLEXIBILITY:
 *    - Categories can reference any SKU type (parent/child/orphan)
 *    - Orphan children can be assigned parents later
 *    - Parents can be converted to orphan children
 * 
 * NAMING CONVENTIONS:
 * -------------------
 * 
 * - name: Full product name (used by parents and orphans)
 * - variantName: Variant display label (used by non-orphan children)
 * - SKUVariant.name: Child's full name (copy of parent name for non-orphans)
 * - SKUVariant.variantName: Variant label for display under parent
 * 
 * =============================================================================
 */

import type { LocalizedName } from '@/app/types/common';

/**
 * Represents the type of SKU in the parent-child hierarchy.
 */
export type SKUType = 'parent' | 'child';

/**
 * Represents a specific variant of an SKU.
 * This is embedded in parent SKUs for fast rendering (performance optimization).
 * Contains child SKU data with optional overrides for inheritance.
 */
export interface SKUVariant {
    skuId: number; // Unique identifier for the child SKU
    name: LocalizedName; // Full name (copied from parent for non-orphans)
    costPrice: number;
    sellingPrice: number;
    mrp: number;
    variantName?: LocalizedName; // Variant display label (e.g., "500g")
    imageUrl?: string; // Optional override (falls back to parent's imageUrl)
    images?: string[]; // Optional override (falls back to parent's images)
    description?: LocalizedName; // Optional override (falls back to parent's description)
    type: 'child'; // Always 'child' since variants are child SKUs
    isActive: number; // Reflects the child SKU's active status (1 for active, 0 for inactive)
}

/**
 * Represents a Stock Keeping Unit (SKU).
 * Can be either parent (container for variants) or child (purchasable item).
 */
export interface SKU {
    skuId: number; // Master identifier for the SKU
    name: LocalizedName; // Base name (used by parents and orphan children)
    imageUrl: string; // Primary image URL
    images: string[]; // Array of image URLs for gallery
    description: LocalizedName; // Detailed description
    type: SKUType; // 'parent' or 'child'
    isActive?: number; // 1 for active, 0 for inactive

    // Parent-only fields
    variants?: SKUVariant[]; // Child SKU data for fast rendering (only for parents)

    // Child-only fields (optional since parents don't have these)
    costPrice?: number; // Required for children, undefined for parents
    sellingPrice?: number; // Required for children, undefined for parents
    mrp?: number; // Required for children, undefined for parents
    parentId?: number; // Parent SKU ID (null/undefined for orphan children)
    variantName?: LocalizedName; // Variant display label (only for non-orphan children)
}

/**
 * Represents a flattened SKU for import/export operations.
 */
export interface FlattenedSKU {
    skuId: number;
    nameEn: string;
    nameTa: string;
    costPrice?: number; // Optional for parent SKUs
    sellingPrice?: number; // Optional for parent SKUs
    mrp?: number; // Optional for parent SKUs
    imageUrl: string;
    images: string; // Comma-separated string of image URLs
    descriptionEn: string;
    descriptionTa: string;
    type: SKUType;
    parentId?: number;
    status?: string; // Status of the SKU: 'active' or 'inactive'
    variantNameEn?: string; // Variant name in English (flat string for CSV)
    variantNameTa?: string; // Variant name in Tamil (flat string for CSV)
}

/**
 * Represents a map of SKUs, where the key is the stringified skuId.
 */
export interface SKUMap {
    [skuId: string]: SKU;
}

/**
 * Represents the data structure within the successful API response for SKUs.
 */
export interface SKUApiResponseData {
    skus: SKUMap;
}

// New interfaces for upsert SKUs API
export interface SKUJsonPayload {
    skuId: number;
    type: SKUType;
    // Fields for parent SKUs (always included)
    name?: LocalizedName;
    description?: LocalizedName;
    imageUrl?: string;
    images?: string[];
    variants?: SKUVariant[];
    // Fields for child SKUs (always included)
    costPrice?: number;
    sellingPrice?: number;
    mrp?: number;
    parentId?: number;
    variantName?: LocalizedName; // CRITICAL: Added missing variantName field for child SKUs
}

export interface UpsertSKUPayload {
    skuId: number;
    isActive: number;
    skuJson: SKUJsonPayload;
}

export type UpsertSKUsRequest = Record<string, UpsertSKUPayload>;

// =============================================================================
// TYPE GUARDS AND UTILITY FUNCTIONS
// =============================================================================

/**
 * Type guard to check if a SKU is a parent SKU.
 * Provides type narrowing for better autocomplete and type safety.
 */
export const isParentSKU = (sku: SKU): sku is SKU & { variants: SKUVariant[] } => {
    return sku.type === 'parent';
};

/**
 * Type guard to check if a SKU is a child SKU.
 * Provides type narrowing to ensure child-specific fields are available.
 */
export const isChildSKU = (sku: SKU): sku is SKU & {
    costPrice: number;
    sellingPrice: number;
    mrp: number;
} => {
    return sku.type === 'child';
};

/**
 * Utility function to check if a child SKU is an orphan (has no parent).
 * @param sku The SKU to check (should be a child SKU)
 * @returns True if the SKU is an orphan child
 */
export const isOrphanChild = (sku: SKU): boolean => {
    return sku.type === 'child' && !sku.parentId;
};

/**
 * Utility function to get the display name for a SKU.
 * @param sku The SKU to get display name for
 * @param language The language code (default: 'en')
 * @returns The appropriate display name
 */
export const getSkuDisplayName = (sku: SKU, language: string = 'en'): string => {
    if (isParentSKU(sku) || isOrphanChild(sku)) {
        return sku.name[language] || sku.name.en || '';
    }
    // Non-orphan child: use variantName if available, otherwise full name
    return sku.variantName?.[language] || sku.variantName?.en || sku.name[language] || sku.name.en || '';
};

/**
 * Utility function to resolve inherited properties for variants.
 * @param variant The variant to resolve properties for
 * @param parent The parent SKU to inherit from
 * @returns Resolved properties with inheritance applied
 */
export const resolveVariantProperties = (variant: SKUVariant, parent: SKU) => {
    return {
        imageUrl: variant.imageUrl || parent.imageUrl,
        images: variant.images || parent.images,
        description: variant.description || parent.description,
    };
};

/**
 * Utility type for variant display data (commonly used in UI components).
 */
export interface VariantDisplayData {
    skuId: number;
    displayName: string;
    price: number;
    mrp: number;
    imageUrl: string;
    isAvailable: boolean;
}

/**
 * Utility function to convert SKUVariant array to display data.
 * @param variants Array of SKUVariants from parent SKU
 * @param parent Parent SKU for inheritance resolution
 * @param language Language for display names (default: 'en')
 * @returns Array of VariantDisplayData for UI rendering
 */
export const getVariantDisplayData = (
    variants: SKUVariant[],
    parent: SKU,
    language: string = 'en'
): VariantDisplayData[] => {
    return variants.map(variant => {
        const resolved = resolveVariantProperties(variant, parent);
        return {
            skuId: variant.skuId,
            displayName: variant.variantName?.[language] || variant.variantName?.en || '',
            price: variant.sellingPrice,
            mrp: variant.mrp,
            imageUrl: resolved.imageUrl,
            isAvailable: true // Could be enhanced with inventory data
        };
    });
};
/**
 * Represents a successful response from the backend.
 * @template D The type of the data payload.
 */
export interface BackendSuccessResponse<D> {
    status: true;
    data: D;
}

/**
 * Detailed error information for better debugging and user experience.
 */
export interface BackendError {
    message: string;
    code: string;
}

/**
 * Represents an error response from the backend.
 */
export interface BackendErrorResponse {
    status: false;
    message: string;
    error: BackendError | string; // Backward compatible with simple string errors
}

/**
 * Represents a generic response from the backend, which can be either success or error.
 * @template D The type of the data payload if the response is successful.
 */
export type BackendResponse<D> = BackendSuccessResponse<D> | BackendErrorResponse;

/**
 * Type guard to check if an error is a structured BackendError.
 */
export const isBackendError = (error: BackendError | string): error is BackendError => {
    return typeof error === 'object' && 'message' in error;
};
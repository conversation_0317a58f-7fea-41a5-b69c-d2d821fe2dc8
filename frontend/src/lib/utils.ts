import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"
import type {
  LocalizedName,
  IndianPhoneNumber,
  DataSizeInfo,
  SupportedLanguage
} from '@/app/types/common';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Helper to generate a basic slug from title for category links
export const generateCategorySlug = (title: string): string => {
  return title.toLowerCase().replace(/\s+/g, '-').replace(/&/g, 'and');
};

export enum AppMode {
  CUSTOMER = 'customer',
  ADMIN = 'admin',
}

/**
 * Determines the current application mode based on the NEXT_PUBLIC_APP_MODE environment variable.
 * Defaults to ADMIN mode if the variable is not set or has an unrecognized value.
 * @returns {AppMode} The current application mode.
 */
export const getAppMode = (): AppMode => {
  const mode = process.env.NEXT_PUBLIC_APP_MODE?.toLowerCase();
  if (mode === AppMode.CUSTOMER) {
    return AppMode.CUSTOMER;
  }
  // Default to ADMIN mode if not specified or invalid
  return AppMode.ADMIN;
};

/**
 * Checks if the application is currently in customer-facing mode.
 * @returns {boolean} True if in CUSTOMER mode, false otherwise.
 */
export const isCustomerFacingMode = (): boolean => {
  return getAppMode() === AppMode.CUSTOMER;
};

/**
 * Gets the next available guest number based on existing session names.
 * Scans through session names looking for "Guest N" pattern and returns N+1.
 * @param {string[]} existingSessionNames - Array of existing session names
 * @returns {number} The next available guest number
 */
export const getNextGuestNumber = (existingSessionNames: string[]): number => {
  const guestPattern = /^Guest (\d+)$/;
  let maxNumber = 0;

  existingSessionNames.forEach(name => {
    if (name) {
      const match = name.match(guestPattern);
      if (match) {
        const num = parseInt(match[1], 10);
        if (num > maxNumber) {
          maxNumber = num;
        }
      }
    }
  });

  return maxNumber + 1;
};

/**
 * Masks a mobile number for privacy by showing only first 2 and last 2 digits
 * @param mobile Mobile number string
 * @returns Masked mobile number (XX****XX format)
 */
export const maskMobile = (mobile: string | null): string => {
  if (!mobile || mobile.length < 4) return mobile || 'N/A';
  return `${mobile.slice(0, 2)}****${mobile.slice(-2)}`;
};

// =============================================================================
// VALIDATION AND LOCALIZATION UTILITIES
// =============================================================================

/**
 * Validates Indian phone number format.
 * @param phone Phone number to validate
 * @returns True if valid format (+91 followed by 10 digits)
 */
export const isValidIndianPhone = (phone: string): phone is IndianPhoneNumber => {
  const phoneRegex = /^\+91[6-9]\d{9}$/;
  return phoneRegex.test(phone);
};

/**
 * Gets the appropriate localized text based on language preference.
 * @param content Localized content object
 * @param language Preferred language
 * @returns Text in preferred language or English fallback
 */
export const getLocalizedText = (content: LocalizedName, language: SupportedLanguage = 'en'): string => {
  return content[language] || content.en || '';
};

/**
 * Calculates data size information for rural market constraints.
 * @param sizeInBytes Size in bytes
 * @returns DataSizeInfo object with MB conversion and limits
 */
export const getDataSizeInfo = (sizeInBytes: number): DataSizeInfo => {
  const sizeInMB = sizeInBytes / (1024 * 1024);
  return {
    sizeInBytes,
    sizeInMB: Math.round(sizeInMB * 100) / 100, // Round to 2 decimal places
    maxAllowedMB: 10, // Rural market constraint
  };
};

// =============================================================================
// PHONE NUMBER UTILITIES FOR INDIAN MOBILE NUMBERS
// =============================================================================

/**
 * Formats an Indian mobile number to include +91 prefix if missing.
 * @param number Mobile number to format (with or without +91 prefix)
 * @returns Formatted mobile number with +91 prefix
 * @example
 * formatIndianMobile('9876543210') // Returns: '+************'
 * formatIndianMobile('+************') // Returns: '+************'
 */
export const formatIndianMobile = (number: string): string => {
  if (!number) return number;

  // Remove any spaces, dashes, or other formatting
  const cleanNumber = number.replace(/[\s\-\(\)]/g, '');

  // If already has +91 prefix, return as is
  if (cleanNumber.startsWith('+91')) {
    return cleanNumber;
  }

  // If starts with 91 but no +, add the +
  if (cleanNumber.startsWith('91') && cleanNumber.length === 12) {
    return `+${cleanNumber}`;
  }

  // If it's a 10-digit number, add +91 prefix
  if (cleanNumber.length === 10 && /^[6-9]\d{9}$/.test(cleanNumber)) {
    return `+91${cleanNumber}`;
  }

  // Return original if format is unclear
  return number;
};

/**
 * Validates Indian phone number format.
 * @param phone Phone number to validate
 * @returns True if valid format (+91 followed by 10 digits starting with 6-9)
 * @example
 * validateIndianMobile('+************') // Returns: true
 * validateIndianMobile('9876543210') // Returns: false (missing +91)
 * validateIndianMobile('+************') // Returns: false (starts with 5)
 */
export const validateIndianMobile = (phone: string): phone is IndianPhoneNumber => {
  if (!phone) return false;

  // Must start with +91 followed by 10 digits, first digit must be 6-9
  const phoneRegex = /^\+91[6-9]\d{9}$/;
  return phoneRegex.test(phone);
};

/**
 * Masks an Indian mobile number for privacy/logging purposes.
 * Shows +91 prefix and last 4 digits, masks middle digits.
 * @param phone Phone number to mask
 * @returns Masked phone number for safe logging
 * @example
 * maskIndianMobile('+************') // Returns: '+91****3210'
 * maskIndianMobile('invalid') // Returns: 'invalid'
 */
export const maskIndianMobile = (phone: string): string => {
  if (!phone) return 'N/A';

  // If it's a valid format, mask it
  if (validateIndianMobile(phone)) {
    const lastFour = phone.slice(-4);
    return `+91****${lastFour}`;
  }

  // If not valid format, return as is (might be partial entry)
  return phone;
};

// Navigation utilities for SKU pages
export interface NavigationContext {
  from: 'list' | 'detail' | 'variant';
  parentId?: number;
}

export function getNavigationContext(searchParams: URLSearchParams): NavigationContext {
  const from = searchParams.get('from') as 'list' | 'detail' | 'variant' || 'list';
  const parentId = searchParams.get('parentId') ? parseInt(searchParams.get('parentId')!) : undefined;

  return { from, parentId };
}

export function buildNavigationUrl(context: NavigationContext): string {
  switch (context.from) {
    case 'detail':
      return context.parentId ? `/admin/skus/${context.parentId}` : '/admin/skus';
    case 'variant':
      return context.parentId ? `/admin/skus/${context.parentId}` : '/admin/skus';
    case 'list':
    default:
      return '/admin/skus';
  }
}

export function addNavigationParams(baseUrl: string, context: NavigationContext): string {
  const url = new URL(baseUrl, 'http://localhost'); // dummy base for URL constructor
  url.searchParams.set('from', context.from);
  if (context.parentId) {
    url.searchParams.set('parentId', context.parentId.toString());
  }
  return url.pathname + url.search;
}

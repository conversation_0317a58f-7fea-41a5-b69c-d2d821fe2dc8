import pointInPolygonHao from 'point-in-polygon-hao'

/**
 * Geographic coordinate representing a latitude/longitude point
 */
export interface LatLng {
    lat: number;
    lng: number;
}

/**
 * Result of point-in-polygon test
 * - true: point is inside the polygon
 * - false: point is outside the polygon
 * - 0: point is exactly on the polygon boundary
 */
export type PointInPolygonResult = boolean | 0;

/**
 * Determines if a geographic point (lat/lng) falls inside a polygon.
 *
 * This function is optimized for rural markets and handles:
 * - Floating-point precision issues common on mobile devices
 * - Edge cases like points exactly on polygon boundaries
 * - Self-intersecting polygons
 * - Automatic polygon closing if not already closed
 *
 * @param point - The point to test as {lat, lng}
 * @param polygon - Array of polygon vertices as {lat, lng} points
 * @returns true if inside, false if outside, 0 if exactly on boundary
 *
 * @example
 * ```typescript
 * const userLocation = { lat: 28.6139, lng: 77.2090 }; // New Delhi
 *
 * const delhiBoundary = [
 *   { lat: 28.7041, lng: 77.1025 }, // Northwest
 *   { lat: 28.7041, lng: 77.2500 }, // Northeast
 *   { lat: 28.4595, lng: 77.2500 }, // Southeast
 *   { lat: 28.4595, lng: 77.1025 }, // Southwest
 *   // No need to close - function handles it automatically
 * ];
 *
 * const canDeliver = isPointInPolygon(userLocation, delhiBoundary);
 * // Returns: true
 * ```
 *
 * @example
 * ```typescript
 * // Service area around a rural market
 * const marketLocation = { lat: 25.5941, lng: 85.1376 }; // Patna
 * const serviceArea = [
 *   { lat: 25.6041, lng: 85.1276 }, // 1km radius approx
 *   { lat: 25.6041, lng: 85.1476 },
 *   { lat: 25.5841, lng: 85.1476 },
 *   { lat: 25.5841, lng: 85.1276 }
 * ];
 *
 * const canServe = isPointInPolygon(marketLocation, serviceArea);
 * ```
 */
export function isPointInPolygon(
    point: LatLng,
    polygon: LatLng[]
): PointInPolygonResult {
    // Validate inputs
    if (!polygon || polygon.length < 3) {
        throw new Error('Invalid polygon: must have at least 3 points to form a polygon');
    }

    // Validate coordinate ranges for geographic data
    if (point.lat < -90 || point.lat > 90) {
        throw new Error(`Invalid latitude: ${point.lat}. Must be between -90 and 90.`);
    }
    if (point.lng < -180 || point.lng > 180) {
        throw new Error(`Invalid longitude: ${point.lng}. Must be between -180 and 180.`);
    }

    // Validate polygon points
    for (let i = 0; i < polygon.length; i++) {
        const p = polygon[i];
        if (p.lat < -90 || p.lat > 90) {
            throw new Error(`Invalid polygon latitude at index ${i}: ${p.lat}. Must be between -90 and 90.`);
        }
        if (p.lng < -180 || p.lng > 180) {
            throw new Error(`Invalid polygon longitude at index ${i}: ${p.lng}. Must be between -180 and 180.`);
        }
    }

    // Convert LatLng to [lng, lat] format expected by point-in-polygon-hao
    const testPoint: [number, number] = [point.lng, point.lat];

    // Convert polygon LatLng[] to [lng, lat][] format and ensure it's closed
    const polygonCoords: [number, number][] = polygon.map(p => [p.lng, p.lat]);

    // Auto-close polygon if not already closed
    const first = polygonCoords[0];
    const last = polygonCoords[polygonCoords.length - 1];
    if (first[0] !== last[0] || first[1] !== last[1]) {
        polygonCoords.push([first[0], first[1]]); // Close the polygon
    }

    // point-in-polygon-hao expects: [exterior] (array of polygon rings)
    const polygonForTest: [number, number][][] = [polygonCoords];

    try {
        return pointInPolygonHao(testPoint, polygonForTest);
    } catch (error) {
        throw new Error(`Point-in-polygon calculation failed: ${error instanceof Error ? error.message : String(error)}`);
    }
}

/**
 * Helper function to create a simple rectangular polygon from bounds
 * Useful for creating quick geographic bounding boxes
 *
 * @param bounds - Geographic bounds {north, south, east, west}
 * @returns Array of LatLng points representing the rectangular area
 *
 * @example
 * ```typescript
 * const delhiBounds = {
 *   north: 28.88,
 *   south: 28.40,
 *   east: 77.35,
 *   west: 76.84
 * };
 *
 * const delhiRect = createRectangularPolygon(delhiBounds);
 * const isInDelhi = isPointInPolygon({ lat: 28.6139, lng: 77.2090 }, delhiRect);
 * ```
 */
export function createRectangularPolygon(bounds: {
    north: number;
    south: number;
    east: number;
    west: number;
}): LatLng[] {
    const { north, south, east, west } = bounds;

    // Validate bounds
    if (north <= south) {
        throw new Error('Invalid bounds: north must be greater than south');
    }
    if (east <= west) {
        throw new Error('Invalid bounds: east must be greater than west');
    }

    return [
        { lat: north, lng: west },  // top-left
        { lat: north, lng: east },  // top-right
        { lat: south, lng: east },  // bottom-right
        { lat: south, lng: west },  // bottom-left
        // No need to close - isPointInPolygon handles it automatically
    ];
}

/**
 * Helper function to create a circular polygon approximation around a center point
 * Useful for creating service areas around a location
 *
 * @param center - Center point of the circle
 * @param radiusKm - Radius in kilometers
 * @param segments - Number of segments to approximate the circle (default: 16)
 * @returns Array of LatLng points approximating a circle
 *
 * @example
 * ```typescript
 * const marketCenter = { lat: 28.6139, lng: 77.2090 };
 * const serviceArea = createCircularPolygon(marketCenter, 5, 12); // 5km radius, 12 segments
 *
 * const customerLocation = { lat: 28.6200, lng: 77.2150 };
 * const canDeliver = isPointInPolygon(customerLocation, serviceArea);
 * ```
 */
export function createCircularPolygon(
    center: LatLng,
    radiusKm: number,
    segments: number = 16
): LatLng[] {
    if (radiusKm <= 0) {
        throw new Error('Radius must be greater than 0');
    }
    if (segments < 3) {
        throw new Error('Segments must be at least 3 to form a polygon');
    }

    const points: LatLng[] = [];

    // Earth radius in kilometers
    const earthRadiusKm = 6371;

    // Convert radius to angular distance
    const angularDistance = radiusKm / earthRadiusKm;

    // Convert center coordinates to radians
    const centerLatRad = (center.lat * Math.PI) / 180;
    const centerLngRad = (center.lng * Math.PI) / 180;

    for (let i = 0; i < segments; i++) {
        // Calculate bearing for this segment
        const bearing = (2 * Math.PI * i) / segments;

        // Calculate point on circle using spherical trigonometry
        const pointLatRad = Math.asin(
            Math.sin(centerLatRad) * Math.cos(angularDistance) +
            Math.cos(centerLatRad) * Math.sin(angularDistance) * Math.cos(bearing)
        );

        const pointLngRad = centerLngRad + Math.atan2(
            Math.sin(bearing) * Math.sin(angularDistance) * Math.cos(centerLatRad),
            Math.cos(angularDistance) - Math.sin(centerLatRad) * Math.sin(pointLatRad)
        );

        // Convert back to degrees
        const pointLat = (pointLatRad * 180) / Math.PI;
        const pointLng = (pointLngRad * 180) / Math.PI;

        points.push({ lat: pointLat, lng: pointLng });
    }

    return points;
}

/**
 * Firebase Storage URL for polygons JSON (with download token)
 */
const POLYGON_JSON_URL =
    'https://firebasestorage.googleapis.com/v0/b/signintest-84632.firebasestorage.app/o/terminals-wowonline-polygon.json?alt=media&token=dd078aea-f2e5-48ed-8e5b-6064101e4667';

interface TerminalPolygon {
    id: string;
    name: string;
    polygon: string; // WKT POLYGON
}

interface PolygonJson {
    terminals: TerminalPolygon[];
}

let cachedEtag: string | null = null;
let cachedData: PolygonJson | null = null;

/**
 * Parses a WKT POLYGON string into an array of {lat, lng} points
 */
export function parseWKTPolygon(wkt: string): LatLng[] {
    // Example: POLYGON ((lng1 lat1, lng2 lat2, ...))
    const match = wkt.match(/POLYGON\s*\(\((.+)\)\)/i);
    if (!match) throw new Error('Invalid WKT POLYGON format');
    return match[1]
        .split(',')
        .map(pair => {
            const [lng, lat] = pair.trim().split(/\s+/).map(Number);
            if (isNaN(lat) || isNaN(lng)) throw new Error('Invalid coordinate in WKT');
            return { lat, lng };
        });
}

/**
 * Fetches the latest polygon/facility data from Firebase Storage, using ETag for caching.
 * Throws error if fetch or parsing fails.
 */
export async function getPolygonFacilityData(): Promise<PolygonJson> {
    // Check ETag with HEAD request
    if (cachedData) {
        return cachedData;
    }
    const resp = await fetch('/terminals-wowonline-polygon.json');
    if (!resp.ok) throw new Error('Failed to fetch polygon data');
    const data = await resp.json();
    // Basic validation
    if (!data.terminals || !Array.isArray(data.terminals)) {
        throw new Error('Invalid polygon data format');
    }
    cachedData = data;
    return data;
}

/**
 * Example: Get all polygons as arrays of {lat, lng} with facility info
 */
export async function getAllTerminalPolygons(): Promise<Array<{ id: string; name: string; polygon: LatLng[] }>> {
    const data = await getPolygonFacilityData();
    return data.terminals.map(t => ({
        id: t.id,
        name: t.name,
        polygon: parseWKTPolygon(t.polygon)
    }));
}

/**
 * Returns the closest facility id for a given lat/lng, or null if not found.
 * Uses the dynamically loaded polygons from Firebase Storage.
 */
export async function getClosestFacility(lat: number, lng: number): Promise<string | null> {
    const terminals = await getAllTerminalPolygons();
    for (const terminal of terminals) {
        if (isPointInPolygon({ lat, lng }, terminal.polygon)) {
            return terminal.id;
        }
    }
    return null;
}
/**
 * EventBus for app-wide event communication
 * Handles events like auth logout, session expiry, etc.
 */
import { logger } from './logger';

export type AppEvents = {
    'auth:logout': void;
    'auth:sessionExpired': void;
    // Future events can be added here
};

export class EventBus {
    private static instance: EventBus;
    private listeners: Map<keyof AppEvents, Function[]> = new Map();

    public static getInstance(): EventBus {
        if (!EventBus.instance) {
            EventBus.instance = new EventBus();
        }
        return EventBus.instance;
    }

    private constructor() {
        // Private constructor to prevent direct instantiation
    }

    /**
     * Emit an event to all registered listeners
     * @param event Event name
     * @param data Optional event data
     */
    public emit<K extends keyof AppEvents>(event: K, data?: AppEvents[K]): void {
        const eventListeners = this.listeners.get(event);
        if (eventListeners) {
            eventListeners.forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    logger.error(`EventBus: Error in listener for event '${String(event)}':`, error);
                }
            });
        }
    }

    /**
     * Register a listener for an event
     * @param event Event name
     * @param callback Callback function
     */
    public on<K extends keyof AppEvents>(event: K, callback: (data?: AppEvents[K]) => void): void {
        if (!this.listeners.has(event)) {
            this.listeners.set(event, []);
        }
        this.listeners.get(event)!.push(callback);
    }

    /**
     * Unregister a listener for an event
     * @param event Event name
     * @param callback Callback function to remove
     */
    public off<K extends keyof AppEvents>(event: K, callback: Function): void {
        const eventListeners = this.listeners.get(event);
        if (eventListeners) {
            const index = eventListeners.indexOf(callback);
            if (index > -1) {
                eventListeners.splice(index, 1);
            }
        }
    }

    /**
     * Remove all listeners for an event
     * @param event Event name
     */
    public removeAllListeners<K extends keyof AppEvents>(event: K): void {
        this.listeners.delete(event);
    }

    /**
     * Clear all listeners (useful for testing)
     */
    public clear(): void {
        this.listeners.clear();
    }
}
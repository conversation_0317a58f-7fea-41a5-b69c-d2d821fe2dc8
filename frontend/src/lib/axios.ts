import axios, { AxiosError, type AxiosResponse } from 'axios';
import type { BackendResponse, BackendSuccessResponse, BackendErrorResponse } from '../app/types/api';
import { AuthRepository } from '../app/repository/AuthRepository';
import { EventBus } from './eventBus';
import { logger } from './logger';

const axiosInstance = axios.create({
    baseURL: process.env.NEXT_PUBLIC_API_URL,
    headers: {
        'Content-Type': 'application/json',
    },
});

// Global flag to prevent multiple simultaneous token refresh attempts
let isRefreshingToken = false;
let refreshTokenPromise: Promise<string | null> | null = null;

// Response Interceptor
axiosInstance.interceptors.response.use(
    // ----- <PERSON>ler -----
    <D = any>(response: AxiosResponse<BackendResponse<D>>) => {
        const backendResponse = response.data;

        if (backendResponse.status === false) {
            logger.error('Backend Error:', (backendResponse as BackendErrorResponse).error);
            return Promise.reject((backendResponse as BackendErrorResponse).error);
        }
        if (backendResponse.status === true) {
            return (backendResponse as BackendSuccessResponse<D>).data;
        }
        logger.error('Unexpected backend response structure:', backendResponse);
        return Promise.reject('Unexpected backend response structure');
    },
    // ----- Error Handler -----
    (error: AxiosError) => {
        // Handles network errors, HTTP 4xx/5xx errors not caught by the above logic,
        // or errors thrown from the success handler (e.g., malformed success response)

        if (error.response) {
            // The request was made and the server responded with a status code
            // that falls out of the range of 2xx
            // We attempt to parse it as a BackendErrorResponse if the structure matches
            const backendResponse = error.response.data as BackendErrorResponse;
            if (backendResponse && backendResponse.status === false && backendResponse.error) {
                logger.error('Backend HTTP Error (parsed):', backendResponse.error);
                return Promise.reject(backendResponse.error);
            }
            // If it doesn't match our expected backend error format, reject with the whole response error
            logger.error('HTTP Error (unparsed):', error.response.data);
            return Promise.reject(error.response.data || error.message); // Fallback to error.message
        } else if (error.request) {
            // The request was made but no response was received (e.g., network error)
            logger.error('Network Error / No Response:', error.message);
            return Promise.reject(error.message); // or a custom error object/message
        } else {
            // Something happened in setting up the request that triggered an Error
            logger.error('Axios Setup Error:', error.message);
            return Promise.reject(error.message);
        }
    }
);

/**
 * Performs token refresh and returns the new token
 * Uses a global promise to prevent multiple simultaneous refresh attempts
 */
async function performTokenRefresh(): Promise<string | null> {
    // If already refreshing, wait for the existing refresh to complete
    if (isRefreshingToken && refreshTokenPromise) {
        logger.info('Axios: Token refresh already in progress, waiting...');
        return refreshTokenPromise;
    }

    // Start a new refresh process
    isRefreshingToken = true;
    refreshTokenPromise = (async () => {
        try {
            logger.info('Axios: Starting token refresh...');
            const authRepository = AuthRepository.getInstance();

            // Get current token to use for refresh API call
            const currentToken = await authRepository.getValidToken();
            if (!currentToken) {
                logger.warn('Axios: No current token available for refresh');
                return null;
            }

            // Call refresh API directly (no more circular dependency)
            const refreshResult = await axios.post('/userApp-user-refreshJwt', {}, {
                baseURL: process.env.NEXT_PUBLIC_API_URL,
                headers: {
                    'Authorization': `Bearer ${currentToken}`,
                    'Content-Type': 'application/json'
                }
            });

            // Extract data from the response (should be { token, expiresAt, refreshAfter })
            const refreshData = refreshResult.data;

            // Handle both direct response and wrapped response
            const tokenData = refreshData.data || refreshData;

            // Update token in repository
            await authRepository.updateTokenMetadata({
                token: tokenData.token,
                expiresAt: tokenData.expiresAt,
                refreshAfter: tokenData.refreshAfter
            });

            logger.info('Axios: Token refreshed successfully');
            return tokenData.token;
        } catch (error) {
            logger.error('Axios: Token refresh failed:', error);

            // Emit logout event on refresh failure
            try {
                const eventBus = EventBus.getInstance();
                eventBus.emit('auth:logout');
                logger.info('Axios: Logout event emitted due to refresh failure');
            } catch (eventError) {
                logger.error('Axios: Failed to emit logout event:', eventError);
            }

            // Clear auth data on refresh failure
            try {
                const authRepository = AuthRepository.getInstance();
                await authRepository.clearAuthData();
                logger.info('Axios: Auth data cleared due to refresh failure');
            } catch (clearError) {
                logger.error('Axios: Failed to clear auth data:', clearError);
            }

            return null;
        } finally {
            // Reset refresh state
            isRefreshingToken = false;
            refreshTokenPromise = null;
        }
    })();

    return refreshTokenPromise;
}

// Request Interceptor - Add JWT token and version, handle token refresh
axiosInstance.interceptors.request.use(
    async (config) => {
        // Add version to all requests
        config.data = {
            ...config.data,
            version: `4.0.0`
        };

        // Determine endpoint type
        const isLoginEndpoint = config.url?.includes('userApp-auth-');
        const isRefreshEndpoint = config.url?.includes('userApp-user-refreshJwt');

        // For login endpoints: Skip everything (no token, no refresh check)
        if (isLoginEndpoint) {
            logger.info('Axios: Skipping token handling for login endpoint:', config.url);
            return config;
        }

        const authRepository = AuthRepository.getInstance();

        // For refresh endpoint: Add current token but skip refresh check
        if (isRefreshEndpoint) {
            logger.info('Axios: Adding token to refresh endpoint (no refresh check):', config.url);
            try {
                const token = await authRepository.getValidToken();
                if (token) {
                    config.headers.Authorization = `Bearer ${token}`;
                } else {
                    logger.warn('Axios: No valid token for refresh endpoint');
                }
            } catch (error) {
                logger.error('Axios: Failed to get token for refresh endpoint:', error);
            }
            return config;
        }

        // For all other endpoints: Check if token needs refresh, then add token
        try {
            let tokenToUse: string | null = null;

            // Check if token needs refresh
            const shouldRefresh = await authRepository.shouldRefreshToken();
            if (shouldRefresh) {
                logger.info('Axios: Token needs refresh before API call:', config.url);
                tokenToUse = await performTokenRefresh();

                if (!tokenToUse) {
                    logger.warn('Axios: Token refresh failed, proceeding without token');
                }
            } else {
                // Get current valid token
                tokenToUse = await authRepository.getValidToken();
            }

            // Add token to request if available
            if (tokenToUse) {
                config.headers.Authorization = `Bearer ${tokenToUse}`;
                logger.info('Axios: Token added to request:', config.url);
            } else {
                logger.info('Axios: No token available for request:', config.url);
            }

        } catch (error) {
            logger.error('Axios: Error handling token for request:', error);
            // Continue with request without token
        }

        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

export default axiosInstance;
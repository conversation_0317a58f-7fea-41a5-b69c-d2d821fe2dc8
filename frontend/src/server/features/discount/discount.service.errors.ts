/**
 * Discount Service Error Classes
 *
 * This file re-exports discount error classes from the consolidated discount.errors.ts
 * for backward compatibility and provides service-specific error aliases.
 */

// Re-export all discount errors from the consolidated location
export {
  DiscountError,
  DiscountNotFoundError,
  DiscountCalculationError,
  DiscountValidationError,
  DiscountStorageError,
  DiscountUsageLimitError,
  DiscountExpiredError,
  DiscountServiceError,
  DiscountOperationError,
  DiscountEligibilityError,
  ServiceNotInitializedError,
  ServiceConfigurationError,
  DiscountEngineError,
  CalculatorRegistrationError,
  isDiscountError,
  isDiscountNotFoundError,
  isDiscountCalculationError,
  isDiscountValidationError,
  createServiceCalculationError,
  createServiceValidationError,
  createEngineCalculationError,
  createEngineValidationError
} from './discount.errors';
/**
 * Discount Service Error Classes
 *
 * This file defines custom error classes for the discount service layer.
 */

import {
  DiscountCalculationError as SharedDiscountCalculationError,
  DiscountValidationError as SharedDiscountValidationError,
  DiscountNotFoundError as SharedDiscountNotFoundError
} from '../../shared/utils/errors';

/**
 * Base error class for service errors
 */
export class DiscountServiceError extends Error {
  constructor(message: string, public readonly code: string) {
    super(message);
    this.name = 'DiscountServiceError';
  }
}

// Export shared error classes with aliases for backward compatibility
export const DiscountNotFoundError = SharedDiscountNotFoundError;
export const DiscountValidationError = SharedDiscountValidationError;

/**
 * Error thrown when a service operation fails
 */
export class DiscountOperationError extends DiscountServiceError {
  constructor(operation: string, message: string) {
    super(`Failed to ${operation}: ${message}`, 'OPERATION_ERROR');
    this.name = 'DiscountOperationError';
  }
}

// Export shared error class with alias for backward compatibility
export const DiscountCalculationError = SharedDiscountCalculationError;

/**
 * Error thrown when discount eligibility check fails
 */
export class DiscountEligibilityError extends DiscountServiceError {
  constructor(discountId: string, reason: string) {
    super(`Discount ${discountId} is not eligible: ${reason}`, 'ELIGIBILITY_ERROR');
    this.name = 'DiscountEligibilityError';
  }
}

/**
 * Error thrown when service is not properly initialized
 */
export class ServiceNotInitializedError extends DiscountServiceError {
  constructor() {
    super('Service not initialized. Call initialize() first.', 'SERVICE_NOT_INITIALIZED');
    this.name = 'ServiceNotInitializedError';
  }
}

/**
 * Error thrown when service configuration is invalid
 */
export class ServiceConfigurationError extends DiscountServiceError {
  constructor(message: string) {
    super(message, 'CONFIGURATION_ERROR');
    this.name = 'ServiceConfigurationError';
  }
}
/**
 * Discount Service Error Classes
 * 
 * This file defines custom error classes for the discount service layer.
 */

/**
 * Base error class for service errors
 */
export class DiscountServiceError extends Error {
  constructor(message: string, public readonly code: string) {
    super(message);
    this.name = 'DiscountServiceError';
  }
}

/**
 * Error thrown when a discount is not found
 */
export class DiscountNotFoundError extends DiscountServiceError {
  constructor(id: string) {
    super(`Discount with id ${id} not found`, 'DISCOUNT_NOT_FOUND');
    this.name = 'DiscountNotFoundError';
  }
}

/**
 * Error thrown when discount validation fails
 */
export class DiscountValidationError extends DiscountServiceError {
  constructor(message: string, public readonly validationErrors: string[]) {
    super(message, 'VALIDATION_ERROR');
    this.name = 'DiscountValidationError';
  }
}

/**
 * Error thrown when a service operation fails
 */
export class DiscountOperationError extends DiscountServiceError {
  constructor(operation: string, message: string) {
    super(`Failed to ${operation}: ${message}`, 'OPERATION_ERROR');
    this.name = 'DiscountOperationError';
  }
}

/**
 * Error thrown when discount calculation fails
 */
export class DiscountCalculationError extends DiscountServiceError {
  constructor(message: string, public readonly cartTotal?: number) {
    super(message, 'CALCULATION_ERROR');
    this.name = 'DiscountCalculationError';
  }
}

/**
 * Error thrown when discount eligibility check fails
 */
export class DiscountEligibilityError extends DiscountServiceError {
  constructor(discountId: string, reason: string) {
    super(`Discount ${discountId} is not eligible: ${reason}`, 'ELIGIBILITY_ERROR');
    this.name = 'DiscountEligibilityError';
  }
}

/**
 * Error thrown when service is not properly initialized
 */
export class ServiceNotInitializedError extends DiscountServiceError {
  constructor() {
    super('Service not initialized. Call initialize() first.', 'SERVICE_NOT_INITIALIZED');
    this.name = 'ServiceNotInitializedError';
  }
}

/**
 * Error thrown when service configuration is invalid
 */
export class ServiceConfigurationError extends DiscountServiceError {
  constructor(message: string) {
    super(message, 'CONFIGURATION_ERROR');
    this.name = 'ServiceConfigurationError';
  }
}
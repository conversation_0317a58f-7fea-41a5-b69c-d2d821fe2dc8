/**
 * Storage utility functions for the discount system
 * 
 * These utilities provide common functionality used across different storage providers,
 * including validation, filtering, sorting, and pagination helpers.
 */

import type { 
  Discount, 
  DiscountFilters,
  DiscountType 
} from '../discount.types';
import type { 
  EnhancedDiscountFilters, 
  PaginationOptions, 
  PaginatedResult 
} from './storage.interface';

export class StorageUtils {
  /**
   * Generate a unique ID for new discounts
   */
  static generateId(): string {
    return crypto.randomUUID();
  }

  /**
   * Get current timestamp for created/updated fields
   */
  static getCurrentTimestamp(): Date {
    return new Date();
  }

  /**
   * Apply filters to a discount array (for in-memory filtering)
   */
  static applyFilters(discounts: Discount[], filters?: EnhancedDiscountFilters): Discount[] {
    if (!filters) return discounts;

    let filtered = discounts;

    // Active/inactive filter
    if (filters.isActive !== undefined) {
      filtered = filtered.filter(discount => discount.isActive === filters.isActive);
    }

    // Type filter
    if (filters.type) {
      filtered = filtered.filter(discount => discount.type === filters.type);
    }

    // Valid at date filter
    if (filters.validAt) {
      const checkDate = filters.validAt;
      filtered = filtered.filter(discount => 
        discount.validFrom <= checkDate && discount.validTo >= checkDate
      );
    }

    // Search filter (name and description)
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      filtered = filtered.filter(discount => 
        discount.name.toLowerCase().includes(searchTerm) ||
        (discount.description && discount.description.toLowerCase().includes(searchTerm))
      );
    }

    return filtered;
  }

  /**
   * Apply sorting to a discount array
   */
  static applySorting(
    discounts: Discount[], 
    sortBy?: keyof Discount, 
    sortOrder: 'asc' | 'desc' = 'desc'
  ): Discount[] {
    if (!sortBy) {
      // Default sort by createdAt desc
      return discounts.sort((a, b) => {
        const aTime = a.createdAt.getTime();
        const bTime = b.createdAt.getTime();
        return sortOrder === 'desc' ? bTime - aTime : aTime - bTime;
      });
    }

    return discounts.sort((a, b) => {
      const aValue = a[sortBy];
      const bValue = b[sortBy];

      // Handle different data types
      if (aValue instanceof Date && bValue instanceof Date) {
        const diff = aValue.getTime() - bValue.getTime();
        return sortOrder === 'desc' ? -diff : diff;
      }

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        const comparison = aValue.localeCompare(bValue);
        return sortOrder === 'desc' ? -comparison : comparison;
      }

      if (typeof aValue === 'number' && typeof bValue === 'number') {
        const diff = aValue - bValue;
        return sortOrder === 'desc' ? -diff : diff;
      }

      if (typeof aValue === 'boolean' && typeof bValue === 'boolean') {
        const diff = Number(aValue) - Number(bValue);
        return sortOrder === 'desc' ? -diff : diff;
      }

      // Fallback to string comparison
      const aStr = String(aValue);
      const bStr = String(bValue);
      const comparison = aStr.localeCompare(bStr);
      return sortOrder === 'desc' ? -comparison : comparison;
    });
  }

  /**
   * Apply pagination to a discount array
   */
  static applyPagination<T>(
    items: T[], 
    pagination?: PaginationOptions
  ): PaginatedResult<T> {
    if (!pagination) {
      return {
        items,
        total: items.length,
        page: 1,
        limit: items.length,
        totalPages: 1
      };
    }

    const { page, limit } = pagination;
    const total = items.length;
    const totalPages = Math.ceil(total / limit);
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedItems = items.slice(startIndex, endIndex);

    return {
      items: paginatedItems,
      total,
      page,
      limit,
      totalPages
    };
  }

  /**
   * Validate discount data before storage operations
   */
  static validateDiscountData(discount: Partial<Discount>): string[] {
    const errors: string[] = [];

    // Required fields validation
    if (discount.name !== undefined) {
      if (!discount.name || discount.name.trim().length === 0) {
        errors.push('Discount name is required');
      } else if (discount.name.length > 100) {
        errors.push('Discount name cannot exceed 100 characters');
      }
    }

    // Description validation
    if (discount.description !== undefined && discount.description.length > 500) {
      errors.push('Discount description cannot exceed 500 characters');
    }

    // Date validation
    if (discount.validFrom && discount.validTo) {
      if (discount.validFrom >= discount.validTo) {
        errors.push('Valid from date must be before valid to date');
      }
    }

    // Usage count validation
    if (discount.usageCount !== undefined && discount.usageCount < 0) {
      errors.push('Usage count cannot be negative');
    }

    // Max usage validation
    if (discount.maxUsage !== undefined && discount.maxUsage < 1) {
      errors.push('Maximum usage must be at least 1');
    }

    // Type-specific validation
    if (discount.type === 'PERCENTAGE_CAP') {
      const percentageDiscount = discount as Partial<Discount & {
        percentage: number;
        maxDiscountAmount: number;
        minCartValue: number;
      }>;

      if (percentageDiscount.percentage !== undefined) {
        if (percentageDiscount.percentage <= 0 || percentageDiscount.percentage > 100) {
          errors.push('Percentage must be between 0.01 and 100');
        }
      }

      if (percentageDiscount.maxDiscountAmount !== undefined && percentageDiscount.maxDiscountAmount < 0) {
        errors.push('Maximum discount amount cannot be negative');
      }

      if (percentageDiscount.minCartValue !== undefined && percentageDiscount.minCartValue < 0) {
        errors.push('Minimum cart value cannot be negative');
      }
    }

    return errors;
  }

  /**
   * Check if a discount is currently active and valid
   */
  static isDiscountActive(discount: Discount, checkDate: Date = new Date()): boolean {
    return (
      discount.isActive &&
      discount.validFrom <= checkDate &&
      discount.validTo >= checkDate &&
      (discount.maxUsage === undefined || discount.usageCount < discount.maxUsage)
    );
  }

  /**
   * Filter discounts to only active ones
   */
  static filterActiveDiscounts(discounts: Discount[], validAt: Date = new Date()): Discount[] {
    return discounts.filter(discount => this.isDiscountActive(discount, validAt));
  }

  /**
   * Create a deep copy of a discount object
   */
  static cloneDiscount(discount: Discount): Discount {
    return {
      ...discount,
      validFrom: new Date(discount.validFrom),
      validTo: new Date(discount.validTo),
      createdAt: new Date(discount.createdAt),
      updatedAt: new Date(discount.updatedAt)
    };
  }

  /**
   * Sanitize discount input data
   */
  static sanitizeDiscountInput(
    input: Omit<Discount, 'id' | 'createdAt' | 'updatedAt' | 'usageCount'>
  ): Omit<Discount, 'id' | 'createdAt' | 'updatedAt' | 'usageCount'> {
    return {
      ...input,
      name: input.name.trim().replace(/\s+/g, ' '),
      description: input.description?.trim().replace(/\s+/g, ' ')
    };
  }

  /**
   * Calculate total pages for pagination
   */
  static calculateTotalPages(total: number, limit: number): number {
    return Math.ceil(total / limit);
  }

  /**
   * Validate pagination parameters
   */
  static validatePagination(pagination?: PaginationOptions): string[] {
    const errors: string[] = [];

    if (pagination) {
      if (pagination.page < 1) {
        errors.push('Page must be at least 1');
      }

      if (pagination.limit < 1) {
        errors.push('Limit must be at least 1');
      }

      if (pagination.limit > 100) {
        errors.push('Limit cannot exceed 100');
      }
    }

    return errors;
  }
}
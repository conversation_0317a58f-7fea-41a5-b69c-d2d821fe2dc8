/**
 * Storage-specific error classes for the discount system
 * 
 * These error classes provide structured error handling for storage operations,
 * enabling proper error categorization and handling at the storage layer.
 */

import { DiscountError } from '../discount.errors';

// Base storage error
export class StorageError extends DiscountError {
  constructor(message: string, public operation: string) {
    super(message, 'STORAGE_ERROR');
    this.name = 'StorageError';
  }
}

// Specific storage error types
export class StorageConnectionError extends StorageError {
  constructor(provider: string) {
    super(`Failed to connect to storage provider: ${provider}`, 'CONNECTION');
    this.name = 'StorageConnectionError';
  }
}

export class StorageTimeoutError extends StorageError {
  constructor(operation: string, timeout: number) {
    super(`Storage operation '${operation}' timed out after ${timeout}ms`, operation);
    this.name = 'StorageTimeoutError';
  }
}

export class StorageValidationError extends StorageError {
  constructor(field: string, value: unknown, reason: string) {
    super(`Validation failed for field '${field}' with value '${value}': ${reason}`, 'VALIDATION');
    this.name = 'StorageValidationError';
  }
}

export class StorageConstraintError extends StorageError {
  constructor(constraint: string, details?: string) {
    super(`Storage constraint violation: ${constraint}${details ? ` - ${details}` : ''}`, 'CONSTRAINT');
    this.name = 'StorageConstraintError';
  }
}

export class StorageTransactionError extends StorageError {
  constructor(operation: string, reason?: string) {
    super(`Transaction ${operation} failed${reason ? `: ${reason}` : ''}`, 'TRANSACTION');
    this.name = 'StorageTransactionError';
  }
}

export class StorageProviderNotFoundError extends StorageError {
  constructor(providerName: string) {
    super(`Storage provider '${providerName}' not found or not registered`, 'PROVIDER_NOT_FOUND');
    this.name = 'StorageProviderNotFoundError';
  }
}

// Error type guards
export function isStorageError(error: unknown): error is StorageError {
  return error instanceof StorageError;
}

export function isStorageConnectionError(error: unknown): error is StorageConnectionError {
  return error instanceof StorageConnectionError;
}

export function isStorageTimeoutError(error: unknown): error is StorageTimeoutError {
  return error instanceof StorageTimeoutError;
}

export function isStorageValidationError(error: unknown): error is StorageValidationError {
  return error instanceof StorageValidationError;
}

export function isStorageConstraintError(error: unknown): error is StorageConstraintError {
  return error instanceof StorageConstraintError;
}

export function isStorageTransactionError(error: unknown): error is StorageTransactionError {
  return error instanceof StorageTransactionError;
}
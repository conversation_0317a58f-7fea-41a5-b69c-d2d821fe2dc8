/**
 * Discount Service Implementation
 * 
 * This file implements the discount service interface, providing business logic
 * for discount operations and integrating the repository and calculation engine.
 */

import type {
  DiscountService,
  CreateDiscountInput,
  UpdateDiscountInput,
  CalculateDiscountsInput,
  ServiceDiscountFilters,
  ServicePaginationOptions,
  ServiceResult,
  BulkOperationResult,
  AnalyticsOptions
} from './discount.service.interface';
import type {
  Discount,
  DiscountType,
  DiscountCalculationResult,
  CartItemWithDetails,
  DiscountUsageStats,
  PaginatedResult,
  CreateDiscountData
} from './discount.types';
import type { DiscountRepository } from './discount.repository';
import type { DiscountEngine } from './discount.engine.interface';
import {
  DiscountServiceError,
  DiscountValidationError,
  DiscountOperationError,
  ServiceNotInitializedError,
  ServiceConfigurationError
} from './discount.service.errors';
import { logger } from '../../../lib/logger';
import { getServiceConfig } from './discount.config';
import { 
  LoggingHelpers, 
  ErrorHandlingHelpers
} from './discount.utils';

/**
 * Configuration options for the discount service
 */
export interface DiscountServiceConfig {
  /**
   * Enable detailed logging
   * @default false
   */
  enableDetailedLogging?: boolean;

  /**
   * Maximum number of discounts that can be applied to a single cart
   * @default 5
   */
  maxDiscountsPerCart?: number;

  /**
   * Enable automatic usage tracking
   * @default true
   */
  enableUsageTracking?: boolean;

  /**
   * Cache TTL for active discounts in milliseconds
   * @default 300000 (5 minutes)
   */
  activeDiscountsCacheTtl?: number;
}

/**
 * Implementation of the discount service
 */
export class DefaultDiscountService implements DiscountService {
  private config: DiscountServiceConfig;
  private initialized = false;
  private activeDiscountsCache: {
    data: Discount[];
    timestamp: number;
  } | null = null;

  constructor(
    private readonly repository: DiscountRepository,
    private readonly engine: DiscountEngine,
    config: DiscountServiceConfig = {}
  ) {
    // Load configuration from centralized config system
    const serviceConfig = getServiceConfig();
    const DEFAULT_CONFIG: DiscountServiceConfig = {
      enableDetailedLogging: serviceConfig.enableDetailedLogging,
      maxDiscountsPerCart: serviceConfig.maxDiscountsPerCart,
      enableUsageTracking: serviceConfig.enableUsageTracking,
      activeDiscountsCacheTtl: serviceConfig.activeDiscountsCacheTtl
    };
    
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    try {
      // Repository and engine don't have initialize methods in current implementation
      // They are ready to use upon construction
      
      this.initialized = true;
      
      if (this.config.enableDetailedLogging) {
        logger.info('[DiscountService] Initialized successfully');
      }
    } catch (error) {
      logger.error('[DiscountService] Failed to initialize:', error);
      throw new ServiceConfigurationError(
        `Failed to initialize service: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  async cleanup(): Promise<void> {
    try {
      // Repository and engine don't have cleanup methods in current implementation
      // Just reset internal state
      
      this.initialized = false;
      this.activeDiscountsCache = null;
      
      if (this.config.enableDetailedLogging) {
        logger.info('[DiscountService] Cleaned up successfully');
      }
    } catch (error) {
      logger.error('[DiscountService] Failed to cleanup:', error);
      throw new DiscountOperationError('cleanup', 
        error instanceof Error ? error.message : 'Unknown error');
    }
  }

  async createDiscount(input: CreateDiscountInput): Promise<ServiceResult<Discount>> {
    await this.ensureInitialized();

    return ErrorHandlingHelpers.executeWithServiceResult(
      'DiscountService',
      'createDiscount',
      async () => {
        // Validate input
        const validationErrors = await this.validateDiscount(input);
        if (!validationErrors.success || (validationErrors.data && validationErrors.data.length > 0)) {
          throw new DiscountValidationError('Validation failed', { validationErrors: validationErrors.data || [] });
        }

        // Convert input to repository format
        const discountData = this.convertCreateInputToDiscountData(input);
        
        // Create discount
        const discount = await this.repository.create(discountData);
        
        // Clear active discounts cache
        this.clearActiveDiscountsCache();
        
        if (this.config.enableDetailedLogging) {
          LoggingHelpers.logOperationInfo('DiscountService', `Created discount: ${discount.id}`);
        }
        
        return discount;
      }
    );
  }

  async getDiscountById(id: string): Promise<ServiceResult<Discount | null>> {
    await this.ensureInitialized();

    return ErrorHandlingHelpers.executeWithServiceResult(
      'DiscountService',
      'getDiscountById',
      async () => {
        const discount = await this.repository.findById(id);
        
        if (this.config.enableDetailedLogging && discount) {
          LoggingHelpers.logOperationInfo('DiscountService', `Found discount: ${id}`);
        }
        
        return discount;
      },
      { discountId: id }
    );
  }

  async listDiscounts(
    filters?: ServiceDiscountFilters,
    pagination?: ServicePaginationOptions
  ): Promise<ServiceResult<PaginatedResult<Discount>>> {
    await this.ensureInitialized();

    try {
      // Convert service filters to repository filters
      const repoFilters = this.convertServiceFiltersToRepoFilters(filters);
      const repoPagination = pagination ? {
        page: pagination.page,
        limit: pagination.limit
      } : undefined;

      const result = await this.repository.findAll(repoFilters, repoPagination);
      
      if (this.config.enableDetailedLogging) {
        logger.debug(`[DiscountService] Listed ${result.items.length} discounts`);
      }
      
      return {
        success: true,
        data: result
      };
    } catch (error) {
      logger.error('[DiscountService] Failed to list discounts:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        code: error instanceof DiscountServiceError ? error.code : 'OPERATION_ERROR'
      };
    }
  }

  async updateDiscount(id: string, input: UpdateDiscountInput): Promise<ServiceResult<Discount>> {
    await this.ensureInitialized();

    try {
      // Check if discount exists
      const existsResult = await this.getDiscountById(id);
      if (!existsResult.success || !existsResult.data) {
        return {
          success: false,
          error: `Discount with id ${id} not found`,
          code: 'DISCOUNT_NOT_FOUND'
        };
      }

      // Validate input
      const validationErrors = await this.validateDiscount(input);
      if (!validationErrors.success || (validationErrors.data && validationErrors.data.length > 0)) {
        return {
          success: false,
          error: 'Validation failed',
          code: 'VALIDATION_ERROR'
        };
      }

      // Update discount
      const updated = await this.repository.update(id, input);
      
      // Clear active discounts cache
      this.clearActiveDiscountsCache();
      
      if (this.config.enableDetailedLogging) {
        logger.info(`[DiscountService] Updated discount: ${id}`);
      }
      
      return {
        success: true,
        data: updated
      };
    } catch (error) {
      logger.error(`[DiscountService] Failed to update discount ${id}:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        code: error instanceof DiscountServiceError ? error.code : 'OPERATION_ERROR'
      };
    }
  }

  async deleteDiscount(id: string): Promise<ServiceResult<boolean>> {
    await this.ensureInitialized();

    try {
      const result = await this.repository.delete(id);
      
      if (result) {
        // Clear active discounts cache
        this.clearActiveDiscountsCache();
        
        if (this.config.enableDetailedLogging) {
          logger.info(`[DiscountService] Deleted discount: ${id}`);
        }
      }
      
      return {
        success: true,
        data: result
      };
    } catch (error) {
      logger.error(`[DiscountService] Failed to delete discount ${id}:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        code: error instanceof DiscountServiceError ? error.code : 'OPERATION_ERROR'
      };
    }
  }

  async bulkDeleteDiscounts(ids: string[]): Promise<ServiceResult<BulkOperationResult>> {
    await this.ensureInitialized();

    try {
      const deletedCount = await this.repository.bulkDelete(ids);
      
      // Clear active discounts cache
      this.clearActiveDiscountsCache();
      
      const result: BulkOperationResult = {
        successCount: deletedCount,
        failureCount: ids.length - deletedCount,
        errors: []
      };
      
      if (this.config.enableDetailedLogging) {
        logger.info(`[DiscountService] Bulk deleted ${deletedCount} discounts`);
      }
      
      return {
        success: true,
        data: result
      };
    } catch (error) {
      logger.error('[DiscountService] Failed to bulk delete discounts:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        code: error instanceof DiscountServiceError ? error.code : 'OPERATION_ERROR'
      };
    }
  }

  async calculateDiscounts(input: CalculateDiscountsInput): Promise<ServiceResult<DiscountCalculationResult>> {
    await this.ensureInitialized();

    try {
      // Get active discounts
      const activeDiscountsResult = await this.getActiveDiscounts();
      if (!activeDiscountsResult.success || !activeDiscountsResult.data) {
        return {
          success: false,
          error: 'Failed to get active discounts',
          code: 'OPERATION_ERROR'
        };
      }

      // Calculate discounts using the engine
      const result = await this.engine.calculateDiscounts(
        input.cartItems,
        activeDiscountsResult.data
      );

      // Record usage if enabled and discounts were applied
      if (this.config.enableUsageTracking && input.applyDiscounts !== false && input.cartId) {
        for (const appliedDiscount of result.appliedDiscounts) {
          try {
            await this.recordDiscountUsage(
              appliedDiscount.discountId,
              input.cartId,
              appliedDiscount.discountAmount,
              result.originalTotal
            );
          } catch (error) {
            // Log but don't fail the calculation
            logger.warn(`[DiscountService] Failed to record usage for discount ${appliedDiscount.discountId}:`, error);
          }
        }
      }
      
      if (this.config.enableDetailedLogging) {
        logger.debug(`[DiscountService] Calculated discounts: ${result.totalDiscount} savings`);
      }
      
      return {
        success: true,
        data: result
      };
    } catch (error) {
      logger.error('[DiscountService] Failed to calculate discounts:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        code: error instanceof DiscountServiceError ? error.code : 'CALCULATION_ERROR'
      };
    }
  }

  async getActiveDiscounts(date?: Date): Promise<ServiceResult<Discount[]>> {
    await this.ensureInitialized();

    try {
      // Check cache first
      if (this.activeDiscountsCache && this.isCacheValid()) {
        if (this.config.enableDetailedLogging) {
          logger.debug('[DiscountService] Returning cached active discounts');
        }
        return {
          success: true,
          data: this.activeDiscountsCache.data
        };
      }

      // Get from repository
      const discounts = await this.repository.findActiveDiscounts(date);
      
      // Update cache
      this.activeDiscountsCache = {
        data: discounts,
        timestamp: Date.now()
      };
      
      if (this.config.enableDetailedLogging) {
        logger.debug(`[DiscountService] Found ${discounts.length} active discounts`);
      }
      
      return {
        success: true,
        data: discounts
      };
    } catch (error) {
      logger.error('[DiscountService] Failed to get active discounts:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        code: error instanceof DiscountServiceError ? error.code : 'OPERATION_ERROR'
      };
    }
  }

  async searchDiscounts(query: string, filters?: ServiceDiscountFilters): Promise<ServiceResult<Discount[]>> {
    await this.ensureInitialized();

    try {
      const repoFilters = this.convertServiceFiltersToRepoFilters(filters);
      const results = await this.repository.search(query, repoFilters);
      
      if (this.config.enableDetailedLogging) {
        logger.debug(`[DiscountService] Search found ${results.length} discounts`);
      }
      
      return {
        success: true,
        data: results
      };
    } catch (error) {
      logger.error(`[DiscountService] Failed to search discounts with query \"${query}\":`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        code: error instanceof DiscountServiceError ? error.code : 'OPERATION_ERROR'
      };
    }
  }

  async getDiscountsByType(type: DiscountType): Promise<ServiceResult<Discount[]>> {
    await this.ensureInitialized();

    try {
      const discounts = await this.repository.findByType(type);
      
      if (this.config.enableDetailedLogging) {
        logger.debug(`[DiscountService] Found ${discounts.length} discounts of type ${type}`);
      }
      
      return {
        success: true,
        data: discounts
      };
    } catch (error) {
      logger.error(`[DiscountService] Failed to get discounts of type ${type}:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        code: error instanceof DiscountServiceError ? error.code : 'OPERATION_ERROR'
      };
    }
  }

  async validateDiscount(input: CreateDiscountInput | UpdateDiscountInput): Promise<ServiceResult<string[]>> {
    try {
      const errors = await this.repository.validateDiscount(input);
      
      return {
        success: true,
        data: errors
      };
    } catch (error) {
      logger.error('[DiscountService] Failed to validate discount:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        code: 'VALIDATION_ERROR'
      };
    }
  }

  async getUsageAnalytics(options?: AnalyticsOptions): Promise<ServiceResult<DiscountUsageStats>> {
    await this.ensureInitialized();

    try {
      const repoOptions = options ? {
        discountId: options.discountId,
        dateFrom: options.dateFrom,
        dateTo: options.dateTo
      } : undefined;

      const stats = await this.repository.getUsageStats(repoOptions);
      
      // Convert UsageStats to DiscountUsageStats format
      const discountUsageStats: DiscountUsageStats = {
        discountId: stats.discountId,
        totalApplications: stats.totalApplications,
        totalSavings: stats.totalSavings,
        averageDiscountAmount: stats.averageDiscountAmount,
        averageCartValue: 0, // Calculate from available data or set default
        usageByDate: stats.dailyStats?.map(stat => ({
          date: stat.date.toISOString().split('T')[0],
          applications: stat.applications,
          savings: stat.savings
        })) || []
      };
      
      return {
        success: true,
        data: discountUsageStats
      };
    } catch (error) {
      logger.error('[DiscountService] Failed to get usage analytics:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        code: error instanceof DiscountServiceError ? error.code : 'OPERATION_ERROR'
      };
    }
  }

  async recordDiscountUsage(
    discountId: string,
    cartId: string,
    discountAmount: number,
    cartTotal: number
  ): Promise<ServiceResult<void>> {
    await this.ensureInitialized();

    try {
      await this.repository.recordUsage({discountId, cartId, discountAmount, cartTotal});
      
      if (this.config.enableDetailedLogging) {
        logger.debug(`[DiscountService] Recorded usage for discount: ${discountId}`);
      }
      
      return {
        success: true
      };
    } catch (error) {
      logger.error(`[DiscountService] Failed to record usage for discount ${discountId}:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        code: error instanceof DiscountServiceError ? error.code : 'OPERATION_ERROR'
      };
    }
  }

  async checkDiscountEligibility(
    discountId: string,
    cartItems: CartItemWithDetails[]
  ): Promise<ServiceResult<{ eligible: boolean; reason?: string; estimatedDiscount?: number; }>> {
    await this.ensureInitialized();

    try {
      // Get the discount
      const discountResult = await this.getDiscountById(discountId);
      if (!discountResult.success || !discountResult.data) {
        return {
          success: true,
          data: {
            eligible: false,
            reason: 'Discount not found'
          }
        };
      }

      const discount = discountResult.data;

      // Check if discount is active and valid
      if (!discount.isActive) {
        return {
          success: true,
          data: {
            eligible: false,
            reason: 'Discount is not active'
          }
        };
      }

      const now = new Date();
      if (now < discount.validFrom || now > discount.validTo) {
        return {
          success: true,
          data: {
            eligible: false,
            reason: 'Discount is not valid at this time'
          }
        };
      }

      // Check usage limit
      if (discount.maxUsage && discount.usageCount >= discount.maxUsage) {
        return {
          success: true,
          data: {
            eligible: false,
            reason: 'Discount usage limit reached'
          }
        };
      }

      // Calculate estimated discount
      const calculationResult = await this.engine.calculateDiscounts(cartItems, [discount]);
      const appliedDiscount = calculationResult.appliedDiscounts.find(ad => ad.discountId === discountId);

      if (!appliedDiscount) {
        return {
          success: true,
          data: {
            eligible: false,
            reason: 'Cart does not meet discount requirements'
          }
        };
      }

      return {
        success: true,
        data: {
          eligible: true,
          estimatedDiscount: appliedDiscount.discountAmount
        }
      };
    } catch (error) {
      logger.error(`[DiscountService] Failed to check eligibility for discount ${discountId}:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        code: error instanceof DiscountServiceError ? error.code : 'OPERATION_ERROR'
      };
    }
  }

  async getHealthStatus(): Promise<{ status: 'healthy' | 'degraded' | 'unhealthy'; details: Record<string, unknown>; }> {
    try {
      if (!this.initialized) {
        return {
          status: 'unhealthy',
          details: {
            initialized: false,
            message: 'Service not initialized'
          }
        };
      }

      // Get repository health
      const repoHealth = await this.repository.healthCheck();
      
      // Get engine health (using getEngineStats since healthCheck doesn't exist)
      const engineStats = this.engine.getEngineStats();
      const engineHealth: { status: 'healthy' | 'degraded' | 'unhealthy'; details: unknown } = {
        status: 'healthy',
        details: engineStats
      };
      
      // Determine overall status
      let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
      if (repoHealth.status === 'unhealthy') {
        status = 'unhealthy';
      } else if (repoHealth.status === 'degraded') {
        status = 'degraded';
      }
      
      return {
        status,
        details: {
          initialized: this.initialized,
          repository: repoHealth,
          engine: engineHealth,
          config: this.config,
          cache: {
            activeDiscountsCache: this.activeDiscountsCache ? {
              itemCount: this.activeDiscountsCache.data.length,
              timestamp: this.activeDiscountsCache.timestamp,
              isValid: this.isCacheValid()
            } : null
          }
        }
      };
    } catch (error) {
      logger.error('[DiscountService] Health check failed:', error);
      return {
        status: 'unhealthy',
        details: {
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      };
    }
  }

  async getServiceStats(): Promise<{ totalDiscounts: number; activeDiscounts: number; totalUsage: number; averageDiscountAmount: number; }> {
    await this.ensureInitialized();

    try {
      // Get total discounts
      const totalDiscounts = await this.repository.count();
      
      // Get active discounts
      const activeDiscountsResult = await this.getActiveDiscounts();
      const activeDiscounts = activeDiscountsResult.success ? activeDiscountsResult.data!.length : 0;
      
      // Get usage stats
      const usageStats = await this.repository.getUsageStats();
      
      return {
        totalDiscounts,
        activeDiscounts,
        totalUsage: usageStats.totalApplications,
        averageDiscountAmount: usageStats.averageDiscountAmount
      };
    } catch (error) {
      logger.error('[DiscountService] Failed to get service stats:', error);
      return {
        totalDiscounts: 0,
        activeDiscounts: 0,
        totalUsage: 0,
        averageDiscountAmount: 0
      };
    }
  }

  /**
   * Private helper methods
   */

  private async ensureInitialized(): Promise<void> {
    if (!this.initialized) {
      throw new ServiceNotInitializedError();
    }
  }

  private convertCreateInputToDiscountData(input: CreateDiscountInput): CreateDiscountData {
    if (input.type === 'PERCENTAGE_CAP') {
      return {
        name: input.name,
        description: input.description,
        type: input.type,
        isActive: input.isActive ?? true,
        validFrom: input.validFrom,
        validTo: input.validTo,
        maxUsage: input.maxUsage,
        percentage: input.percentage!,
        maxDiscountAmount: input.maxDiscountAmount!,
        minCartValue: input.minCartValue!
      };
    }

    // This should never happen with current types, but provides type safety
    throw new Error(`Unsupported discount type: ${input.type}`);
  }

  private convertServiceFiltersToRepoFilters(filters?: ServiceDiscountFilters): Record<string, unknown> | undefined {
    if (!filters) return undefined;

    return {
      isActive: filters.isActive,
      type: filters.type,
      validAt: filters.validAt,
      search: filters.search,
      sortBy: filters.sortBy,
      sortOrder: filters.sortOrder
    };
  }

  private clearActiveDiscountsCache(): void {
    this.activeDiscountsCache = null;
  }

  private isCacheValid(): boolean {
    if (!this.activeDiscountsCache) return false;
    
    const now = Date.now();
    const cacheAge = now - this.activeDiscountsCache.timestamp;
    return cacheAge < (this.config.activeDiscountsCacheTtl || getServiceConfig().activeDiscountsCacheTtl);
  }
}

/**
 * Factory function to create a discount service
 */
export function createDiscountService(
  repository: DiscountRepository,
  engine: DiscountEngine,
  config?: DiscountServiceConfig
): DiscountService {
  return new DefaultDiscountService(repository, engine, config);
}
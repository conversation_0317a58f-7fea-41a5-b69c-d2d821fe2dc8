/**
 * Integration tests for Discount Engine with Percentage Cap Calculator
 * 
 * Tests cover the complete discount calculation flow with real calculator
 * implementations, various cart scenarios, and edge cases.
 */

import {
  DefaultDiscountEngine,
  DefaultDiscountEngineFactory,
  createDiscountEngineFactory
} from '../discount.engine';
import { PercentageCapDiscountCalculator } from '../calculators/percentage-cap.calculator';
import type {
  DiscountEngine
} from '../discount.engine.interface';
import type {
  CartItemWithDetails,
  PercentageCapDiscount
} from '../discount.types';

// Mock logger to avoid console output during tests
jest.mock('@/lib/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn()
  }
}));

// Test data fixtures
const createMockCartItems = (overrides: Partial<CartItemWithDetails>[] = []): CartItemWithDetails[] => {
  const defaults: CartItemWithDetails[] = [
    {
      skuId: 1,
      quantity: 2,
      pricePerUnit: 100,
      mrpPerUnit: 120,
      name: 'Test Product 1'
    },
    {
      skuId: 2,
      quantity: 1,
      pricePerUnit: 200,
      mrpPerUnit: 250,
      name: 'Test Product 2'
    }
  ];

  return overrides.length > 0 
    ? overrides.map((override, index) => ({ ...defaults[index] || defaults[0], ...override }))
    : defaults;
};

const createMockPercentageCapDiscount = (overrides: Partial<PercentageCapDiscount> = {}): PercentageCapDiscount => ({
  id: 'test-discount-123',
  name: 'Test Percentage Cap Discount',
  description: 'Test discount description',
  type: 'PERCENTAGE_CAP',
  isActive: true,
  validFrom: new Date('2025-01-01'),
  validTo: new Date('2025-12-31'),
  createdAt: new Date('2025-01-01T10:00:00Z'),
  updatedAt: new Date('2025-01-01T10:00:00Z'),
  usageCount: 0,
  percentage: 10,
  maxDiscountAmount: 50,
  minCartValue: 300,
  maxUsage: 1000,
  ...overrides
});

describe('Discount Engine Integration with Percentage Cap Calculator', () => {
  let engine: DiscountEngine;
  let calculator: PercentageCapDiscountCalculator;

  beforeEach(() => {
    engine = new DefaultDiscountEngine();
    calculator = new PercentageCapDiscountCalculator();
    engine.registerCalculator(calculator);
  });

  describe('Basic Discount Calculation', () => {
    test('should calculate percentage discount correctly', async () => {
      const cartItems = createMockCartItems(); // Total: 400
      const discount = createMockPercentageCapDiscount({
        percentage: 10,
        maxDiscountAmount: 50,
        minCartValue: 300
      });

      const result = await engine.calculateDiscounts(cartItems, [discount]);

      expect(result).toEqual({
        totalDiscount: 40, // 10% of 400
        appliedDiscounts: [{
          discountId: discount.id,
          discountName: discount.name,
          discountAmount: 40,
          discountType: 'PERCENTAGE_CAP'
        }],
        originalTotal: 400,
        finalTotal: 360,
        savings: 40
      });
    });

    test('should apply discount cap when percentage exceeds maximum', async () => {
      const cartItems = createMockCartItems([
        { pricePerUnit: 500, quantity: 2 } // Total: 1000
      ]);
      const discount = createMockPercentageCapDiscount({
        percentage: 10,
        maxDiscountAmount: 50, // Cap at 50
        minCartValue: 300
      });

      const result = await engine.calculateDiscounts(cartItems, [discount]);

      expect(result.totalDiscount).toBe(50); // Capped at 50, not 100
      expect(result.finalTotal).toBe(950);
      expect(result.appliedDiscounts[0].discountAmount).toBe(50);
    });

    test('should not apply discount when cart is below minimum value', async () => {
      const cartItems = createMockCartItems([
        { pricePerUnit: 50, quantity: 2 } // Total: 100
      ]);
      const discount = createMockPercentageCapDiscount({
        percentage: 10,
        maxDiscountAmount: 50,
        minCartValue: 300 // Cart total is below this
      });

      const result = await engine.calculateDiscounts(cartItems, [discount]);

      expect(result.totalDiscount).toBe(0);
      expect(result.appliedDiscounts).toHaveLength(0);
      expect(result.finalTotal).toBe(100);
    });
  });

  describe('Multiple Discounts', () => {
    test('should apply best discount in non-stacking mode', async () => {
      const cartItems = createMockCartItems(); // Total: 400
      const discount1 = createMockPercentageCapDiscount({
        id: 'discount-1',
        name: 'Discount 1',
        percentage: 10,
        maxDiscountAmount: 30, // Better discount: 30
        minCartValue: 300
      });
      const discount2 = createMockPercentageCapDiscount({
        id: 'discount-2',
        name: 'Discount 2',
        percentage: 5,
        maxDiscountAmount: 50,
        minCartValue: 300 // Worse discount: 20 (5% of 400)
      });

      const result = await engine.calculateDiscounts(cartItems, [discount1, discount2]);

      expect(result.totalDiscount).toBe(30);
      expect(result.appliedDiscounts).toHaveLength(1);
      expect(result.appliedDiscounts[0].discountId).toBe('discount-1');
    });

    test('should apply multiple discounts in stacking mode', async () => {
      engine.updateConfig({ allowStacking: true });
      
      const cartItems = createMockCartItems(); // Total: 400
      const discount1 = createMockPercentageCapDiscount({
        id: 'discount-1',
        name: 'Discount 1',
        percentage: 10,
        maxDiscountAmount: 30,
        minCartValue: 300
      });
      const discount2 = createMockPercentageCapDiscount({
        id: 'discount-2',
        name: 'Discount 2',
        percentage: 5,
        maxDiscountAmount: 50,
        minCartValue: 300
      });

      const result = await engine.calculateDiscounts(cartItems, [discount1, discount2]);

      expect(result.totalDiscount).toBe(50); // 30 + 20
      expect(result.appliedDiscounts).toHaveLength(2);
      expect(result.finalTotal).toBe(350);
    });

    test('should respect maximum discounts per cart limit', async () => {
      engine.updateConfig({ 
        allowStacking: true,
        maxDiscountsPerCart: 2
      });
      
      const cartItems = createMockCartItems(); // Total: 400
      const discounts = Array.from({ length: 3 }, (_, i) => 
        createMockPercentageCapDiscount({
          id: `discount-${i}`,
          name: `Discount ${i}`,
          percentage: 5,
          maxDiscountAmount: 20,
          minCartValue: 300
        })
      );

      const result = await engine.calculateDiscounts(cartItems, discounts);

      expect(result.appliedDiscounts).toHaveLength(2); // Limited to 2
      expect(result.totalDiscount).toBe(40); // 20 + 20
    });
  });

  describe('Discount Priority and Sorting', () => {
    test('should prioritize newer discounts', async () => {
      const cartItems = createMockCartItems(); // Total: 400
      const olderDiscount = createMockPercentageCapDiscount({
        id: 'older-discount',
        name: 'Older Discount',
        percentage: 15, // Better percentage
        maxDiscountAmount: 100,
        minCartValue: 300,
        createdAt: new Date('2025-01-01T10:00:00Z')
      });
      const newerDiscount = createMockPercentageCapDiscount({
        id: 'newer-discount',
        name: 'Newer Discount',
        percentage: 10, // Worse percentage but newer
        maxDiscountAmount: 30,
        minCartValue: 300,
        createdAt: new Date('2025-01-02T10:00:00Z')
      });

      const result = await engine.calculateDiscounts(cartItems, [olderDiscount, newerDiscount]);

      // Should apply the better discount (older one with 15%)
      expect(result.appliedDiscounts[0].discountId).toBe('older-discount');
      expect(result.totalDiscount).toBe(60); // 15% of 400
    });
  });

  describe('Edge Cases and Error Handling', () => {
    test('should handle empty discount list', async () => {
      const cartItems = createMockCartItems();

      const result = await engine.calculateDiscounts(cartItems, []);

      expect(result.totalDiscount).toBe(0);
      expect(result.appliedDiscounts).toHaveLength(0);
      expect(result.originalTotal).toBe(400);
      expect(result.finalTotal).toBe(400);
    });

    test('should handle inactive discounts', async () => {
      const cartItems = createMockCartItems();
      const inactiveDiscount = createMockPercentageCapDiscount({
        isActive: false
      });

      const result = await engine.calculateDiscounts(cartItems, [inactiveDiscount]);

      expect(result.totalDiscount).toBe(0);
      expect(result.appliedDiscounts).toHaveLength(0);
    });

    test('should handle expired discounts', async () => {
      const cartItems = createMockCartItems();
      const expiredDiscount = createMockPercentageCapDiscount({
        validFrom: new Date('2023-01-01'),
        validTo: new Date('2023-12-31') // Expired
      });

      const result = await engine.calculateDiscounts(cartItems, [expiredDiscount]);

      expect(result.totalDiscount).toBe(0);
      expect(result.appliedDiscounts).toHaveLength(0);
    });

    test('should handle discounts with usage limit reached', async () => {
      const cartItems = createMockCartItems();
      const limitReachedDiscount = createMockPercentageCapDiscount({
        usageCount: 100,
        maxUsage: 100 // Limit reached
      });

      const result = await engine.calculateDiscounts(cartItems, [limitReachedDiscount]);

      expect(result.totalDiscount).toBe(0);
      expect(result.appliedDiscounts).toHaveLength(0);
    });

    test('should ensure final total is never negative', async () => {
      const cartItems = createMockCartItems([
        { pricePerUnit: 10, quantity: 1 } // Total: 10
      ]);
      const highDiscount = createMockPercentageCapDiscount({
        percentage: 100,
        maxDiscountAmount: 50, // More than cart total
        minCartValue: 5
      });

      const result = await engine.calculateDiscounts(cartItems, [highDiscount]);

      expect(result.finalTotal).toBe(0); // Should not be negative
      expect(result.totalDiscount).toBe(10); // Should be limited to cart total
    });
  });

  describe('Validation Integration', () => {
    test('should validate discount through engine', () => {
      const validDiscount = createMockPercentageCapDiscount({
        percentage: 10,
        maxDiscountAmount: 50,
        minCartValue: 300
      });

      const validation = engine.validateDiscount(validDiscount);

      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    test('should return validation errors for invalid discount', () => {
      const invalidDiscount = createMockPercentageCapDiscount({
        percentage: 0, // Invalid
        maxDiscountAmount: -10 // Invalid
      });

      const validation = engine.validateDiscount(invalidDiscount);

      expect(validation.isValid).toBe(false);
      expect(validation.errors.length).toBeGreaterThan(0);
    });

    test('should validate discount combinations', () => {
      const discount1 = createMockPercentageCapDiscount({ id: 'discount-1' });
      const discount2 = createMockPercentageCapDiscount({ id: 'discount-2' });

      const validation = engine.validateDiscountCombination([discount1, discount2]);

      // Should fail in non-stacking mode
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('Discount stacking is not allowed');
    });
  });

  describe('Factory Integration', () => {
    test('should create engine with default calculators', () => {
      const factory = new DefaultDiscountEngineFactory();
      const engineWithDefaults = factory.createEngineWithDefaults();

      const registeredTypes = engineWithDefaults.getRegisteredTypes();
      expect(registeredTypes).toContain('PERCENTAGE_CAP');
    });

    test('should create engine through factory function', () => {
      const factory = createDiscountEngineFactory();
      const engine = factory.createEngineWithDefaults();

      expect(engine).toBeDefined();
      expect(engine.getRegisteredTypes()).toContain('PERCENTAGE_CAP');
    });
  });

  describe('Real-world Scenarios', () => {
    test('should handle typical e-commerce discount scenario', async () => {
      // Scenario: "10% off up to ₹100 on orders above ₹500"
      const cartItems = createMockCartItems([
        { skuId: 1, quantity: 2, pricePerUnit: 250, mrpPerUnit: 300, name: 'Product A' }, // ₹500
        { skuId: 2, quantity: 1, pricePerUnit: 150, mrpPerUnit: 200, name: 'Product B' }  // ₹150
      ]); // Total: ₹650

      const discount = createMockPercentageCapDiscount({
        name: '10% off up to ₹100 on orders above ₹500',
        percentage: 10,
        maxDiscountAmount: 100,
        minCartValue: 500
      });

      const result = await engine.calculateDiscounts(cartItems, [discount]);

      expect(result.originalTotal).toBe(650);
      expect(result.totalDiscount).toBe(65); // 10% of 650, under cap of 100
      expect(result.finalTotal).toBe(585);
      expect(result.savings).toBe(65);
    });

    test('should handle high-value cart with discount cap', async () => {
      // Scenario: High-value cart where discount hits the cap
      const cartItems = createMockCartItems([
        { skuId: 1, quantity: 1, pricePerUnit: 2000, mrpPerUnit: 2500, name: 'Premium Product' }
      ]); // Total: ₹2000

      const discount = createMockPercentageCapDiscount({
        name: '15% off up to ₹200 on orders above ₹1000',
        percentage: 15,
        maxDiscountAmount: 200, // Cap will be hit
        minCartValue: 1000
      });

      const result = await engine.calculateDiscounts(cartItems, [discount]);

      expect(result.originalTotal).toBe(2000);
      expect(result.totalDiscount).toBe(200); // Capped at 200, not 300 (15% of 2000)
      expect(result.finalTotal).toBe(1800);
    });

    test('should handle multiple competing discounts', async () => {
      const cartItems = createMockCartItems([
        { skuId: 1, quantity: 3, pricePerUnit: 200, mrpPerUnit: 250, name: 'Product' }
      ]); // Total: ₹600

      const discounts = [
        createMockPercentageCapDiscount({
          id: 'discount-1',
          name: '20% off up to ₹50',
          percentage: 20,
          maxDiscountAmount: 50, // Will be capped
          minCartValue: 500
        }),
        createMockPercentageCapDiscount({
          id: 'discount-2',
          name: '10% off up to ₹100',
          percentage: 10,
          maxDiscountAmount: 100, // Better overall
          minCartValue: 500
        }),
        createMockPercentageCapDiscount({
          id: 'discount-3',
          name: '5% off up to ₹200',
          percentage: 5,
          maxDiscountAmount: 200, // Worst option
          minCartValue: 500
        })
      ];

      const result = await engine.calculateDiscounts(cartItems, discounts);

      // Should pick discount-2 (₹60) over discount-1 (₹50) and discount-3 (₹30)
      expect(result.appliedDiscounts[0].discountId).toBe('discount-2');
      expect(result.totalDiscount).toBe(60);
    });
  });

  describe('Performance and Statistics', () => {
    test('should track calculation statistics', async () => {
      const cartItems = createMockCartItems();
      const discount = createMockPercentageCapDiscount();

      await engine.calculateDiscounts(cartItems, [discount]);
      await engine.calculateDiscounts(cartItems, [discount]);

      const stats = engine.getEngineStats();

      expect(stats.registeredCalculators).toBe(1);
      expect(stats.totalCalculations).toBe(2);
      expect(stats.averageCalculationTimeMs).toBeGreaterThanOrEqual(0);
      expect(stats.lastCalculationAt).toBeInstanceOf(Date);
    });

    test('should handle large number of discounts efficiently', async () => {
      const cartItems = createMockCartItems();
      const discounts = Array.from({ length: 50 }, (_, i) => 
        createMockPercentageCapDiscount({
          id: `discount-${i}`,
          name: `Discount ${i}`,
          percentage: 5 + (i % 10), // Vary percentages
          maxDiscountAmount: 20 + (i % 30), // Vary caps
          minCartValue: 200 + (i % 100) // Vary minimums
        })
      );

      const startTime = Date.now();
      const result = await engine.calculateDiscounts(cartItems, discounts);
      const endTime = Date.now();

      expect(endTime - startTime).toBeLessThan(100); // Should be fast
      expect(result).toBeDefined();
      expect(result.appliedDiscounts).toHaveLength(1); // Best discount applied
    });
  });
});
/**
 * Unit tests for Discount Service
 * 
 * Tests cover the service layer functionality, business logic,
 * integration with repository and engine, and error handling.
 */

import { DefaultDiscountService, createDiscountService } from '../discount.service';
import type { DiscountService, CreateDiscountInput, UpdateDiscountInput, CalculateDiscountsInput } from '../discount.service.interface';
import type { DiscountRepository } from '../discount.repository';
import type { DiscountEngine } from '../discount.engine.interface';
import type { Discount, DiscountCalculationResult, CartItemWithDetails, DiscountUsageStats } from '../discount.types';
import {
  DiscountServiceError,
  DiscountNotFoundError,
  DiscountValidationError,
  DiscountOperationError,
  DiscountCalculationError,
  ServiceNotInitializedError
} from '../discount.service.errors';

// Mock logger to avoid console output during tests
jest.mock('@/lib/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn()
  }
}));

// Test data fixtures
const createMockDiscount = (overrides: Partial<Discount> = {}): Discount => ({
  id: 'test-discount-1',
  name: 'Test Discount',
  description: 'Test discount description',
  type: 'PERCENTAGE_CAP',
  isActive: true,
  validFrom: new Date('2025-01-01'),
  validTo: new Date('2025-12-31'),
  createdAt: new Date(),
  updatedAt: new Date(),
  usageCount: 0,
  percentage: 10,
  maxDiscountAmount: 100,
  minCartValue: 500,
  maxUsage: 1000,
  ...overrides
});

const createMockCreateInput = (overrides: Partial<CreateDiscountInput> = {}): CreateDiscountInput => ({
  name: 'Test Discount',
  description: 'Test discount description',
  type: 'PERCENTAGE_CAP',
  isActive: true,
  validFrom: new Date('2025-01-01'),
  validTo: new Date('2025-12-31'),
  percentage: 10,
  maxDiscountAmount: 100,
  minCartValue: 500,
  maxUsage: 1000,
  ...overrides
});

const createMockCartItems = (): CartItemWithDetails[] => [
  {
    skuId: 1,
    quantity: 2,
    pricePerUnit: 300,
    mrpPerUnit: 350,
    name: 'Test Product 1'
  },
  {
    skuId: 2,
    quantity: 1,
    pricePerUnit: 200,
    mrpPerUnit: 250,
    name: 'Test Product 2'
  }
];

const createMockCalculationResult = (): DiscountCalculationResult => ({
  totalDiscount: 50,
  appliedDiscounts: [{
    discountId: 'test-discount-1',
    discountName: 'Test Discount',
    discountAmount: 50,
    discountType: 'PERCENTAGE_CAP'
  }],
  originalTotal: 800,
  finalTotal: 750,
  savings: 50
});

describe('DiscountService', () => {
  let service: DiscountService;
  let mockRepository: jest.Mocked<DiscountRepository>;
  let mockEngine: jest.Mocked<DiscountEngine>;

  beforeEach(async () => {
    // Create mock repository
    mockRepository = {
      create: jest.fn(),
      findById: jest.fn(),
      findAll: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      bulkDelete: jest.fn(),
      findActiveDiscounts: jest.fn(),
      findByType: jest.fn(),
      exists: jest.fn(),
      count: jest.fn(),
      search: jest.fn(),
      incrementUsage: jest.fn(),
      recordUsage: jest.fn(),
      getUsageStats: jest.fn(),
      validateDiscount: jest.fn(),
      healthCheck: jest.fn()
    };

    // Create mock engine
    mockEngine = {
      calculateDiscounts: jest.fn(),
      registerCalculator: jest.fn(),
      unregisterCalculator: jest.fn(),
      getCalculator: jest.fn(),
      getRegisteredTypes: jest.fn(),
      validateDiscount: jest.fn(),
      validateDiscountCombination: jest.fn(),
      getConfig: jest.fn(),
      updateConfig: jest.fn(),
      getEngineStats: jest.fn().mockReturnValue({
        registeredCalculators: 1,
        totalCalculations: 0,
        averageCalculationTimeMs: 0,
        errors: { total: 0, recent: [] }
      })
    };

    service = new DefaultDiscountService(mockRepository, mockEngine);
    await service.initialize();
  });

  afterEach(async () => {
    await service.cleanup();
  });

  describe('Initialization', () => {
    test('should initialize successfully', async () => {
      const newService = new DefaultDiscountService(mockRepository, mockEngine);
      await expect(newService.initialize()).resolves.toBeUndefined();
    });

    test('should be idempotent when initialized multiple times', async () => {
      await service.initialize();
      await expect(service.initialize()).resolves.toBeUndefined();
    });

    test('should handle initialization errors', async () => {
      const failingRepo = {
        ...mockRepository,
        initialize: jest.fn().mockRejectedValue(new Error('Init failed'))
      };

      const newService = new DefaultDiscountService(failingRepo as any, mockEngine);
      
      // The service doesn't actually call repository.initialize() in current implementation
      // So this test should pass without throwing
      await expect(newService.initialize()).resolves.toBeUndefined();
    });

    test('should throw error when using uninitialized service', async () => {
      const uninitializedService = new DefaultDiscountService(mockRepository, mockEngine);
      
      await expect(uninitializedService.createDiscount(createMockCreateInput()))
        .rejects.toThrow(ServiceNotInitializedError);
    });
  });

  describe('CRUD Operations', () => {
    describe('createDiscount', () => {
      test('should create discount successfully', async () => {
        const input = createMockCreateInput();
        const expectedDiscount = createMockDiscount();
        
        mockRepository.validateDiscount.mockResolvedValue([]);
        mockRepository.create.mockResolvedValue(expectedDiscount);

        const result = await service.createDiscount(input);

        expect(result.success).toBe(true);
        expect(result.data).toEqual(expectedDiscount);
        expect(mockRepository.create).toHaveBeenCalledWith(
          expect.objectContaining({
            name: input.name,
            type: input.type,
            percentage: input.percentage
          })
        );
      });

      test('should handle validation errors', async () => {
        const input = createMockCreateInput({ name: '' });
        
        mockRepository.validateDiscount.mockResolvedValue(['Name is required']);

        const result = await service.createDiscount(input);

        expect(result.success).toBe(false);
        expect(result.code).toBe('VALIDATION_ERROR');
        expect(mockRepository.create).not.toHaveBeenCalled();
      });

      test('should handle repository errors', async () => {
        const input = createMockCreateInput();
        
        mockRepository.validateDiscount.mockResolvedValue([]);
        mockRepository.create.mockRejectedValue(new Error('Database error'));

        const result = await service.createDiscount(input);

        expect(result.success).toBe(false);
        expect(result.error).toContain('Database error');
      });
    });

    describe('getDiscountById', () => {
      test('should get discount by ID successfully', async () => {
        const discount = createMockDiscount();
        mockRepository.findById.mockResolvedValue(discount);

        const result = await service.getDiscountById('test-discount-1');

        expect(result.success).toBe(true);
        expect(result.data).toEqual(discount);
        expect(mockRepository.findById).toHaveBeenCalledWith('test-discount-1');
      });

      test('should return null for non-existent discount', async () => {
        mockRepository.findById.mockResolvedValue(null);

        const result = await service.getDiscountById('non-existent');

        expect(result.success).toBe(true);
        expect(result.data).toBeNull();
      });

      test('should handle repository errors', async () => {
        mockRepository.findById.mockRejectedValue(new Error('Database error'));

        const result = await service.getDiscountById('test-discount-1');

        expect(result.success).toBe(false);
        expect(result.error).toContain('Database error');
      });
    });

    describe('listDiscounts', () => {
      test('should list discounts successfully', async () => {
        const discounts = [createMockDiscount(), createMockDiscount({ id: 'test-discount-2' })];
        const paginatedResult = {
          items: discounts,
          total: 2,
          page: 1,
          limit: 20,
          totalPages: 1
        };
        
        mockRepository.findAll.mockResolvedValue(paginatedResult);

        const result = await service.listDiscounts();

        expect(result.success).toBe(true);
        expect(result.data).toEqual(paginatedResult);
        expect(mockRepository.findAll).toHaveBeenCalled();
      });

      test('should apply filters and pagination', async () => {
        const filters = { isActive: true };
        const pagination = { page: 2, limit: 10 };
        
        mockRepository.findAll.mockResolvedValue({
          items: [],
          total: 0,
          page: 2,
          limit: 10,
          totalPages: 0
        });

        await service.listDiscounts(filters, pagination);

        expect(mockRepository.findAll).toHaveBeenCalledWith(
          expect.objectContaining({ isActive: true }),
          expect.objectContaining({ page: 2, limit: 10 })
        );
      });
    });

    describe('updateDiscount', () => {
      test('should update discount successfully', async () => {
        const existingDiscount = createMockDiscount();
        const updates: UpdateDiscountInput = { name: 'Updated Name' };
        const updatedDiscount = { ...existingDiscount, name: 'Updated Name' };
        
        mockRepository.findById.mockResolvedValue(existingDiscount);
        mockRepository.validateDiscount.mockResolvedValue([]);
        mockRepository.update.mockResolvedValue(updatedDiscount);

        const result = await service.updateDiscount('test-discount-1', updates);

        expect(result.success).toBe(true);
        expect(result.data).toEqual(updatedDiscount);
        expect(mockRepository.update).toHaveBeenCalledWith('test-discount-1', updates);
      });

      test('should handle non-existent discount', async () => {
        mockRepository.findById.mockResolvedValue(null);

        const result = await service.updateDiscount('non-existent', { name: 'New Name' });

        expect(result.success).toBe(false);
        expect(result.code).toBe('DISCOUNT_NOT_FOUND');
        expect(mockRepository.update).not.toHaveBeenCalled();
      });

      test('should handle validation errors', async () => {
        const existingDiscount = createMockDiscount();
        
        mockRepository.findById.mockResolvedValue(existingDiscount);
        mockRepository.validateDiscount.mockResolvedValue(['Invalid data']);

        const result = await service.updateDiscount('test-discount-1', { percentage: -10 });

        expect(result.success).toBe(false);
        expect(result.code).toBe('VALIDATION_ERROR');
        expect(mockRepository.update).not.toHaveBeenCalled();
      });
    });

    describe('deleteDiscount', () => {
      test('should delete discount successfully', async () => {
        mockRepository.delete.mockResolvedValue(true);

        const result = await service.deleteDiscount('test-discount-1');

        expect(result.success).toBe(true);
        expect(result.data).toBe(true);
        expect(mockRepository.delete).toHaveBeenCalledWith('test-discount-1');
      });

      test('should handle non-existent discount', async () => {
        mockRepository.delete.mockResolvedValue(false);

        const result = await service.deleteDiscount('non-existent');

        expect(result.success).toBe(true);
        expect(result.data).toBe(false);
      });
    });

    describe('bulkDeleteDiscounts', () => {
      test('should bulk delete discounts successfully', async () => {
        const ids = ['discount-1', 'discount-2', 'discount-3'];
        mockRepository.bulkDelete.mockResolvedValue(2);

        const result = await service.bulkDeleteDiscounts(ids);

        expect(result.success).toBe(true);
        expect(result.data?.successCount).toBe(2);
        expect(result.data?.failureCount).toBe(1);
        expect(mockRepository.bulkDelete).toHaveBeenCalledWith(ids);
      });
    });
  });

  describe('Discount Calculation', () => {
    describe('calculateDiscounts', () => {
      test('should calculate discounts successfully', async () => {
        const cartItems = createMockCartItems();
        const activeDiscounts = [createMockDiscount()];
        const calculationResult = createMockCalculationResult();
        const input: CalculateDiscountsInput = {
          cartItems,
          cartId: 'cart-123',
          applyDiscounts: true
        };

        mockRepository.findActiveDiscounts.mockResolvedValue(activeDiscounts);
        mockEngine.calculateDiscounts.mockResolvedValue(calculationResult);
        mockRepository.recordUsage.mockResolvedValue(undefined);

        const result = await service.calculateDiscounts(input);

        expect(result.success).toBe(true);
        expect(result.data).toEqual(calculationResult);
        expect(mockEngine.calculateDiscounts).toHaveBeenCalledWith(cartItems, activeDiscounts);
      });

      test('should record usage when discounts are applied', async () => {
        const cartItems = createMockCartItems();
        const activeDiscounts = [createMockDiscount()];
        const calculationResult = createMockCalculationResult();
        const input: CalculateDiscountsInput = {
          cartItems,
          cartId: 'cart-123',
          applyDiscounts: true
        };

        mockRepository.findActiveDiscounts.mockResolvedValue(activeDiscounts);
        mockEngine.calculateDiscounts.mockResolvedValue(calculationResult);
        mockRepository.recordUsage.mockResolvedValue(undefined);

        await service.calculateDiscounts(input);

        expect(mockRepository.recordUsage).toHaveBeenCalledWith(
          expect.objectContaining({
            discountId: 'test-discount-1',
            cartId: 'cart-123',
            discountAmount: 50,
            cartTotal: 800
          })
        );
      });

      test('should not record usage when applyDiscounts is false', async () => {
        const cartItems = createMockCartItems();
        const activeDiscounts = [createMockDiscount()];
        const calculationResult = createMockCalculationResult();
        const input: CalculateDiscountsInput = {
          cartItems,
          applyDiscounts: false
        };

        mockRepository.findActiveDiscounts.mockResolvedValue(activeDiscounts);
        mockEngine.calculateDiscounts.mockResolvedValue(calculationResult);

        await service.calculateDiscounts(input);

        expect(mockRepository.recordUsage).not.toHaveBeenCalled();
      });

      test('should handle calculation errors gracefully', async () => {
        const cartItems = createMockCartItems();
        const input: CalculateDiscountsInput = { cartItems };

        mockRepository.findActiveDiscounts.mockResolvedValue([]);
        mockEngine.calculateDiscounts.mockRejectedValue(new Error('Calculation failed'));

        const result = await service.calculateDiscounts(input);

        expect(result.success).toBe(false);
        expect(result.error).toContain('Calculation failed');
      });

      test('should continue calculation even if usage recording fails', async () => {
        const cartItems = createMockCartItems();
        const activeDiscounts = [createMockDiscount()];
        const calculationResult = createMockCalculationResult();
        const input: CalculateDiscountsInput = {
          cartItems,
          cartId: 'cart-123',
          applyDiscounts: true
        };

        mockRepository.findActiveDiscounts.mockResolvedValue(activeDiscounts);
        mockEngine.calculateDiscounts.mockResolvedValue(calculationResult);
        mockRepository.recordUsage.mockRejectedValue(new Error('Recording failed'));

        const result = await service.calculateDiscounts(input);

        expect(result.success).toBe(true);
        expect(result.data).toEqual(calculationResult);
      });
    });

    describe('checkDiscountEligibility', () => {
      test('should check eligibility for valid discount', async () => {
        const discount = createMockDiscount();
        const cartItems = createMockCartItems();
        const calculationResult = createMockCalculationResult();

        mockRepository.findById.mockResolvedValue(discount);
        mockEngine.calculateDiscounts.mockResolvedValue(calculationResult);

        const result = await service.checkDiscountEligibility('test-discount-1', cartItems);

        expect(result.success).toBe(true);
        expect(result.data?.eligible).toBe(true);
        expect(result.data?.estimatedDiscount).toBe(50);
      });

      test('should return ineligible for non-existent discount', async () => {
        const cartItems = createMockCartItems();

        mockRepository.findById.mockResolvedValue(null);

        const result = await service.checkDiscountEligibility('non-existent', cartItems);

        expect(result.success).toBe(true);
        expect(result.data?.eligible).toBe(false);
        expect(result.data?.reason).toBe('Discount not found');
      });

      test('should return ineligible for inactive discount', async () => {
        const discount = createMockDiscount({ isActive: false });
        const cartItems = createMockCartItems();

        mockRepository.findById.mockResolvedValue(discount);

        const result = await service.checkDiscountEligibility('test-discount-1', cartItems);

        expect(result.success).toBe(true);
        expect(result.data?.eligible).toBe(false);
        expect(result.data?.reason).toBe('Discount is not active');
      });

      test('should return ineligible for expired discount', async () => {
        const discount = createMockDiscount({
          validFrom: new Date('2020-01-01'),
          validTo: new Date('2020-12-31')
        });
        const cartItems = createMockCartItems();

        mockRepository.findById.mockResolvedValue(discount);

        const result = await service.checkDiscountEligibility('test-discount-1', cartItems);

        expect(result.success).toBe(true);
        expect(result.data?.eligible).toBe(false);
        expect(result.data?.reason).toBe('Discount is not valid at this time');
      });

      test('should return ineligible when usage limit reached', async () => {
        const discount = createMockDiscount({
          maxUsage: 100,
          usageCount: 100
        });
        const cartItems = createMockCartItems();

        mockRepository.findById.mockResolvedValue(discount);

        const result = await service.checkDiscountEligibility('test-discount-1', cartItems);

        expect(result.success).toBe(true);
        expect(result.data?.eligible).toBe(false);
        expect(result.data?.reason).toBe('Discount usage limit reached');
      });

      test('should return ineligible when cart does not meet requirements', async () => {
        const discount = createMockDiscount();
        const cartItems = createMockCartItems();
        const calculationResult = {
          ...createMockCalculationResult(),
          appliedDiscounts: [] // No discounts applied
        };

        mockRepository.findById.mockResolvedValue(discount);
        mockEngine.calculateDiscounts.mockResolvedValue(calculationResult);

        const result = await service.checkDiscountEligibility('test-discount-1', cartItems);

        expect(result.success).toBe(true);
        expect(result.data?.eligible).toBe(false);
        expect(result.data?.reason).toBe('Cart does not meet discount requirements');
      });
    });
  });

  describe('Query Operations', () => {
    describe('getActiveDiscounts', () => {
      test('should get active discounts successfully', async () => {
        const activeDiscounts = [createMockDiscount()];
        mockRepository.findActiveDiscounts.mockResolvedValue(activeDiscounts);

        const result = await service.getActiveDiscounts();

        expect(result.success).toBe(true);
        expect(result.data).toEqual(activeDiscounts);
        expect(mockRepository.findActiveDiscounts).toHaveBeenCalled();
      });

      test('should use cache for subsequent calls', async () => {
        const activeDiscounts = [createMockDiscount()];
        mockRepository.findActiveDiscounts.mockResolvedValue(activeDiscounts);

        // First call
        await service.getActiveDiscounts();
        // Second call
        await service.getActiveDiscounts();

        // Repository should only be called once due to caching
        expect(mockRepository.findActiveDiscounts).toHaveBeenCalledTimes(1);
      });

      test('should get active discounts for specific date', async () => {
        const specificDate = new Date('2025-06-01');
        const activeDiscounts = [createMockDiscount()];
        mockRepository.findActiveDiscounts.mockResolvedValue(activeDiscounts);

        const result = await service.getActiveDiscounts(specificDate);

        expect(result.success).toBe(true);
        expect(mockRepository.findActiveDiscounts).toHaveBeenCalledWith(specificDate);
      });
    });

    describe('searchDiscounts', () => {
      test('should search discounts successfully', async () => {
        const searchResults = [createMockDiscount()];
        mockRepository.search.mockResolvedValue(searchResults);

        const result = await service.searchDiscounts('test query');

        expect(result.success).toBe(true);
        expect(result.data).toEqual(searchResults);
        expect(mockRepository.search).toHaveBeenCalledWith('test query', undefined);
      });

      test('should search with filters', async () => {
        const searchResults = [createMockDiscount()];
        const filters = { isActive: true };
        mockRepository.search.mockResolvedValue(searchResults);

        await service.searchDiscounts('test query', filters);

        expect(mockRepository.search).toHaveBeenCalledWith('test query', expect.objectContaining(filters));
      });
    });

    describe('getDiscountsByType', () => {
      test('should get discounts by type successfully', async () => {
        const discounts = [createMockDiscount()];
        mockRepository.findByType.mockResolvedValue(discounts);

        const result = await service.getDiscountsByType('PERCENTAGE_CAP');

        expect(result.success).toBe(true);
        expect(result.data).toEqual(discounts);
        expect(mockRepository.findByType).toHaveBeenCalledWith('PERCENTAGE_CAP');
      });
    });
  });

  describe('Analytics and Usage', () => {
    describe('getUsageAnalytics', () => {
      test('should get usage analytics successfully', async () => {
        const mockStats: DiscountUsageStats = {
          totalApplications: 10,
          totalSavings: 500,
          averageDiscountAmount: 50,
          averageCartValue: 800,
          usageByDate: []
        };
        
        // Mock the repository to return UsageStats format
        mockRepository.getUsageStats.mockResolvedValue({
          totalApplications: 10,
          totalSavings: 500,
          averageDiscountAmount: 50,
          dateRange: { from: new Date(), to: new Date() },
          dailyStats: []
        });

        const result = await service.getUsageAnalytics();

        expect(result.success).toBe(true);
        expect(result.data?.totalApplications).toBe(10);
        expect(result.data?.totalSavings).toBe(500);
      });

      test('should get usage analytics with filters', async () => {
        const options = {
          discountId: 'test-discount-1',
          dateFrom: new Date('2025-01-01'),
          dateTo: new Date('2025-12-31')
        };

        mockRepository.getUsageStats.mockResolvedValue({
          discountId: 'test-discount-1',
          totalApplications: 5,
          totalSavings: 250,
          averageDiscountAmount: 50,
          dateRange: { from: new Date(), to: new Date() },
          dailyStats: []
        });

        await service.getUsageAnalytics(options);

        expect(mockRepository.getUsageStats).toHaveBeenCalledWith({
          discountId: 'test-discount-1',
          dateFrom: options.dateFrom,
          dateTo: options.dateTo
        });
      });
    });

    describe('recordDiscountUsage', () => {
      test('should record usage successfully', async () => {
        mockRepository.recordUsage.mockResolvedValue(undefined);

        const result = await service.recordDiscountUsage('test-discount-1', 'cart-123', 50, 800);

        expect(result.success).toBe(true);
        expect(mockRepository.recordUsage).toHaveBeenCalledWith(
          expect.objectContaining({
            discountId: 'test-discount-1',
            cartId: 'cart-123',
            discountAmount: 50,
            cartTotal: 800
          })
        );
      });

      test('should handle recording errors', async () => {
        mockRepository.recordUsage.mockRejectedValue(new Error('Recording failed'));

        const result = await service.recordDiscountUsage('test-discount-1', 'cart-123', 50, 800);

        expect(result.success).toBe(false);
        expect(result.error).toContain('Recording failed');
      });
    });
  });

  describe('Validation', () => {
    test('should validate discount successfully', async () => {
      const input = createMockCreateInput();
      mockRepository.validateDiscount.mockResolvedValue([]);

      const result = await service.validateDiscount(input);

      expect(result.success).toBe(true);
      expect(result.data).toEqual([]);
      expect(mockRepository.validateDiscount).toHaveBeenCalledWith(input);
    });

    test('should return validation errors', async () => {
      const input = createMockCreateInput({ name: '' });
      const errors = ['Name is required'];
      mockRepository.validateDiscount.mockResolvedValue(errors);

      const result = await service.validateDiscount(input);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(errors);
    });
  });

  describe('Health and Statistics', () => {
    describe('getHealthStatus', () => {
      test('should return healthy status', async () => {
        mockRepository.healthCheck.mockResolvedValue({
          status: 'healthy',
          details: { initialized: true }
        });

        const result = await service.getHealthStatus();

        expect(result.status).toBe('healthy');
        expect(result.details.initialized).toBe(true);
        expect(result.details.repository).toBeDefined();
        expect(result.details.engine).toBeDefined();
      });

      test('should return unhealthy status when not initialized', async () => {
        const uninitializedService = new DefaultDiscountService(mockRepository, mockEngine);
        
        const result = await uninitializedService.getHealthStatus();

        expect(result.status).toBe('unhealthy');
        expect(result.details.initialized).toBe(false);
      });

      test('should return degraded status when repository is degraded', async () => {
        mockRepository.healthCheck.mockResolvedValue({
          status: 'degraded',
          details: { message: 'Some issues' }
        });

        const result = await service.getHealthStatus();

        expect(result.status).toBe('degraded');
      });
    });

    describe('getServiceStats', () => {
      test('should get service statistics successfully', async () => {
        mockRepository.count.mockResolvedValue(10);
        mockRepository.findActiveDiscounts.mockResolvedValue([createMockDiscount()]);
        mockRepository.getUsageStats.mockResolvedValue({
          totalApplications: 50,
          totalSavings: 1000,
          averageDiscountAmount: 20,
          dateRange: { from: new Date(), to: new Date() }
        });

        const result = await service.getServiceStats();

        expect(result.totalDiscounts).toBe(10);
        expect(result.activeDiscounts).toBe(1);
        expect(result.totalUsage).toBe(50);
        expect(result.averageDiscountAmount).toBe(20);
      });

      test('should handle errors gracefully', async () => {
        mockRepository.count.mockRejectedValue(new Error('Database error'));

        const result = await service.getServiceStats();

        expect(result.totalDiscounts).toBe(0);
        expect(result.activeDiscounts).toBe(0);
        expect(result.totalUsage).toBe(0);
        expect(result.averageDiscountAmount).toBe(0);
      });
    });
  });

  describe('Factory Function', () => {
    test('should create service with factory function', async () => {
      const factoryService = createDiscountService(mockRepository, mockEngine);
      await factoryService.initialize();

      expect(factoryService).toBeInstanceOf(DefaultDiscountService);

      const result = await factoryService.getServiceStats();
      expect(result).toBeDefined();
    });

    test('should create service with custom config', async () => {
      const config = {
        enableDetailedLogging: true,
        maxDiscountsPerCart: 3
      };

      const factoryService = createDiscountService(mockRepository, mockEngine, config);
      await factoryService.initialize();

      expect(factoryService).toBeInstanceOf(DefaultDiscountService);
    });
  });

  describe('Error Handling', () => {
    test('should handle repository errors gracefully', async () => {
      mockRepository.findById.mockRejectedValue(new Error('Database connection failed'));

      const result = await service.getDiscountById('test-discount-1');

      expect(result.success).toBe(false);
      expect(result.error).toContain('Database connection failed');
    });

    test('should handle engine errors gracefully', async () => {
      const cartItems = createMockCartItems();
      const input: CalculateDiscountsInput = { cartItems };

      mockRepository.findActiveDiscounts.mockResolvedValue([createMockDiscount()]);
      mockEngine.calculateDiscounts.mockRejectedValue(new Error('Engine calculation failed'));

      const result = await service.calculateDiscounts(input);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Engine calculation failed');
    });

    test('should wrap unknown errors appropriately', async () => {
      mockRepository.findById.mockRejectedValue('String error');

      const result = await service.getDiscountById('test-discount-1');

      expect(result.success).toBe(false);
      expect(result.error).toBe('Unknown error');
    });
  });
});
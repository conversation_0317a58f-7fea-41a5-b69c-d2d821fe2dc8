/**
 * Simple unit tests for Discount Repository
 * 
 * Tests basic repository functionality with the current implementation.
 */

import { DefaultDiscountRepository } from '../discount.repository';
import { InMemoryDiscountStorageProvider } from '../storage/in-memory.provider';
import type { Discount } from '../discount.types';

// Mock logger to avoid console output during tests
jest.mock('@/lib/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn()
  }
}));

describe('DiscountRepository - Basic Tests', () => {
  let repository: DefaultDiscountRepository;
  let storageProvider: InMemoryDiscountStorageProvider;

  beforeEach(() => {
    storageProvider = new InMemoryDiscountStorageProvider();
    repository = new DefaultDiscountRepository({ storageProvider });
  });

  test('should create and find a discount', async () => {
    const discountData = {
      name: 'Test Discount',
      description: 'Test discount description',
      type: 'PERCENTAGE_CAP' as const,
      isActive: true,
      validFrom: new Date('2025-01-01'),
      validTo: new Date('2025-12-31'),
      percentage: 10,
      maxDiscountAmount: 100,
      minCartValue: 500,
      maxUsage: 1000
    };

    const created = await repository.create(discountData);
    
    expect(created.id).toBeDefined();
    expect(created.name).toBe('Test Discount');
    expect(created.usageCount).toBe(0);

    const found = await repository.findById(created.id);
    expect(found).toEqual(created);
  });

  test('should list all discounts', async () => {
    const discount1 = {
      name: 'Discount 1',
      type: 'PERCENTAGE_CAP' as const,
      isActive: true,
      validFrom: new Date('2025-01-01'),
      validTo: new Date('2025-12-31'),
      percentage: 10,
      maxDiscountAmount: 100,
      minCartValue: 500
    };

    const discount2 = {
      name: 'Discount 2',
      type: 'PERCENTAGE_CAP' as const,
      isActive: false,
      validFrom: new Date('2025-01-01'),
      validTo: new Date('2025-12-31'),
      percentage: 15,
      maxDiscountAmount: 150,
      minCartValue: 600
    };

    await repository.create(discount1);
    await repository.create(discount2);

    const result = await repository.findAll();
    
    expect(result.items.length).toBe(2);
    expect(result.total).toBe(2);
  });

  test('should update a discount', async () => {
    const discountData = {
      name: 'Original Name',
      type: 'PERCENTAGE_CAP' as const,
      isActive: true,
      validFrom: new Date('2025-01-01'),
      validTo: new Date('2025-12-31'),
      percentage: 10,
      maxDiscountAmount: 100,
      minCartValue: 500
    };

    const created = await repository.create(discountData);
    const updated = await repository.update(created.id, { name: 'Updated Name' });
    
    expect(updated.name).toBe('Updated Name');
    expect(updated.id).toBe(created.id);
  });

  test('should delete a discount', async () => {
    const discountData = {
      name: 'To Delete',
      type: 'PERCENTAGE_CAP' as const,
      isActive: true,
      validFrom: new Date('2025-01-01'),
      validTo: new Date('2025-12-31'),
      percentage: 10,
      maxDiscountAmount: 100,
      minCartValue: 500
    };

    const created = await repository.create(discountData);
    const deleted = await repository.delete(created.id);
    
    expect(deleted).toBe(true);
    
    const found = await repository.findById(created.id);
    expect(found).toBeNull();
  });

  test('should track usage', async () => {
    const discountData = {
      name: 'Usage Test',
      type: 'PERCENTAGE_CAP' as const,
      isActive: true,
      validFrom: new Date('2025-01-01'),
      validTo: new Date('2025-12-31'),
      percentage: 10,
      maxDiscountAmount: 100,
      minCartValue: 500
    };

    const created = await repository.create(discountData);
    
    await repository.incrementUsage(created.id);
    
    const updated = await repository.findById(created.id);
    expect(updated?.usageCount).toBe(1);
  });

  test('should return health status', async () => {
    const health = await repository.healthCheck();
    
    expect(health.status).toBeDefined();
    expect(['healthy', 'degraded', 'unhealthy']).toContain(health.status);
  });
});
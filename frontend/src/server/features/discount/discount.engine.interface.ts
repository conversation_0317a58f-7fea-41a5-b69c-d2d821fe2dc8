/**
 * Discount Engine Interface
 * 
 * This file defines the core interfaces for the discount calculation engine,
 * providing a pluggable architecture that supports multiple discount types
 * and extensible calculator registration.
 */

import type {
  Discount,
  DiscountCalculationResult,
  CartItemWithDetails,
  DiscountType
} from './discount.types';
import {
  DiscountCalculationError as SharedDiscountCalculationError,
  DiscountValidationError as SharedDiscountValidationError,
  createEngineCalculationError,
  createEngineValidationError
} from '../../shared/utils/errors';

/**
 * Calculator interface for specific discount types
 * 
 * Each discount type (PERCENTAGE_CAP, FIXED_AMOUNT, etc.) implements this interface
 * to provide type-specific calculation logic.
 */
export interface DiscountCalculator {
  /**
   * The discount type this calculator handles
   */
  readonly type: DiscountType;

  /**
   * Check if this calculator can apply the given discount to the cart
   * 
   * @param discount - The discount rule to evaluate
   * @param cartItems - The cart items to check against
   * @returns true if the discount can be applied, false otherwise
   */
  canApply(discount: Discount, cartItems: CartItemWithDetails[]): boolean;

  /**
   * Calculate the discount amount for the given cart
   * 
   * @param discount - The discount rule to apply
   * @param cartItems - The cart items to calculate discount for
   * @returns The calculated discount amount (always positive)
   */
  calculate(discount: Discount, cartItems: CartItemWithDetails[]): number;

  /**
   * Validate that the discount configuration is valid for this calculator
   * 
   * @param discount - The discount to validate
   * @returns Array of validation error messages (empty if valid)
   */
  validate(discount: Discount): string[];
}

/**
 * Configuration options for the discount engine
 */
export interface DiscountEngineConfig {
  /**
   * Maximum number of discounts that can be applied to a single cart
   * @default 10
   */
  maxDiscountsPerCart?: number;

  /**
   * Whether to allow discount stacking (multiple discounts on same cart)
   * @default false
   */
  allowStacking?: boolean;

  /**
   * Timeout for discount calculations in milliseconds
   * @default 5000
   */
  calculationTimeoutMs?: number;

  /**
   * Whether to log detailed calculation steps for debugging
   * @default false
   */
  enableDebugLogging?: boolean;
}

/**
 * Result of discount engine validation
 */
export interface DiscountEngineValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Main discount engine interface
 * 
 * The engine coordinates discount calculations across multiple discount types
 * using registered calculators. It provides a unified interface for discount
 * calculation while maintaining extensibility for new discount types.
 */
export interface DiscountEngine {
  /**
   * Calculate applicable discounts for a cart
   * 
   * @param cartItems - The items in the cart
   * @param availableDiscounts - The discount rules to consider
   * @returns Promise resolving to calculation results
   */
  calculateDiscounts(
    cartItems: CartItemWithDetails[],
    availableDiscounts: Discount[]
  ): Promise<DiscountCalculationResult>;

  /**
   * Register a calculator for a specific discount type
   * 
   * @param calculator - The calculator implementation to register
   * @throws Error if a calculator for this type is already registered
   */
  registerCalculator(calculator: DiscountCalculator): void;

  /**
   * Unregister a calculator for a specific discount type
   * 
   * @param type - The discount type to unregister
   * @returns true if a calculator was unregistered, false if none was found
   */
  unregisterCalculator(type: DiscountType): boolean;

  /**
   * Get the registered calculator for a discount type
   * 
   * @param type - The discount type to get calculator for
   * @returns The calculator if registered, undefined otherwise
   */
  getCalculator(type: DiscountType): DiscountCalculator | undefined;

  /**
   * Get all registered discount types
   * 
   * @returns Array of registered discount types
   */
  getRegisteredTypes(): DiscountType[];

  /**
   * Validate a discount against its registered calculator
   * 
   * @param discount - The discount to validate
   * @returns Validation result with errors and warnings
   */
  validateDiscount(discount: Discount): DiscountEngineValidationResult;

  /**
   * Validate multiple discounts for potential conflicts
   * 
   * @param discounts - The discounts to validate together
   * @returns Validation result for the discount combination
   */
  validateDiscountCombination(discounts: Discount[]): DiscountEngineValidationResult;

  /**
   * Get the current engine configuration
   * 
   * @returns Current configuration settings
   */
  getConfig(): DiscountEngineConfig;

  /**
   * Update engine configuration
   * 
   * @param config - New configuration settings (merged with existing)
   */
  updateConfig(config: Partial<DiscountEngineConfig>): void;

  /**
   * Get engine statistics and health information
   * 
   * @returns Engine health and performance metrics
   */
  getEngineStats(): {
    registeredCalculators: number;
    totalCalculations: number;
    averageCalculationTimeMs: number;
    lastCalculationAt?: Date;
    errors: {
      total: number;
      recent: Array<{
        timestamp: Date;
        error: string;
        discountType?: DiscountType;
      }>;
    };
  };
}

/**
 * Factory interface for creating discount engines
 */
export interface DiscountEngineFactory {
  /**
   * Create a new discount engine instance
   * 
   * @param config - Optional configuration for the engine
   * @returns A new discount engine instance
   */
  createEngine(config?: DiscountEngineConfig): DiscountEngine;

  /**
   * Create an engine with default calculators registered
   * 
   * @param config - Optional configuration for the engine
   * @returns A new engine with standard calculators pre-registered
   */
  createEngineWithDefaults(config?: DiscountEngineConfig): DiscountEngine;
}

/**
 * Error types specific to discount engine operations
 */
export class DiscountEngineError extends Error {
  constructor(
    message: string,
    public readonly code: string,
    public readonly discountType?: DiscountType
  ) {
    super(message);
    this.name = 'DiscountEngineError';
  }
}

export class CalculatorRegistrationError extends DiscountEngineError {
  constructor(type: DiscountType, message: string) {
    super(`Calculator registration failed for type ${type}: ${message}`, 'CALCULATOR_REGISTRATION_ERROR', type);
  }
}

// Export shared error classes with aliases for backward compatibility
export const DiscountCalculationError = SharedDiscountCalculationError;
export const DiscountValidationError = SharedDiscountValidationError;

// Factory functions for engine-specific error creation
export { createEngineCalculationError, createEngineValidationError };

/**
 * Utility functions for discount engine operations
 */
export const DiscountEngineUtils = {
  /**
   * Calculate cart total from cart items
   */
  calculateCartTotal(cartItems: CartItemWithDetails[]): number {
    return cartItems.reduce((total, item) => {
      return total + (item.pricePerUnit * item.quantity);
    }, 0);
  },

  /**
   * Validate cart items for discount calculation
   */
  validateCartItems(cartItems: CartItemWithDetails[]): string[] {
    const errors: string[] = [];

    if (!Array.isArray(cartItems)) {
      errors.push('Cart items must be an array');
      return errors;
    }

    if (cartItems.length === 0) {
      errors.push('Cart cannot be empty');
      return errors;
    }

    cartItems.forEach((item, index) => {
      if (!item.skuId || item.skuId <= 0) {
        errors.push(`Item ${index + 1}: Invalid SKU ID`);
      }

      if (!item.quantity || item.quantity <= 0) {
        errors.push(`Item ${index + 1}: Quantity must be greater than 0`);
      }

      if (item.pricePerUnit < 0) {
        errors.push(`Item ${index + 1}: Price cannot be negative`);
      }

      if (item.mrpPerUnit < 0) {
        errors.push(`Item ${index + 1}: MRP cannot be negative`);
      }
    });

    return errors;
  },

  /**
   * Sort discounts by priority for application order
   */
  sortDiscountsByPriority(discounts: Discount[]): Discount[] {
    return discounts.sort((a, b) => {
      // Sort by creation date (newer first) as default priority
      return b.createdAt.getTime() - a.createdAt.getTime();
    });
  },

  /**
   * Check if two discounts can be combined
   */
  canCombineDiscounts(discount1: Discount, discount2: Discount): boolean {
    // For now, don't allow combining discounts of the same type
    return discount1.type !== discount2.type;
  }
};
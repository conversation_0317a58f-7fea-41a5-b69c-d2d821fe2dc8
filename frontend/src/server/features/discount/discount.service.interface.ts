/**
 * Discount Service Interface
 * 
 * This file defines the interface for the discount service layer,
 * which provides business logic for discount operations and integrates
 * the repository and calculation engine.
 */

import type {
  Discount,
  DiscountType,
  DiscountCalculationResult,
  CartItemWithDetails,
  DiscountUsageStats,
  DiscountFilters,
  RepositoryPaginationOptions,
  PaginatedResult
} from './discount.types';

/**
 * Service-level pagination options
 */
export interface ServicePaginationOptions extends RepositoryPaginationOptions {
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

/**
 * Service-level filter options
 */
export interface ServiceDiscountFilters extends DiscountFilters {
  includeInactive?: boolean;
  includeExpired?: boolean;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

/**
 * Discount creation input (service level)
 */
export interface CreateDiscountInput {
  name: string;
  description?: string;
  type: DiscountType;
  isActive?: boolean;
  validFrom: Date;
  validTo: Date;
  percentage?: number;
  maxDiscountAmount?: number;
  minCartValue?: number;
  maxUsage?: number;
}

/**
 * Discount update input (service level)
 */
export interface UpdateDiscountInput {
  name?: string;
  description?: string;
  isActive?: boolean;
  validFrom?: Date;
  validTo?: Date;
  percentage?: number;
  maxDiscountAmount?: number;
  minCartValue?: number;
  maxUsage?: number;
}

/**
 * Cart calculation input
 */
export interface CalculateDiscountsInput {
  cartItems: CartItemWithDetails[];
  cartId?: string;
  userId?: string;
  applyDiscounts?: boolean;
}

/**
 * Service operation result wrapper
 */
export interface ServiceResult<T> {
  success: boolean;
  data?: T;
  error?: string;
  code?: string;
}

/**
 * Bulk operation result
 */
export interface BulkOperationResult {
  successCount: number;
  failureCount: number;
  errors: Array<{
    id: string;
    error: string;
  }>;
}

/**
 * Analytics options
 */
export interface AnalyticsOptions {
  discountId?: string;
  dateFrom?: Date;
  dateTo?: Date;
  includeDetails?: boolean;
}

/**
 * Discount service interface
 */
export interface DiscountService {
  /**
   * Initialize the service
   */
  initialize(): Promise<void>;

  /**
   * Clean up service resources
   */
  cleanup(): Promise<void>;

  /**
   * Create a new discount
   * 
   * @param input - The discount data to create
   * @returns Service result with the created discount
   */
  createDiscount(input: CreateDiscountInput): Promise<ServiceResult<Discount>>;

  /**
   * Get a discount by ID
   * 
   * @param id - The discount ID
   * @returns Service result with the discount or null if not found
   */
  getDiscountById(id: string): Promise<ServiceResult<Discount | null>>;

  /**
   * List discounts with filtering and pagination
   * 
   * @param filters - Optional filters to apply
   * @param pagination - Optional pagination options
   * @returns Service result with paginated discounts
   */
  listDiscounts(
    filters?: ServiceDiscountFilters,
    pagination?: ServicePaginationOptions
  ): Promise<ServiceResult<PaginatedResult<Discount>>>;

  /**
   * Update an existing discount
   * 
   * @param id - The discount ID to update
   * @param input - The fields to update
   * @returns Service result with the updated discount
   */
  updateDiscount(id: string, input: UpdateDiscountInput): Promise<ServiceResult<Discount>>;

  /**
   * Delete a discount
   * 
   * @param id - The discount ID to delete
   * @returns Service result with deletion status
   */
  deleteDiscount(id: string): Promise<ServiceResult<boolean>>;

  /**
   * Delete multiple discounts
   * 
   * @param ids - Array of discount IDs to delete
   * @returns Service result with bulk operation result
   */
  bulkDeleteDiscounts(ids: string[]): Promise<ServiceResult<BulkOperationResult>>;

  /**
   * Calculate discounts for a cart
   * 
   * @param input - Cart calculation input
   * @returns Service result with discount calculation result
   */
  calculateDiscounts(input: CalculateDiscountsInput): Promise<ServiceResult<DiscountCalculationResult>>;

  /**
   * Get active discounts
   * 
   * @param date - Optional date to check validity (defaults to current date)
   * @returns Service result with active discounts
   */
  getActiveDiscounts(date?: Date): Promise<ServiceResult<Discount[]>>;

  /**
   * Search discounts
   * 
   * @param query - Search query
   * @param filters - Optional additional filters
   * @returns Service result with matching discounts
   */
  searchDiscounts(query: string, filters?: ServiceDiscountFilters): Promise<ServiceResult<Discount[]>>;

  /**
   * Get discounts by type
   * 
   * @param type - The discount type
   * @returns Service result with matching discounts
   */
  getDiscountsByType(type: DiscountType): Promise<ServiceResult<Discount[]>>;

  /**
   * Validate a discount
   * 
   * @param input - The discount data to validate
   * @returns Service result with validation errors (empty array if valid)
   */
  validateDiscount(input: CreateDiscountInput | UpdateDiscountInput): Promise<ServiceResult<string[]>>;

  /**
   * Get usage statistics
   * 
   * @param options - Analytics options
   * @returns Service result with usage statistics
   */
  getUsageAnalytics(options?: AnalyticsOptions): Promise<ServiceResult<DiscountUsageStats>>;

  /**
   * Record discount usage (called when discount is applied)
   * 
   * @param discountId - The discount ID
   * @param cartId - The cart ID
   * @param discountAmount - The amount of discount applied
   * @param cartTotal - The total cart value
   * @returns Service result with operation status
   */
  recordDiscountUsage(
    discountId: string,
    cartId: string,
    discountAmount: number,
    cartTotal: number
  ): Promise<ServiceResult<void>>;

  /**
   * Check if a discount can be applied to a cart
   * 
   * @param discountId - The discount ID
   * @param cartItems - The cart items
   * @returns Service result with eligibility status and reason
   */
  checkDiscountEligibility(
    discountId: string,
    cartItems: CartItemWithDetails[]
  ): Promise<ServiceResult<{
    eligible: boolean;
    reason?: string;
    estimatedDiscount?: number;
  }>>;

  /**
   * Get service health status
   * 
   * @returns Service health information
   */
  getHealthStatus(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    details: Record<string, any>;
  }>;

  /**
   * Get service statistics
   * 
   * @returns Service statistics
   */
  getServiceStats(): Promise<{
    totalDiscounts: number;
    activeDiscounts: number;
    totalUsage: number;
    averageDiscountAmount: number;
  }>;
}
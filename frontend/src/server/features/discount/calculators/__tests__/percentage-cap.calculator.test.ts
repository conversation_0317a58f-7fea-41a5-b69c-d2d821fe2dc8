/**
 * Unit tests for Percentage Cap Discount Calculator
 * 
 * Tests cover the percentage cap calculator logic, validation,
 * edge cases, and error handling scenarios.
 */

import { PercentageCapDiscountCalculator } from '../percentage-cap.calculator';
import type {
    Discount,
    CartItemWithDetails,
    PercentageCapDiscount
} from '../../discount.types';

// Mock logger to avoid console output during tests
jest.mock('@/lib/logger', () => ({
    logger: {
        info: jest.fn(),
        error: jest.fn(),
        warn: jest.fn(),
        debug: jest.fn()
    }
}));

// Test data fixtures
const createMockCartItems = (overrides: Partial<CartItemWithDetails>[] = []): CartItemWithDetails[] => {
    const defaults: CartItemWithDetails[] = [
        {
            skuId: 1,
            quantity: 2,
            pricePerUnit: 100,
            mrpPerUnit: 120,
            name: 'Test Product 1'
        },
        {
            skuId: 2,
            quantity: 1,
            pricePerUnit: 200,
            mrpPerUnit: 250,
            name: 'Test Product 2'
        }
    ];

    return overrides.length > 0
        ? overrides.map((override, index) => ({ ...defaults[index] || defaults[0], ...override }))
        : defaults;
};

const createMockPercentageCapDiscount = (overrides: Partial<PercentageCapDiscount> = {}): PercentageCapDiscount => ({
    id: 'test-discount-123',
    name: 'Test Percentage Cap Discount',
    description: 'Test discount description',
    type: 'PERCENTAGE_CAP',
    isActive: true,
    validFrom: new Date('2025-01-01'),
    validTo: new Date('2025-12-31'),
    createdAt: new Date('2025-01-01T10:00:00Z'),
    updatedAt: new Date('2025-01-01T10:00:00Z'),
    usageCount: 0,
    percentage: 10,
    maxDiscountAmount: 50,
    minCartValue: 300,
    maxUsage: 1000,
    ...overrides
});

describe('PercentageCapDiscountCalculator', () => {
    let calculator: PercentageCapDiscountCalculator;

    beforeEach(() => {
        calculator = new PercentageCapDiscountCalculator();
    });

    describe('Calculator Properties', () => {
        test('should have correct type', () => {
            expect(calculator.type).toBe('PERCENTAGE_CAP');
        });
    });

    describe('canApply', () => {
        test('should return true when all conditions are met', () => {
            const cartItems = createMockCartItems(); // Total: 400
            const discount = createMockPercentageCapDiscount({
                percentage: 10,
                maxDiscountAmount: 50,
                minCartValue: 300,
                isActive: true,
                validFrom: new Date('2025-01-01'),
                validTo: new Date('2025-12-31')
            });

            const result = calculator.canApply(discount, cartItems);

            expect(result).toBe(true);
        });

        test('should return false when cart total is below minimum value', () => {
            const cartItems = createMockCartItems([
                { pricePerUnit: 50, quantity: 2 } // Total: 100
            ]);
            const discount = createMockPercentageCapDiscount({
                minCartValue: 300 // Cart total is below this
            });

            const result = calculator.canApply(discount, cartItems);

            expect(result).toBe(false);
        });

        test('should return false when discount is inactive', () => {
            const cartItems = createMockCartItems(); // Total: 400
            const discount = createMockPercentageCapDiscount({
                isActive: false,
                minCartValue: 300
            });

            const result = calculator.canApply(discount, cartItems);

            expect(result).toBe(false);
        });

        test('should return false when discount is expired', () => {
            const cartItems = createMockCartItems(); // Total: 400
            const discount = createMockPercentageCapDiscount({
                validFrom: new Date('2023-01-01'),
                validTo: new Date('2023-12-31'), // Expired
                minCartValue: 300
            });

            const result = calculator.canApply(discount, cartItems);

            expect(result).toBe(false);
        });

        test('should return false when usage limit is reached', () => {
            const cartItems = createMockCartItems(); // Total: 400
            const discount = createMockPercentageCapDiscount({
                usageCount: 100,
                maxUsage: 100, // Limit reached
                minCartValue: 300
            });

            const result = calculator.canApply(discount, cartItems);

            expect(result).toBe(false);
        });

        test('should return false for invalid discount type', () => {
            const cartItems = createMockCartItems();
            const invalidDiscount = {
                ...createMockPercentageCapDiscount(),
                type: 'INVALID_TYPE'
            } as any;

            const result = calculator.canApply(invalidDiscount, cartItems);

            expect(result).toBe(false);
        });

        test('should handle errors gracefully', () => {
            const cartItems = createMockCartItems();
            const discount = null as any; // This will cause an error

            const result = calculator.canApply(discount, cartItems);

            expect(result).toBe(false);
        });

        test('should return true when cart total equals minimum value', () => {
            const cartItems = createMockCartItems([
                { pricePerUnit: 150, quantity: 2 } // Total: 300
            ]);
            const discount = createMockPercentageCapDiscount({
                minCartValue: 300 // Exactly equal
            });

            const result = calculator.canApply(discount, cartItems);

            expect(result).toBe(true);
        });
    });

    describe('calculate', () => {
        test('should calculate correct discount amount', () => {
            const cartItems = createMockCartItems(); // Total: 400
            const discount = createMockPercentageCapDiscount({
                percentage: 10,
                maxDiscountAmount: 50,
                minCartValue: 300
            });

            const result = calculator.calculate(discount, cartItems);

            expect(result).toBe(40); // 10% of 400, under the cap of 50
        });

        test('should apply discount cap when percentage exceeds maximum', () => {
            const cartItems = createMockCartItems([
                { pricePerUnit: 500, quantity: 2 } // Total: 1000
            ]);
            const discount = createMockPercentageCapDiscount({
                percentage: 10,
                maxDiscountAmount: 50, // Cap at 50
                minCartValue: 300
            });

            const result = calculator.calculate(discount, cartItems);

            expect(result).toBe(50); // Capped at 50, not 100 (10% of 1000)
        });

        test('should return 0 when cart is below minimum value', () => {
            const cartItems = createMockCartItems([
                { pricePerUnit: 50, quantity: 2 } // Total: 100
            ]);
            const discount = createMockPercentageCapDiscount({
                percentage: 10,
                maxDiscountAmount: 50,
                minCartValue: 300 // Cart total is below this
            });

            const result = calculator.calculate(discount, cartItems);

            expect(result).toBe(0);
        });

        test('should return 0 for invalid discount type', () => {
            const cartItems = createMockCartItems();
            const invalidDiscount = {
                ...createMockPercentageCapDiscount(),
                type: 'INVALID_TYPE'
            } as any;

            const result = calculator.calculate(invalidDiscount, cartItems);

            expect(result).toBe(0);
        });

        test('should handle errors gracefully', () => {
            const cartItems = createMockCartItems();
            const discount = null as any; // This will cause an error

            const result = calculator.calculate(discount, cartItems);

            expect(result).toBe(0);
        });

        test('should round result to 2 decimal places', () => {
            const cartItems = createMockCartItems([
                { pricePerUnit: 333, quantity: 1 } // Total: 333
            ]);
            const discount = createMockPercentageCapDiscount({
                percentage: 10,
                maxDiscountAmount: 100,
                minCartValue: 300
            });

            const result = calculator.calculate(discount, cartItems);

            expect(result).toBe(33.3); // 10% of 333 = 33.3
        });

        test('should handle zero percentage', () => {
            const cartItems = createMockCartItems(); // Total: 400
            const discount = createMockPercentageCapDiscount({
                percentage: 0,
                maxDiscountAmount: 50,
                minCartValue: 300
            });

            const result = calculator.calculate(discount, cartItems);

            expect(result).toBe(0);
        });

        test('should handle very small percentages', () => {
            const cartItems = createMockCartItems([
                { pricePerUnit: 1000, quantity: 1 } // Total: 1000
            ]);
            const discount = createMockPercentageCapDiscount({
                percentage: 0.01, // 0.01%
                maxDiscountAmount: 50,
                minCartValue: 300
            });

            const result = calculator.calculate(discount, cartItems);

            expect(result).toBe(0.1); // 0.01% of 1000 = 0.1
        });
    });

    describe('validate', () => {
        test('should return no errors for valid discount', () => {
            const discount = createMockPercentageCapDiscount({
                percentage: 10,
                maxDiscountAmount: 50,
                minCartValue: 300,
                name: 'Valid Discount',
                validFrom: new Date('2024-01-01'),
                validTo: new Date('2024-12-31')
            });

            const errors = calculator.validate(discount);

            expect(errors).toHaveLength(0);
        });

        test('should return error for invalid discount type', () => {
            const invalidDiscount = {
                ...createMockPercentageCapDiscount(),
                type: 'INVALID_TYPE'
            } as any;

            const errors = calculator.validate(invalidDiscount);

            expect(errors).toContain('Invalid discount type: expected PERCENTAGE_CAP, got INVALID_TYPE');
        });

        test('should validate percentage field', () => {
            const discount = createMockPercentageCapDiscount({
                percentage: 0 // Invalid
            });

            const errors = calculator.validate(discount);

            expect(errors).toContain('Percentage must be greater than 0');
        });

        test('should validate percentage upper bound', () => {
            const discount = createMockPercentageCapDiscount({
                percentage: 150 // Invalid - over 100%
            });

            const errors = calculator.validate(discount);

            expect(errors).toContain('Percentage cannot exceed 100');
        });

        test('should validate percentage lower bound', () => {
            const discount = createMockPercentageCapDiscount({
                percentage: 0.001 // Invalid - below 0.01%
            });

            const errors = calculator.validate(discount);

            expect(errors).toContain('Percentage must be at least 0.01 (0.01%)');
        });

        test('should validate percentage type', () => {
            const discount = createMockPercentageCapDiscount({
                percentage: 'invalid' as any
            });

            const errors = calculator.validate(discount);

            expect(errors).toContain('Percentage must be a number');
        });

        test('should validate maximum discount amount', () => {
            const discount = createMockPercentageCapDiscount({
                maxDiscountAmount: 0 // Invalid
            });

            const errors = calculator.validate(discount);

            expect(errors).toContain('Maximum discount amount must be greater than 0');
        });

        test('should validate maximum discount amount upper bound', () => {
            const discount = createMockPercentageCapDiscount({
                maxDiscountAmount: 200000 // Invalid - too high
            });

            const errors = calculator.validate(discount);

            expect(errors).toContain('Maximum discount amount seems unreasonably high (> ₹1,00,000)');
        });

        test('should validate maximum discount amount type', () => {
            const discount = createMockPercentageCapDiscount({
                maxDiscountAmount: 'invalid' as any
            });

            const errors = calculator.validate(discount);

            expect(errors).toContain('Maximum discount amount must be a number');
        });

        test('should validate minimum cart value', () => {
            const discount = createMockPercentageCapDiscount({
                minCartValue: -100 // Invalid
            });

            const errors = calculator.validate(discount);

            expect(errors).toContain('Minimum cart value cannot be negative');
        });

        test('should validate minimum cart value upper bound', () => {
            const discount = createMockPercentageCapDiscount({
                minCartValue: 2000000 // Invalid - too high
            });

            const errors = calculator.validate(discount);

            expect(errors).toContain('Minimum cart value seems unreasonably high (> ₹10,00,000)');
        });

        test('should validate minimum cart value type', () => {
            const discount = createMockPercentageCapDiscount({
                minCartValue: 'invalid' as any
            });

            const errors = calculator.validate(discount);

            expect(errors).toContain('Minimum cart value must be a number');
        });

        test('should validate discount name', () => {
            const discount = createMockPercentageCapDiscount({
                name: '' // Invalid
            });

            const errors = calculator.validate(discount);

            expect(errors).toContain('Discount name is required');
        });

        test('should validate date range', () => {
            const discount = createMockPercentageCapDiscount({
                validFrom: new Date('2024-12-31'),
                validTo: new Date('2024-01-01') // Invalid - from is after to
            });

            const errors = calculator.validate(discount);

            expect(errors).toContain('Valid from date must be before valid to date');
        });

        test('should validate maximum usage', () => {
            const discount = createMockPercentageCapDiscount({
                maxUsage: 0 // Invalid
            });

            const errors = calculator.validate(discount);

            expect(errors).toContain('Maximum usage must be at least 1 if specified');
        });

        test('should validate cross-field relationships', () => {
            const discount = createMockPercentageCapDiscount({
                percentage: 10,
                maxDiscountAmount: 500, // Too high relative to min cart value
                minCartValue: 100
            });

            const errors = calculator.validate(discount);

            expect(errors).toContain('Maximum discount amount should not exceed minimum cart value');
        });

        test('should handle validation errors gracefully', () => {
            const discount = null as any; // This will cause an error

            const errors = calculator.validate(discount);

            expect(errors).toContain('Validation failed due to unexpected error');
        });

        test('should validate multiple errors at once', () => {
            const discount = createMockPercentageCapDiscount({
                percentage: 0, // Invalid
                maxDiscountAmount: -10, // Invalid
                minCartValue: -5, // Invalid
                name: '' // Invalid
            });

            const errors = calculator.validate(discount);

            expect(errors.length).toBeGreaterThan(3);
        });
    });

    describe('Helper Methods', () => {
        describe('getDescription', () => {
            test('should return correct description for valid discount', () => {
                const discount = createMockPercentageCapDiscount({
                    percentage: 15,
                    maxDiscountAmount: 100,
                    minCartValue: 500
                });

                const description = calculator.getDescription(discount);

                expect(description).toBe('15% off up to ₹100 on cart value above ₹500');
            });

            test('should return error message for invalid discount type', () => {
                const invalidDiscount = {
                    ...createMockPercentageCapDiscount(),
                    type: 'INVALID_TYPE'
                } as any;

                const description = calculator.getDescription(invalidDiscount);

                expect(description).toBe('Invalid percentage cap discount');
            });
        });

        describe('getMaxPossibleDiscount', () => {
            test('should return maximum discount amount', () => {
                const discount = createMockPercentageCapDiscount({
                    maxDiscountAmount: 75
                });

                const maxDiscount = calculator.getMaxPossibleDiscount(discount);

                expect(maxDiscount).toBe(75);
            });

            test('should return 0 for invalid discount type', () => {
                const invalidDiscount = {
                    ...createMockPercentageCapDiscount(),
                    type: 'INVALID_TYPE'
                } as any;

                const maxDiscount = calculator.getMaxPossibleDiscount(invalidDiscount);

                expect(maxDiscount).toBe(0);
            });
        });

        describe('getCartValueForMaxDiscount', () => {
            test('should calculate correct cart value for maximum discount', () => {
                const discount = createMockPercentageCapDiscount({
                    percentage: 10,
                    maxDiscountAmount: 50,
                    minCartValue: 300
                });

                const cartValue = calculator.getCartValueForMaxDiscount(discount);

                expect(cartValue).toBe(500); // 50 / 0.10 = 500
            });

            test('should return minimum cart value when it is higher', () => {
                const discount = createMockPercentageCapDiscount({
                    percentage: 50, // High percentage
                    maxDiscountAmount: 50,
                    minCartValue: 300
                });

                const cartValue = calculator.getCartValueForMaxDiscount(discount);

                expect(cartValue).toBe(300); // minCartValue is higher than calculated value (100)
            });

            test('should return 0 for invalid discount type', () => {
                const invalidDiscount = {
                    ...createMockPercentageCapDiscount(),
                    type: 'INVALID_TYPE'
                } as any;

                const cartValue = calculator.getCartValueForMaxDiscount(invalidDiscount);

                expect(cartValue).toBe(0);
            });
        });

        describe('wouldReceiveMaxDiscount', () => {
            test('should return true when cart qualifies for maximum discount', () => {
                const cartItems = createMockCartItems([
                    { pricePerUnit: 300, quantity: 2 } // Total: 600
                ]);
                const discount = createMockPercentageCapDiscount({
                    percentage: 10,
                    maxDiscountAmount: 50,
                    minCartValue: 300
                });

                const result = calculator.wouldReceiveMaxDiscount(discount, cartItems);

                expect(result).toBe(true); // 600 >= 500 (cart value for max discount)
            });

            test('should return false when cart does not qualify for maximum discount', () => {
                const cartItems = createMockCartItems([
                    { pricePerUnit: 200, quantity: 2 } // Total: 400
                ]);
                const discount = createMockPercentageCapDiscount({
                    percentage: 10,
                    maxDiscountAmount: 50,
                    minCartValue: 300
                });

                const result = calculator.wouldReceiveMaxDiscount(discount, cartItems);

                expect(result).toBe(false); // 400 < 500 (cart value for max discount)
            });

            test('should return false when discount cannot be applied', () => {
                const cartItems = createMockCartItems([
                    { pricePerUnit: 50, quantity: 2 } // Total: 100
                ]);
                const discount = createMockPercentageCapDiscount({
                    percentage: 10,
                    maxDiscountAmount: 50,
                    minCartValue: 300 // Cart total is below this
                });

                const result = calculator.wouldReceiveMaxDiscount(discount, cartItems);

                expect(result).toBe(false);
            });
        });
    });

    describe('Edge Cases', () => {
        test('should handle empty cart', () => {
            const cartItems: CartItemWithDetails[] = [];
            const discount = createMockPercentageCapDiscount({
                minCartValue: 0
            });

            const canApply = calculator.canApply(discount, cartItems);
            const calculate = calculator.calculate(discount, cartItems);

            expect(canApply).toBe(true); // Empty cart has total 0, which meets minCartValue 0
            expect(calculate).toBe(0); // 0% of 0 = 0
        });

        test('should handle very large cart values', () => {
            const cartItems = createMockCartItems([
                { pricePerUnit: 100000, quantity: 10 } // Total: 1,000,000
            ]);
            const discount = createMockPercentageCapDiscount({
                percentage: 1,
                maxDiscountAmount: 5000,
                minCartValue: 50000
            });

            const result = calculator.calculate(discount, cartItems);

            expect(result).toBe(5000); // Capped at maxDiscountAmount
        });

        test('should handle very small cart values', () => {
            const cartItems = createMockCartItems([
                { pricePerUnit: 0.01, quantity: 1 } // Total: 0.01
            ]);
            const discount = createMockPercentageCapDiscount({
                percentage: 10,
                maxDiscountAmount: 1,
                minCartValue: 0
            });

            const result = calculator.calculate(discount, cartItems);

            expect(result).toBe(0); // 10% of 0.01 = 0.001, rounded to 0
        });

        test('should handle 100% discount', () => {
            const cartItems = createMockCartItems([
                { pricePerUnit: 100, quantity: 1 } // Total: 100
            ]);
            const discount = createMockPercentageCapDiscount({
                percentage: 100,
                maxDiscountAmount: 200, // Higher than cart total
                minCartValue: 50
            });

            const result = calculator.calculate(discount, cartItems);

            expect(result).toBe(100); // 100% of 100 = 100
        });
    });
});
/**
 * Zod validation schemas for discount system
 * 
 * This file contains comprehensive Zod schemas for runtime type validation
 * and seamless tRPC integration throughout the discount system.
 */

import { z } from 'zod';
import { 
  CommonFieldSchemas, 
  ValidationPatterns
} from './discount.validation.helpers';

// Base discount schema with common fields (without refinements for extensibility)
const BaseDiscountSchemaCore = z.object({
    name: CommonFieldSchemas.name(),
    description: CommonFieldSchemas.description(),
    isActive: z.boolean().default(true),
    validFrom: CommonFieldSchemas.date(),
    validTo: CommonFieldSchemas.date(),
    maxUsage: CommonFieldSchemas.maxUsage()
});

// Base discount schema with date validation
export const BaseDiscountSchema = ValidationPatterns.withDateRangeValidation(BaseDiscountSchemaCore);

// Percentage cap discount schema (built from core schema to avoid extend() issues)
const PercentageCapDiscountSchemaCore = BaseDiscountSchemaCore.extend({
    type: z.literal('PERCENTAGE_CAP'),
    percentage: CommonFieldSchemas.percentage(),
    maxDiscountAmount: CommonFieldSchemas.positiveNumber('Maximum discount amount'),
    minCartValue: CommonFieldSchemas.positiveNumber('Minimum cart value')
});

export const PercentageCapDiscountSchema = ValidationPatterns.withPercentageCapValidation(
    ValidationPatterns.withDateRangeValidation(PercentageCapDiscountSchemaCore)
);

// Create discount schema (currently only supports PERCENTAGE_CAP, will be union when more types are added)
export const CreateDiscountSchema = PercentageCapDiscountSchema;

// Update schema (manual partial to avoid issues with discriminated union)
export const UpdateDiscountSchema = z.object({
    id: CommonFieldSchemas.uuid('discount ID'),
    name: CommonFieldSchemas.name().optional(),
    description: CommonFieldSchemas.description(),
    type: z.literal('PERCENTAGE_CAP').optional(),
    isActive: z.boolean().optional(),
    validFrom: CommonFieldSchemas.date().optional(),
    validTo: CommonFieldSchemas.date().optional(),
    maxUsage: CommonFieldSchemas.maxUsage(),
    percentage: CommonFieldSchemas.percentage().optional(),
    maxDiscountAmount: CommonFieldSchemas.positiveNumber('Maximum discount amount').optional(),
    minCartValue: CommonFieldSchemas.positiveNumber('Minimum cart value').optional()
}).refine(
    (data: {
        validFrom?: Date;
        validTo?: Date;
        maxDiscountAmount?: number;
        percentage?: number;
    }) => {
        // If both dates are provided, validate the range
        if (data.validFrom && data.validTo) {
            return data.validFrom < data.validTo;
        }
        return true;
    },
    {
        message: 'Valid from date must be before valid to date',
        path: ['validTo']
    }
).refine(
    (data: {
        maxDiscountAmount?: number;
        percentage?: number;
    }) => {
        // If both percentage and max discount are provided, validate business rule
        if (data.percentage !== undefined && data.maxDiscountAmount !== undefined) {
            return !(data.maxDiscountAmount === 0 && data.percentage > 0);
        }
        return true;
    },
    {
        message: 'Maximum discount amount cannot be zero when percentage is greater than zero',
        path: ['maxDiscountAmount']
    }
);

// Cart item schema for discount calculations
export const CartItemSchema = z.object({
    skuId: CommonFieldSchemas.positiveInteger('SKU ID'),
    variantSkuId: CommonFieldSchemas.positiveInteger('Variant SKU ID').optional(),
    quantity: z.number()
        .int('Quantity must be an integer')
        .min(1, 'Quantity must be at least 1'),
    pricePerUnit: CommonFieldSchemas.positiveNumber('Price per unit'),
    mrpPerUnit: CommonFieldSchemas.positiveNumber('MRP per unit'),
    name: z.string().optional(),
    categoryId: CommonFieldSchemas.positiveInteger('Category ID').optional()
});

// Cart calculation schema
import { getValidationConfig } from './discount.config';
const { maxCartItems } = getValidationConfig();
export const CalculateDiscountSchema = z.object({
    cartItems: z.array(CartItemSchema)
        .min(1, 'Cart must contain at least one item')
        .max(maxCartItems, `Cart cannot contain more than ${maxCartItems} items`)
});

// Query schemas
export const GetDiscountByIdSchema = z.object({
    id: CommonFieldSchemas.uuid('discount ID')
});

export const DeleteDiscountSchema = z.object({
    id: CommonFieldSchemas.uuid('discount ID')
});

// List discounts schema with pagination and filtering
const { maxDiscountsPerList } = getValidationConfig();
export const ListDiscountsSchema = z.object({
    page: z.number()
        .int('Page must be an integer')
        .min(1, 'Page must be at least 1')
        .default(1),
    limit: z.number()
        .int('Limit must be an integer')
        .min(1, 'Limit must be at least 1')
        .max(maxDiscountsPerList, `Limit cannot exceed ${maxDiscountsPerList}`)
        .default(20),
    filters: z.object({
        isActive: z.boolean().optional(),
        type: z.enum(['PERCENTAGE_CAP']).optional(),
        validAt: CommonFieldSchemas.date().optional(),
        search: CommonFieldSchemas.searchTerm()
    }).optional()
});

// Usage statistics schema
export const GetUsageStatsSchema = z.object({
    discountId: CommonFieldSchemas.uuid('discount ID').optional(),
    dateFrom: CommonFieldSchemas.date().optional(),
    dateTo: CommonFieldSchemas.date().optional()
}).refine(
    (data: {
        dateFrom?: Date;
        dateTo?: Date;
    }) => {
        // If both dates are provided, validate the range
        if (data.dateFrom && data.dateTo) {
            return data.dateFrom <= data.dateTo;
        }
        return true;
    },
    {
        message: 'Date from must be before or equal to date to',
        path: ['dateTo']
    }
);

// Response schemas for type inference
export const DiscountResponseSchema = z.object({
    id: z.string().uuid(),
    name: z.string(),
    description: z.string().optional(),
    type: z.enum(['PERCENTAGE_CAP']),
    isActive: z.boolean(),
    validFrom: z.date(),
    validTo: z.date(),
    createdAt: z.date(),
    updatedAt: z.date(),
    usageCount: z.number().int().min(0),
    maxUsage: z.number().int().min(1).optional(),
    // Type-specific fields (will be present based on discriminated union)
    percentage: z.number().min(0.01).max(100).optional(),
    maxDiscountAmount: z.number().min(0).optional(),
    minCartValue: z.number().min(0).optional()
});

export const AppliedDiscountSchema = z.object({
    discountId: z.string().uuid(),
    discountName: z.string(),
    discountAmount: z.number().min(0),
    discountType: z.enum(['PERCENTAGE_CAP'])
});

export const DiscountCalculationResultSchema = z.object({
    totalDiscount: z.number().min(0),
    appliedDiscounts: z.array(AppliedDiscountSchema),
    originalTotal: z.number().min(0),
    finalTotal: z.number().min(0),
    savings: z.number().min(0)
}).refine(
    (data: {
        totalDiscount: number;
        originalTotal: number;
        finalTotal: number;
    }) => data.finalTotal === data.originalTotal - data.totalDiscount,
    {
        message: 'Final total must equal original total minus total discount',
        path: ['finalTotal']
    }
).refine(
    (data: {
        totalDiscount: number;
        savings: number;
    }) => data.savings === data.totalDiscount,
    {
        message: 'Savings must equal total discount',
        path: ['savings']
    }
);

export const PaginatedDiscountsResponseSchema = z.object({
    items: z.array(DiscountResponseSchema),
    total: z.number().int().min(0),
    page: z.number().int().min(1),
    limit: z.number().int().min(1),
    totalPages: z.number().int().min(0)
});

export const UsageStatsResponseSchema = z.object({
    discountId: z.string().uuid().optional(),
    totalApplications: z.number().int().min(0),
    totalSavings: z.number().min(0),
    averageDiscountAmount: z.number().min(0),
    dateRange: z.object({
        from: z.date(),
        to: z.date()
    }),
    dailyStats: z.array(z.object({
        date: z.date(),
        applications: z.number().int().min(0),
        savings: z.number().min(0)
    })).optional()
});

// Type inference exports for use throughout the application
export type CreateDiscountInput = z.infer<typeof CreateDiscountSchema>;
export type UpdateDiscountInput = z.infer<typeof UpdateDiscountSchema>;
export type CalculateDiscountInput = z.infer<typeof CalculateDiscountSchema>;
export type GetDiscountByIdInput = z.infer<typeof GetDiscountByIdSchema>;
export type ListDiscountsInput = z.infer<typeof ListDiscountsSchema>;
export type GetUsageStatsInput = z.infer<typeof GetUsageStatsSchema>;

export type DiscountResponse = z.infer<typeof DiscountResponseSchema>;
export type AppliedDiscount = z.infer<typeof AppliedDiscountSchema>;
export type DiscountCalculationResult = z.infer<typeof DiscountCalculationResultSchema>;
export type PaginatedDiscountsResponse = z.infer<typeof PaginatedDiscountsResponseSchema>;
export type UsageStatsResponse = z.infer<typeof UsageStatsResponseSchema>;
export type CartItem = z.infer<typeof CartItemSchema>;

// Validation helper functions
export const ValidationHelpers = {
    /**
     * Validate and parse discount creation input
     */
    parseCreateDiscountInput: (input: unknown) => {
        return CreateDiscountSchema.parse(input);
    },

    /**
     * Validate and parse discount update input
     */
    parseUpdateDiscountInput: (input: unknown) => {
        return UpdateDiscountSchema.parse(input);
    },

    /**
     * Validate and parse cart items for discount calculation
     */
    parseCalculateDiscountInput: (input: unknown) => {
        return CalculateDiscountSchema.parse(input);
    },

    /**
     * Safe parse with error handling
     */
    safeParseCreateDiscount: (input: unknown) => {
        return CreateDiscountSchema.safeParse(input);
    },

    /**
     * Get validation error messages in a user-friendly format
     */
    getValidationErrors: (error: z.ZodError) => {
        return error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message
        }));
    }
};
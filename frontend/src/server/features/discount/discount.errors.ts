/**
 * Consolidated Discount Error Classes
 * 
 * This file contains all discount-specific error classes, consolidating duplicates
 * while maintaining feature encapsulation within the discount domain.
 */

import type { DiscountType } from './discount.types';

// Base discount error class (domain-specific)
export class DiscountError extends Error {
  constructor(message: string, public code: string) {
    super(message);
    this.name = 'DiscountError';
  }
}

// Core discount error types with enhanced context support
export class DiscountNotFoundError extends DiscountError {
  constructor(id: string) {
    super(`Discount with id ${id} not found`, 'DISCOUNT_NOT_FOUND');
    this.name = 'DiscountNotFoundError';
  }
}

export class DiscountCalculationError extends DiscountError {
  constructor(
    message: string,
    public readonly context?: {
      discountType?: DiscountType;
      cartTotal?: number;
    }
  ) {
    super(message, 'CALCULATION_ERROR');
    this.name = 'DiscountCalculationError';
  }
}

export class DiscountValidationError extends DiscountError {
  constructor(
    message: string,
    public readonly context?: {
      discountType?: DiscountType;
      validationErrors?: string[];
    }
  ) {
    super(message, 'VALIDATION_ERROR');
    this.name = 'DiscountValidationError';
  }
}

export class DiscountStorageError extends DiscountError {
  constructor(message: string) {
    super(message, 'STORAGE_ERROR');
    this.name = 'DiscountStorageError';
  }
}

export class DiscountUsageLimitError extends DiscountError {
  constructor(discountName: string) {
    super(`Discount "${discountName}" has reached its usage limit`, 'USAGE_LIMIT_EXCEEDED');
    this.name = 'DiscountUsageLimitError';
  }
}

export class DiscountExpiredError extends DiscountError {
  constructor(discountName: string) {
    super(`Discount "${discountName}" has expired`, 'DISCOUNT_EXPIRED');
    this.name = 'DiscountExpiredError';
  }
}

// Service-specific error types
export class DiscountServiceError extends DiscountError {
  constructor(message: string, code: string) {
    super(message, code);
    this.name = 'DiscountServiceError';
  }
}

export class DiscountOperationError extends DiscountServiceError {
  constructor(operation: string, message: string) {
    super(`Failed to ${operation}: ${message}`, 'OPERATION_ERROR');
    this.name = 'DiscountOperationError';
  }
}

export class DiscountEligibilityError extends DiscountServiceError {
  constructor(discountId: string, reason: string) {
    super(`Discount ${discountId} is not eligible: ${reason}`, 'ELIGIBILITY_ERROR');
    this.name = 'DiscountEligibilityError';
  }
}

export class ServiceNotInitializedError extends DiscountServiceError {
  constructor() {
    super('Service not initialized. Call initialize() first.', 'SERVICE_NOT_INITIALIZED');
    this.name = 'ServiceNotInitializedError';
  }
}

export class ServiceConfigurationError extends DiscountServiceError {
  constructor(message: string) {
    super(message, 'CONFIGURATION_ERROR');
    this.name = 'ServiceConfigurationError';
  }
}

// Engine-specific error types
export class DiscountEngineError extends DiscountError {
  constructor(
    message: string,
    code: string,
    public readonly discountType?: DiscountType
  ) {
    super(message, code);
    this.name = 'DiscountEngineError';
  }
}

export class CalculatorRegistrationError extends DiscountEngineError {
  constructor(type: DiscountType, message: string) {
    super(`Calculator registration failed for type ${type}: ${message}`, 'CALCULATOR_REGISTRATION_ERROR', type);
    this.name = 'CalculatorRegistrationError';
  }
}

// Error type guard functions
export function isDiscountError(error: unknown): error is DiscountError {
  return error instanceof DiscountError;
}

export function isDiscountNotFoundError(error: unknown): error is DiscountNotFoundError {
  return error instanceof DiscountNotFoundError;
}

export function isDiscountCalculationError(error: unknown): error is DiscountCalculationError {
  return error instanceof DiscountCalculationError;
}

export function isDiscountValidationError(error: unknown): error is DiscountValidationError {
  return error instanceof DiscountValidationError;
}

// Factory functions for specific use cases
export const createServiceCalculationError = (message: string, cartTotal?: number): DiscountCalculationError => {
  return new DiscountCalculationError(message, { cartTotal });
};

export const createServiceValidationError = (message: string, validationErrors: string[]): DiscountValidationError => {
  return new DiscountValidationError(message, { validationErrors });
};

export const createEngineCalculationError = (type: DiscountType, message: string): DiscountCalculationError => {
  return new DiscountCalculationError(message, { discountType: type });
};

export const createEngineValidationError = (type: DiscountType, message: string): DiscountValidationError => {
  return new DiscountValidationError(message, { discountType: type });
};

/**
 * Centralized configuration for the discount system
 * 
 * This file provides a centralized configuration system that loads from
 * environment variables and provides defaults for all discount system components.
 */

/**
 * Validation configuration
 */
export interface ValidationConfig {
  maxNameLength: number;
  maxDescriptionLength: number;
  maxPercentage: number;
  maxCartItems: number;
  maxDiscountsPerList: number;
  maxUsageLimit: number;
  maxSearchTermLength: number;
}

/**
 * Storage configuration
 */
export interface StorageConfig {
  maxDiscounts: number;
  maxUsageEntries: number;
  healthCheckThresholds: {
    degradedRatio: number;
    unhealthyRatio: number;
  };
}

/**
 * Engine configuration
 */
export interface EngineConfig {
  maxDiscountsPerCart: number;
  allowStacking: boolean;
  calculationTimeoutMs: number;
  enableDebugLogging: boolean;
  businessValidationLimits: {
    maxDiscountAmount: number;
    maxCartValue: number;
  };
}

/**
 * Repository configuration
 */
export interface RepositoryConfig {
  enableCaching: boolean;
  cacheTimeoutMs: number;
  enableMetrics: boolean;
  healthCheckThresholds: {
    minSuccessRate: number;
    degradedSuccessRate: number;
  };
}

/**
 * Service configuration
 */
export interface ServiceConfig {
  enableDetailedLogging: boolean;
  maxDiscountsPerCart: number;
  enableUsageTracking: boolean;
  activeDiscountsCacheTtl: number;
}

/**
 * Complete discount system configuration
 */
export interface DiscountSystemConfig {
  validation: ValidationConfig;
  storage: StorageConfig;
  engine: EngineConfig;
  repository: RepositoryConfig;
  service: ServiceConfig;
}

/**
 * Default configuration values
 */
const DEFAULT_CONFIG: DiscountSystemConfig = {
  validation: {
    maxNameLength: 100,
    maxDescriptionLength: 500,
    maxPercentage: 100,
    maxCartItems: 100,
    maxDiscountsPerList: 100,
    maxUsageLimit: 1000000,
    maxSearchTermLength: 100
  },
  storage: {
    maxDiscounts: 10000,
    maxUsageEntries: 100000,
    healthCheckThresholds: {
      degradedRatio: 0.9,
      unhealthyRatio: 1.0
    }
  },
  engine: {
    maxDiscountsPerCart: 10,
    allowStacking: false,
    calculationTimeoutMs: 5000,
    enableDebugLogging: false,
    businessValidationLimits: {
      maxDiscountAmount: 100000, // ₹100,000
      maxCartValue: 1000000 // ₹10,00,000
    }
  },
  repository: {
    enableCaching: false,
    cacheTimeoutMs: 300000, // 5 minutes
    enableMetrics: true,
    healthCheckThresholds: {
      minSuccessRate: 0.95,
      degradedSuccessRate: 0.8
    }
  },
  service: {
    enableDetailedLogging: false,
    maxDiscountsPerCart: 5,
    enableUsageTracking: true,
    activeDiscountsCacheTtl: 300000 // 5 minutes
  }
};

/**
 * Environment variable mappings
 */
const ENV_MAPPINGS = {
  // Validation
  'DISCOUNT_MAX_NAME_LENGTH': 'validation.maxNameLength',
  'DISCOUNT_MAX_DESCRIPTION_LENGTH': 'validation.maxDescriptionLength',
  'DISCOUNT_MAX_PERCENTAGE': 'validation.maxPercentage',
  'DISCOUNT_MAX_CART_ITEMS': 'validation.maxCartItems',
  'DISCOUNT_MAX_DISCOUNTS_PER_LIST': 'validation.maxDiscountsPerList',
  'DISCOUNT_MAX_USAGE_LIMIT': 'validation.maxUsageLimit',
  'DISCOUNT_MAX_SEARCH_TERM_LENGTH': 'validation.maxSearchTermLength',
  
  // Storage
  'DISCOUNT_STORAGE_MAX_DISCOUNTS': 'storage.maxDiscounts',
  'DISCOUNT_STORAGE_MAX_USAGE_ENTRIES': 'storage.maxUsageEntries',
  'DISCOUNT_STORAGE_DEGRADED_RATIO': 'storage.healthCheckThresholds.degradedRatio',
  'DISCOUNT_STORAGE_UNHEALTHY_RATIO': 'storage.healthCheckThresholds.unhealthyRatio',
  
  // Engine
  'DISCOUNT_ENGINE_MAX_DISCOUNTS_PER_CART': 'engine.maxDiscountsPerCart',
  'DISCOUNT_ENGINE_ALLOW_STACKING': 'engine.allowStacking',
  'DISCOUNT_ENGINE_CALCULATION_TIMEOUT_MS': 'engine.calculationTimeoutMs',
  'DISCOUNT_ENGINE_ENABLE_DEBUG_LOGGING': 'engine.enableDebugLogging',
  'DISCOUNT_ENGINE_MAX_DISCOUNT_AMOUNT': 'engine.businessValidationLimits.maxDiscountAmount',
  'DISCOUNT_ENGINE_MAX_CART_VALUE': 'engine.businessValidationLimits.maxCartValue',
  
  // Repository
  'DISCOUNT_REPOSITORY_ENABLE_CACHING': 'repository.enableCaching',
  'DISCOUNT_REPOSITORY_CACHE_TIMEOUT_MS': 'repository.cacheTimeoutMs',
  'DISCOUNT_REPOSITORY_ENABLE_METRICS': 'repository.enableMetrics',
  'DISCOUNT_REPOSITORY_MIN_SUCCESS_RATE': 'repository.healthCheckThresholds.minSuccessRate',
  'DISCOUNT_REPOSITORY_DEGRADED_SUCCESS_RATE': 'repository.healthCheckThresholds.degradedSuccessRate',
  
  // Service
  'DISCOUNT_SERVICE_ENABLE_DETAILED_LOGGING': 'service.enableDetailedLogging',
  'DISCOUNT_SERVICE_MAX_DISCOUNTS_PER_CART': 'service.maxDiscountsPerCart',
  'DISCOUNT_SERVICE_ENABLE_USAGE_TRACKING': 'service.enableUsageTracking',
  'DISCOUNT_SERVICE_ACTIVE_DISCOUNTS_CACHE_TTL': 'service.activeDiscountsCacheTtl'
};

/**
 * Configuration manager class
 */
class DiscountConfigManager {
  private config: DiscountSystemConfig;
  private initialized = false;

  constructor() {
    this.config = this.loadConfiguration();
  }

  /**
   * Load configuration from environment variables and defaults
   */
  private loadConfiguration(): DiscountSystemConfig {
    const config = JSON.parse(JSON.stringify(DEFAULT_CONFIG)) as DiscountSystemConfig;

    // Apply environment variable overrides
    for (const [envVar, configPath] of Object.entries(ENV_MAPPINGS)) {
      const envValue = process.env[envVar];
      if (envValue !== undefined) {
        this.setNestedValue(config as unknown as Record<string, unknown>, configPath, this.parseEnvValue(envValue));
      }
    }

    return config;
  }

  /**
   * Parse environment variable value to appropriate type
   */
  private parseEnvValue(value: string): string | number | boolean {
    // Boolean values
    if (value.toLowerCase() === 'true') return true;
    if (value.toLowerCase() === 'false') return false;
    
    // Numeric values
    const numValue = Number(value);
    if (!isNaN(numValue)) return numValue;
    
    // String values
    return value;
  }

  /**
   * Set nested object value using dot notation path
   */
  private setNestedValue(obj: Record<string, unknown>, path: string, value: string | number | boolean): void {
    const keys = path.split('.');
    let current: Record<string, unknown> = obj;
    
    for (let i = 0; i < keys.length - 1; i++) {
      const key = keys[i];
      if (!(key in current)) {
        current[key] = {};
      }
      current = current[key] as Record<string, unknown>;
    }
    
    current[keys[keys.length - 1]] = value;
  }

  /**
   * Get the complete configuration
   */
  getConfig(): DiscountSystemConfig {
    return JSON.parse(JSON.stringify(this.config));
  }

  /**
   * Get validation configuration
   */
  getValidationConfig(): ValidationConfig {
    return { ...this.config.validation };
  }

  /**
   * Get storage configuration
   */
  getStorageConfig(): StorageConfig {
    return { ...this.config.storage };
  }

  /**
   * Get engine configuration
   */
  getEngineConfig(): EngineConfig {
    return { ...this.config.engine };
  }

  /**
   * Get repository configuration
   */
  getRepositoryConfig(): RepositoryConfig {
    return { ...this.config.repository };
  }

  /**
   * Get service configuration
   */
  getServiceConfig(): ServiceConfig {
    return { ...this.config.service };
  }

  /**
   * Update configuration (useful for testing)
   */
  updateConfig(updates: Partial<DiscountSystemConfig>): void {
    this.config = {
      ...this.config,
      ...updates,
      validation: { ...this.config.validation, ...updates.validation },
      storage: { ...this.config.storage, ...updates.storage },
      engine: { ...this.config.engine, ...updates.engine },
      repository: { ...this.config.repository, ...updates.repository },
      service: { ...this.config.service, ...updates.service }
    };
  }

  /**
   * Validate configuration values
   */
  validateConfig(): string[] {
    const errors: string[] = [];
    const config = this.config;

    // Validation config checks
    if (config.validation.maxNameLength <= 0) {
      errors.push('validation.maxNameLength must be positive');
    }
    if (config.validation.maxPercentage <= 0 || config.validation.maxPercentage > 100) {
      errors.push('validation.maxPercentage must be between 0 and 100');
    }

    // Storage config checks
    if (config.storage.maxDiscounts <= 0) {
      errors.push('storage.maxDiscounts must be positive');
    }
    if (config.storage.healthCheckThresholds.degradedRatio < 0 || config.storage.healthCheckThresholds.degradedRatio > 1) {
      errors.push('storage.healthCheckThresholds.degradedRatio must be between 0 and 1');
    }

    // Engine config checks
    if (config.engine.maxDiscountsPerCart <= 0) {
      errors.push('engine.maxDiscountsPerCart must be positive');
    }
    if (config.engine.calculationTimeoutMs <= 0) {
      errors.push('engine.calculationTimeoutMs must be positive');
    }

    // Repository config checks
    if (config.repository.cacheTimeoutMs <= 0) {
      errors.push('repository.cacheTimeoutMs must be positive');
    }
    if (config.repository.healthCheckThresholds.minSuccessRate < 0 || config.repository.healthCheckThresholds.minSuccessRate > 1) {
      errors.push('repository.healthCheckThresholds.minSuccessRate must be between 0 and 1');
    }

    // Service config checks
    if (config.service.maxDiscountsPerCart <= 0) {
      errors.push('service.maxDiscountsPerCart must be positive');
    }
    if (config.service.activeDiscountsCacheTtl <= 0) {
      errors.push('service.activeDiscountsCacheTtl must be positive');
    }

    return errors;
  }

  /**
   * Initialize and validate configuration
   */
  initialize(): void {
    if (this.initialized) {
      return;
    }

    const errors = this.validateConfig();
    if (errors.length > 0) {
      throw new Error(`Invalid discount system configuration: ${errors.join(', ')}`);
    }

    this.initialized = true;
  }

  /**
   * Check if configuration is initialized
   */
  isInitialized(): boolean {
    return this.initialized;
  }
}

/**
 * Singleton configuration manager instance
 */
let configManagerInstance: DiscountConfigManager | null = null;

/**
 * Get the configuration manager instance
 */
export function getDiscountConfig(): DiscountConfigManager {
  if (!configManagerInstance) {
    configManagerInstance = new DiscountConfigManager();
    configManagerInstance.initialize();
  }
  return configManagerInstance;
}

/**
 * Reset configuration manager (useful for testing)
 */
export function resetDiscountConfig(): void {
  configManagerInstance = null;
}

/**
 * Convenience functions for getting specific configurations
 */
export const getValidationConfig = (): ValidationConfig => getDiscountConfig().getValidationConfig();
export const getStorageConfig = (): StorageConfig => getDiscountConfig().getStorageConfig();
export const getEngineConfig = (): EngineConfig => getDiscountConfig().getEngineConfig();
export const getRepositoryConfig = (): RepositoryConfig => getDiscountConfig().getRepositoryConfig();
export const getServiceConfig = (): ServiceConfig => getDiscountConfig().getServiceConfig();

// Types are already exported as interfaces above
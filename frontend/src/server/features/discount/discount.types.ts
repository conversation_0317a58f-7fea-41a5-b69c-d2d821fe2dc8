/**
 * Core discount system types and interfaces
 * 
 * This file defines the fundamental types used throughout the discount system,
 * providing a foundation for type-safe discount operations.
 */

// Supported discount types (extensible)
export type DiscountType = 'PERCENTAGE_CAP';

// Base discount interface - common properties for all discount types
export interface BaseDiscount {
  id: string;
  name: string;
  description?: string;
  type: DiscountType;
  isActive: boolean;
  validFrom: Date;
  validTo: Date;
  createdAt: Date;
  updatedAt: Date;
  usageCount: number;
  maxUsage?: number;
}

// Specific discount type for initial implementation
export interface PercentageCapDiscount extends BaseDiscount {
  type: 'PERCENTAGE_CAP';
  percentage: number; // X% off
  maxDiscountAmount: number; // up to Y
  minCartValue: number; // on cart value above Z
}

// Union type for all discount types (extensible)
export type Discount = PercentageCapDiscount;

// Discount calculation result
export interface DiscountCalculationResult {
  totalDiscount: number;
  appliedDiscounts: AppliedDiscount[];
  originalTotal: number;
  finalTotal: number;
  savings: number;
}

export interface AppliedDiscount {
  discountId: string;
  discountName: string;
  discountAmount: number;
  discountType: DiscountType;
}

// Server-side cart interfaces (compatible with frontend types)
export interface ServerCartItem {
  skuId: number;
  variantSkuId?: number;
  quantity: number;
  pricePerUnit: number;
  mrpPerUnit: number;
}

// Extended cart item for discount calculations
export interface CartItemWithDetails extends ServerCartItem {
  name?: string; // Optional for logging/debugging
  categoryId?: number; // For future category-based discounts
}

// Discount filters for queries
export interface DiscountFilters {
  isActive?: boolean;
  type?: DiscountType;
  validAt?: Date;
}

// Usage tracking
export interface DiscountUsageEntry {
  discountId: string;
  cartId: string;
  discountAmount: number;
  cartTotal: number;
  appliedAt: Date;
}

// Usage statistics
export interface DiscountUsageStats {
  discountId?: string;
  totalApplications: number;
  totalSavings: number;
  averageDiscountAmount: number;
  averageCartValue: number;
  usageByDate: Array<{
    date: string;
    applications: number;
    savings: number;
  }>;
  topCartIds?: Array<{
    cartId: string;
    applications: number;
    totalSavings: number;
  }>;
}



// Input types for creating/updating discounts (basic versions - Zod versions in validation.ts)
export type CreateDiscountData = Omit<Discount, 'id' | 'createdAt' | 'updatedAt' | 'usageCount'>;
export type UpdateDiscountData = Partial<CreateDiscountData> & { id: string };

// Discount status enumeration
export enum DiscountStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  EXPIRED = 'EXPIRED',
  USAGE_LIMIT_REACHED = 'USAGE_LIMIT_REACHED'
}

// Helper type for discount creation without computed fields
export type DiscountCreateData = {
  name: string;
  description?: string;
  type: DiscountType;
  isActive: boolean;
  validFrom: Date;
  validTo: Date;
  maxUsage?: number;
} & (
  | {
      type: 'PERCENTAGE_CAP';
      percentage: number;
      maxDiscountAmount: number;
      minCartValue: number;
    }
);

// Utility functions for type validation and manipulation
export const DiscountTypeUtils = {
  /**
   * Check if a discount is currently valid based on date range
   */
  isValidByDate(discount: Discount, checkDate: Date = new Date()): boolean {
    return checkDate >= discount.validFrom && checkDate <= discount.validTo;
  },

  /**
   * Check if a discount has reached its usage limit
   */
  hasReachedUsageLimit(discount: Discount): boolean {
    return discount.maxUsage !== undefined && discount.usageCount >= discount.maxUsage;
  },

  /**
   * Get the current status of a discount
   */
  getDiscountStatus(discount: Discount, checkDate: Date = new Date()): DiscountStatus {
    if (!discount.isActive) {
      return DiscountStatus.INACTIVE;
    }
    
    if (!this.isValidByDate(discount, checkDate)) {
      return DiscountStatus.EXPIRED;
    }
    
    if (this.hasReachedUsageLimit(discount)) {
      return DiscountStatus.USAGE_LIMIT_REACHED;
    }
    
    return DiscountStatus.ACTIVE;
  },

  /**
   * Check if a discount can be applied (active, valid date, under usage limit)
   */
  canBeApplied(discount: Discount, checkDate: Date = new Date()): boolean {
    return this.getDiscountStatus(discount, checkDate) === DiscountStatus.ACTIVE;
  },

  /**
   * Calculate cart total from cart items
   */
  calculateCartTotal(cartItems: CartItemWithDetails[]): number {
    return cartItems.reduce((total, item) => {
      return total + (item.pricePerUnit * item.quantity);
    }, 0);
  },

  /**
   * Type guard to check if discount is PercentageCapDiscount
   */
  isPercentageCapDiscount(discount: Discount): discount is PercentageCapDiscount {
    return discount.type === 'PERCENTAGE_CAP';
  }
};
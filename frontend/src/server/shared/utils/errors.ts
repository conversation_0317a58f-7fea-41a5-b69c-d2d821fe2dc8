/**
 * Shared error classes for cross-cutting concerns
 *
 * This file contains only generic, reusable error classes that can be used
 * across multiple features and domains. Feature-specific errors should be
 * defined within their respective feature directories.
 */

// Generic base error class for structured error handling
export class BaseError extends Error {
  constructor(message: string, public code: string) {
    super(message);
    this.name = 'BaseError';
  }
}

// Generic validation error for cross-cutting validation concerns
export class ValidationError extends BaseError {
  constructor(message: string, public validationErrors?: string[]) {
    super(message, 'VALIDATION_ERROR');
    this.name = 'ValidationError';
  }
}

// Generic configuration error for system-wide configuration issues
export class ConfigurationError extends BaseError {
  constructor(message: string) {
    super(message, 'CONFIGURATION_ERROR');
    this.name = 'ConfigurationError';
  }
}

// Generic not found error for resource lookup failures
export class NotFoundError extends BaseError {
  constructor(resource: string, identifier: string) {
    super(`${resource} with identifier ${identifier} not found`, 'NOT_FOUND');
    this.name = 'NotFoundError';
  }
}

// Generic error type guard functions
export function isBaseError(error: unknown): error is BaseError {
  return error instanceof BaseError;
}

export function isValidationError(error: unknown): error is ValidationError {
  return error instanceof ValidationError;
}

export function isConfigurationError(error: unknown): error is ConfigurationError {
  return error instanceof ConfigurationError;
}

export function isNotFoundError(error: unknown): error is NotFoundError {
  return error instanceof NotFoundError;
}
/**
 * Custom error classes for the discount system
 * 
 * These error classes provide structured error handling throughout the system,
 * enabling proper error categorization and handling.
 */

// Base discount error class
export class DiscountError extends Error {
  constructor(message: string, public code: string) {
    super(message);
    this.name = 'DiscountError';
  }
}

// Specific error types
export class DiscountNotFoundError extends DiscountError {
  constructor(id: string) {
    super(`Discount with id ${id} not found`, 'DISCOUNT_NOT_FOUND');
  }
}

export class DiscountCalculationError extends DiscountError {
  constructor(message: string) {
    super(message, 'CALCULATION_ERROR');
  }
}

export class DiscountValidationError extends DiscountError {
  constructor(message: string) {
    super(message, 'VALIDATION_ERROR');
  }
}

export class DiscountStorageError extends DiscountError {
  constructor(message: string) {
    super(message, 'STORAGE_ERROR');
  }
}

export class DiscountUsageLimitError extends DiscountError {
  constructor(discountName: string) {
    super(`Discount "${discountName}" has reached its usage limit`, 'USAGE_LIMIT_EXCEEDED');
  }
}

export class DiscountExpiredError extends DiscountError {
  constructor(discountName: string) {
    super(`Discount "${discountName}" has expired`, 'DISCOUNT_EXPIRED');
  }
}

// Error type guard functions
export function isDiscountError(error: unknown): error is DiscountError {
  return error instanceof DiscountError;
}

export function isDiscountNotFoundError(error: unknown): error is DiscountNotFoundError {
  return error instanceof DiscountNotFoundError;
}

export function isDiscountCalculationError(error: unknown): error is DiscountCalculationError {
  return error instanceof DiscountCalculationError;
}
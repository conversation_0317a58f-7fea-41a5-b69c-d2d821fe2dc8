/**
 * Custom error classes for the discount system
 *
 * These error classes provide structured error handling throughout the system,
 * enabling proper error categorization and handling.
 */

import type { DiscountType } from '../../features/discount/discount.types';

// Base discount error class
export class DiscountError extends Error {
  constructor(message: string, public code: string) {
    super(message);
    this.name = 'DiscountError';
  }
}

// Specific error types
export class DiscountNotFoundError extends DiscountError {
  constructor(id: string) {
    super(`Discount with id ${id} not found`, 'DISCOUNT_NOT_FOUND');
  }
}

export class DiscountCalculationError extends DiscountError {
  constructor(
    message: string,
    public readonly context?: {
      discountType?: DiscountType;
      cartTotal?: number;
    }
  ) {
    super(message, 'CALCULATION_ERROR');
    this.name = 'DiscountCalculationError';
  }
}

export class DiscountValidationError extends DiscountError {
  constructor(
    message: string,
    public readonly context?: {
      discountType?: DiscountType;
      validationErrors?: string[];
    }
  ) {
    super(message, 'VALIDATION_ERROR');
    this.name = 'DiscountValidationError';
  }
}

export class DiscountStorageError extends DiscountError {
  constructor(message: string) {
    super(message, 'STORAGE_ERROR');
  }
}

export class DiscountUsageLimitError extends DiscountError {
  constructor(discountName: string) {
    super(`Discount "${discountName}" has reached its usage limit`, 'USAGE_LIMIT_EXCEEDED');
  }
}

export class DiscountExpiredError extends DiscountError {
  constructor(discountName: string) {
    super(`Discount "${discountName}" has expired`, 'DISCOUNT_EXPIRED');
  }
}

// Error type guard functions
export function isDiscountError(error: unknown): error is DiscountError {
  return error instanceof DiscountError;
}

export function isDiscountNotFoundError(error: unknown): error is DiscountNotFoundError {
  return error instanceof DiscountNotFoundError;
}

export function isDiscountCalculationError(error: unknown): error is DiscountCalculationError {
  return error instanceof DiscountCalculationError;
}

export function isDiscountValidationError(error: unknown): error is DiscountValidationError {
  return error instanceof DiscountValidationError;
}

// Factory functions for backward compatibility and specific use cases
export const createServiceCalculationError = (message: string, cartTotal?: number): DiscountCalculationError => {
  return new DiscountCalculationError(message, { cartTotal });
};

export const createServiceValidationError = (message: string, validationErrors: string[]): DiscountValidationError => {
  return new DiscountValidationError(message, { validationErrors });
};

export const createEngineCalculationError = (type: DiscountType, message: string): DiscountCalculationError => {
  return new DiscountCalculationError(message, { discountType: type });
};

export const createEngineValidationError = (type: DiscountType, message: string): DiscountValidationError => {
  return new DiscountValidationError(message, { discountType: type });
};
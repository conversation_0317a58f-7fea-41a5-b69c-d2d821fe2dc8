# Server-Side Architecture

This directory contains the server-side implementation of the Infinity Portal backend features, organized using a feature-based architecture pattern.

## Directory Structure

```
src/server/
├── features/                     # Feature-based organization
│   └── discount/                 # Discount system feature
│       ├── discount.types.ts     # Core discount types and interfaces
│       ├── discount.engine.interface.ts # Discount calculation engine interfaces
│       └── storage/              # Storage layer
│           └── storage.interface.ts # Storage provider interface
│
├── shared/                       # Shared server utilities
│   ├── types/                    # Shared type definitions
│   │   └── api.types.ts          # API response and pagination types
│   └── utils/                    # Shared utilities
│       └── errors.ts             # Custom error classes
│
├── index.ts                      # Server exports
└── README.md                     # This file
```

## Architecture Principles

### Feature-Based Organization
- Each feature is self-contained in its own directory
- Features export their public interfaces through the main server index
- Internal feature implementation details are kept private

### Pluggable Architecture
- Storage providers implement a common interface for easy swapping
- Discount calculators use a plugin system for extensibility
- Error handling is centralized and consistent

### Type Safety
- All interfaces are strongly typed with TypeScript
- Shared types ensure consistency between server and client
- Custom error classes provide structured error handling

## Current Features

### Discount System
The discount system provides flexible discount management with:
- Pluggable storage providers (in-memory, database, etc.)
- Extensible discount calculation engine
- Type-safe interfaces for all operations
- Comprehensive error handling

## Usage

Import server functionality using the centralized exports:

```typescript
import { 
  DiscountEngine, 
  DiscountStorageProvider,
  DiscountError 
} from '@/server';
```
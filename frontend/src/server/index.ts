/**
 * Server exports
 * 
 * This file provides a centralized export point for server-side modules,
 * making it easy to import server functionality from other parts of the application.
 */

// Feature exports - Core types
export * from './features/discount/discount.types';
export * from './features/discount/discount.engine.interface';

// Feature exports - Validation (with explicit re-exports to avoid conflicts)
export {
  // Zod schemas
  BaseDiscountSchema,
  PercentageCapDiscountSchema,
  CreateDiscountSchema,
  UpdateDiscountSchema,
  CartItemSchema,
  CalculateDiscountSchema,
  GetDiscountByIdSchema,
  DeleteDiscountSchema,
  ListDiscountsSchema,
  GetUsageStatsSchema,
  DiscountResponseSchema,
  AppliedDiscountSchema,
  DiscountCalculationResultSchema,
  PaginatedDiscountsResponseSchema,
  UsageStatsResponseSchema,
  
  // Validation helpers
  ValidationHelpers
} from './features/discount/discount.validation';

// Zod-inferred types (separate export type for isolatedModules compatibility)
export type {
  CreateDiscountInput,
  UpdateDiscountInput,
  CalculateDiscountInput,
  GetDiscountByIdInput,
  ListDiscountsInput,
  GetUsageStatsInput,
  DiscountResponse,
  PaginatedDiscountsResponse,
  UsageStatsResponse,
  CartItem,
  AppliedDiscount as ZodAppliedDiscount,
  DiscountCalculationResult as ZodDiscountCalculationResult
} from './features/discount/discount.validation';

export * from './features/discount/discount.validation.helpers';

// Shared utilities (explicit exports to avoid conflicts)
export type {
  ApiResponse,
  PaginationParams,
  PaginatedResponse,
  ApiError,
  DateRangeFilter,
  BaseFilters
} from './shared/types/api.types';

// Shared error classes and utilities
export {
  DiscountError,
  DiscountNotFoundError,
  DiscountCalculationError,
  DiscountValidationError,
  DiscountStorageError,
  DiscountUsageLimitError,
  DiscountExpiredError,
  isDiscountError,
  isDiscountNotFoundError,
  isDiscountCalculationError,
  isDiscountValidationError,
  // Factory functions for specific use cases
  createServiceCalculationError,
  createServiceValidationError,
  createEngineCalculationError,
  createEngineValidationError
} from './shared/utils/errors';

// Storage interfaces
export * from './features/discount/storage/storage.interface';
export * from './features/discount/storage/storage.errors';
export * from './features/discount/storage/storage.utils';
export * from './features/discount/storage/in-memory.provider';
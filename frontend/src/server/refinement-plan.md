# Server Code Duplication Refinement Plan

## Executive Summary

This document outlines a comprehensive plan to eliminate code duplication in the `/frontend/src/server/` directory while maintaining existing functionality and following DRY principles. The analysis identified several critical areas of duplication that need consolidation.

## Identified Duplications

### 1. **Critical: Duplicate Error Classes**

#### **DiscountCalculationError** - 3 Locations
- **Location 1**: `shared/utils/errors.ts` (lines 23-27)
  - Simple constructor: `constructor(message: string)`
  - Code: `'CALCULATION_ERROR'`
  - Base class: `DiscountError`

- **Location 2**: `features/discount/discount.service.errors.ts` (lines 50-55)
  - Enhanced constructor: `constructor(message: string, public readonly cartTotal?: number)`
  - Code: `'CALCULATION_ERROR'`
  - Base class: `DiscountServiceError`

- **Location 3**: `features/discount/discount.engine.interface.ts` (lines 236-240)
  - Type-specific constructor: `constructor(type: DiscountType, message: string)`
  - Code: `'CALCULATION_ERROR'`
  - Base class: `DiscountEngineError`

#### **DiscountValidationError** - 3 Locations
- **Location 1**: `shared/utils/errors.ts` (lines 29-33)
  - Simple constructor: `constructor(message: string)`
  - Code: `'VALIDATION_ERROR'`
  - Base class: `DiscountError`

- **Location 2**: `features/discount/discount.service.errors.ts` (lines 30-35)
  - Enhanced constructor: `constructor(message: string, public readonly validationErrors: string[])`
  - Code: `'VALIDATION_ERROR'`
  - Base class: `DiscountServiceError`

- **Location 3**: `features/discount/discount.engine.interface.ts` (lines 242-246)
  - Type-specific constructor: `constructor(type: DiscountType, message: string)`
  - Code: `'VALIDATION_ERROR'`
  - Base class: `DiscountEngineError`

### 2. **Duplicate Type Definitions**

#### **PaginatedResult/PaginatedResponse** - 2 Locations
- **Location 1**: `shared/types/api.types.ts` (lines 22-28)
  ```typescript
  export interface PaginatedResponse<T> {
    items: T[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }
  ```

- **Location 2**: `features/discount/storage/storage.interface.ts` (lines 25-31)
  ```typescript
  export interface PaginatedResult<T> {
    items: T[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }
  ```

#### **PaginationParams/PaginationOptions** - 2 Locations
- **Location 1**: `shared/types/api.types.ts` (lines 17-20)
  ```typescript
  export interface PaginationParams {
    page: number;
    limit: number;
  }
  ```

- **Location 2**: `features/discount/storage/storage.interface.ts` (lines 33-36)
  ```typescript
  export interface PaginationOptions {
    page: number;
    limit: number;
  }
  ```

### 3. **Duplicate Utility Functions**

#### **Data Transformation Utilities**
- **Location 1**: `features/discount/discount.utils.ts` (lines 327-340)
  - `deepClone()`, `sanitizeString()`, `normalizeWhitespace()`

- **Location 2**: `features/discount/storage/storage.utils.ts` (lines 240-261)
  - `cloneDiscount()`, `sanitizeDiscountInput()` (similar string normalization)

#### **Validation Utilities**
- **Location 1**: `features/discount/discount.validation.helpers.ts` (lines 69-154)
  - Field validation schemas and refinements

- **Location 2**: `features/discount/storage/storage.utils.ts` (lines 273-291)
  - `validatePagination()` function

### 4. **Configuration Duplication**

#### **Health Check Thresholds**
- **Location 1**: `features/discount/discount.config.ts` (lines 27-30, 97-100)
  - Storage health check thresholds in `StorageConfig`

- **Location 2**: `features/discount/discount.config.ts` (lines 54-57)
  - Repository health check thresholds in `RepositoryConfig`

## Consolidation Plan

### Phase 1: Error Class Consolidation

#### **Step 1.1: Create Unified Error Hierarchy**
**Target Location**: `shared/utils/errors.ts`

**Action**: Extend existing error classes to support all use cases:

```typescript
// Enhanced base error with optional metadata
export class DiscountCalculationError extends DiscountError {
  constructor(
    message: string,
    public readonly context?: {
      discountType?: DiscountType;
      cartTotal?: number;
    }
  ) {
    super(message, 'CALCULATION_ERROR');
  }
}

export class DiscountValidationError extends DiscountError {
  constructor(
    message: string,
    public readonly context?: {
      discountType?: DiscountType;
      validationErrors?: string[];
    }
  ) {
    super(message, 'VALIDATION_ERROR');
  }
}
```

#### **Step 1.2: Update Import Statements**
**Files to Update**:
- `features/discount/discount.service.errors.ts` - Remove duplicate classes
- `features/discount/discount.engine.interface.ts` - Remove duplicate classes
- All test files and implementation files using these errors

**Migration Strategy**:
1. Update imports to use shared error classes
2. Modify error instantiation to use new context parameter
3. Update error handling logic to access context properties

### Phase 2: Type Definition Consolidation

#### **Step 2.1: Unify Pagination Types**
**Target Location**: `shared/types/api.types.ts`

**Action**: 
1. Keep `PaginatedResponse<T>` as the canonical type
2. Export alias for backward compatibility:
   ```typescript
   export type PaginatedResult<T> = PaginatedResponse<T>;
   export type PaginationOptions = PaginationParams;
   ```

#### **Step 2.2: Update Storage Interface**
**File**: `features/discount/storage/storage.interface.ts`

**Action**: Replace local types with imports from shared types:
```typescript
import type { PaginatedResponse, PaginationParams } from '../../../shared/types/api.types';

// Remove duplicate type definitions
// Update all references to use imported types
```

### Phase 3: Utility Function Consolidation

#### **Step 3.1: Create Shared Utilities Module**
**Target Location**: `shared/utils/common.ts` (new file)

**Action**: Extract common utilities:
```typescript
export const DataUtils = {
  deepClone: <T>(obj: T): T => JSON.parse(JSON.stringify(obj)),
  sanitizeString: (str: string) => str.trim().replace(/\s+/g, ' '),
  normalizeWhitespace: (str: string) => str.replace(/\s+/g, ' ').trim()
};

export const ValidationUtils = {
  validatePagination: (pagination?: PaginationParams) => { /* ... */ }
};
```

#### **Step 3.2: Update Feature-Specific Utils**
**Files to Update**:
- `features/discount/discount.utils.ts` - Remove duplicated functions, import from shared
- `features/discount/storage/storage.utils.ts` - Remove duplicated functions, import from shared

### Phase 4: Configuration Consolidation

#### **Step 4.1: Unify Health Check Configuration**
**File**: `features/discount/discount.config.ts`

**Action**: Create shared health check interface:
```typescript
export interface HealthCheckThresholds {
  degradedRatio: number;
  unhealthyRatio: number;
}

// Use in both StorageConfig and RepositoryConfig
export interface StorageConfig {
  maxDiscounts: number;
  maxUsageEntries: number;
  healthCheckThresholds: HealthCheckThresholds;
}

export interface RepositoryConfig {
  enableCaching: boolean;
  cacheTimeoutMs: number;
  enableMetrics: boolean;
  healthCheckThresholds: HealthCheckThresholds;
}
```

## Implementation Strategy

### **Priority Order**
1. **High Priority**: Error class consolidation (Phase 1)
2. **Medium Priority**: Type definition consolidation (Phase 2)
3. **Medium Priority**: Utility function consolidation (Phase 3)
4. **Low Priority**: Configuration consolidation (Phase 4)

### **Risk Mitigation**
1. **Backward Compatibility**: Maintain aliases for removed types during transition
2. **Incremental Migration**: Update one module at a time
3. **Test Coverage**: Ensure all existing tests pass after each phase
4. **Import Analysis**: Use IDE tools to find all references before removal

### **Breaking Change Prevention**
1. **Export Aliases**: Maintain old export names temporarily
2. **Gradual Deprecation**: Add deprecation warnings before removal
3. **Documentation Updates**: Update all references in documentation

## Expected Benefits

### **Code Quality Improvements**
- **Reduced Duplication**: Eliminate 6 duplicate error classes and 4 duplicate type definitions
- **Improved Maintainability**: Single source of truth for common functionality
- **Enhanced Type Safety**: Consistent interfaces across all modules

### **Developer Experience**
- **Clearer Architecture**: Well-defined shared vs feature-specific boundaries
- **Easier Debugging**: Consistent error handling patterns
- **Reduced Cognitive Load**: Fewer similar-but-different implementations

### **Future Extensibility**
- **Easier Feature Addition**: Clear patterns for new discount types
- **Simplified Testing**: Centralized utilities for common test scenarios
- **Better Error Handling**: Rich context information for debugging

## Detailed Implementation Steps

### **Phase 1 Detailed Steps: Error Class Consolidation**

#### **Step 1.1: Enhance Shared Error Classes**
**File**: `shared/utils/errors.ts`

**Changes**:
1. Add optional context parameter to existing error classes
2. Maintain backward compatibility with simple constructors
3. Add type guards for context properties

**Implementation**:
```typescript
// Enhanced DiscountCalculationError
export class DiscountCalculationError extends DiscountError {
  constructor(
    message: string,
    context?: {
      discountType?: DiscountType;
      cartTotal?: number;
    }
  ) {
    super(message, 'CALCULATION_ERROR');
    this.context = context;
  }

  public readonly context?: {
    discountType?: DiscountType;
    cartTotal?: number;
  };
}

// Enhanced DiscountValidationError
export class DiscountValidationError extends DiscountError {
  constructor(
    message: string,
    context?: {
      discountType?: DiscountType;
      validationErrors?: string[];
    }
  ) {
    super(message, 'VALIDATION_ERROR');
    this.context = context;
  }

  public readonly context?: {
    discountType?: DiscountType;
    validationErrors?: string[];
  };
}
```

#### **Step 1.2: Update Service Error Usage**
**File**: `features/discount/discount.service.errors.ts`

**Changes**:
1. Remove duplicate error class definitions
2. Import from shared utils
3. Create factory functions for service-specific usage

**Implementation**:
```typescript
import {
  DiscountCalculationError as SharedDiscountCalculationError,
  DiscountValidationError as SharedDiscountValidationError
} from '../../../shared/utils/errors';

// Factory functions for service-specific error creation
export const createServiceCalculationError = (message: string, cartTotal?: number) => {
  return new SharedDiscountCalculationError(message, { cartTotal });
};

export const createServiceValidationError = (message: string, validationErrors: string[]) => {
  return new SharedDiscountValidationError(message, { validationErrors });
};

// Export aliases for backward compatibility
export const DiscountCalculationError = SharedDiscountCalculationError;
export const DiscountValidationError = SharedDiscountValidationError;
```

#### **Step 1.3: Update Engine Error Usage**
**File**: `features/discount/discount.engine.interface.ts`

**Changes**:
1. Remove duplicate error class definitions
2. Import from shared utils
3. Create factory functions for engine-specific usage

**Implementation**:
```typescript
import {
  DiscountCalculationError as SharedDiscountCalculationError,
  DiscountValidationError as SharedDiscountValidationError
} from '../../../shared/utils/errors';

// Factory functions for engine-specific error creation
export const createEngineCalculationError = (type: DiscountType, message: string) => {
  return new SharedDiscountCalculationError(message, { discountType: type });
};

export const createEngineValidationError = (type: DiscountType, message: string) => {
  return new SharedDiscountValidationError(message, { discountType: type });
};

// Export aliases for backward compatibility
export const DiscountCalculationError = SharedDiscountCalculationError;
export const DiscountValidationError = SharedDiscountValidationError;
```

### **Phase 2 Detailed Steps: Type Consolidation**

#### **Step 2.1: Update Shared API Types**
**File**: `shared/types/api.types.ts`

**Changes**:
1. Add backward compatibility aliases
2. Enhance existing types if needed

**Implementation**:
```typescript
// Existing types remain unchanged
export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface PaginationParams {
  page: number;
  limit: number;
}

// Backward compatibility aliases
export type PaginatedResult<T> = PaginatedResponse<T>;
export type PaginationOptions = PaginationParams;
```

#### **Step 2.2: Update Storage Interface**
**File**: `features/discount/storage/storage.interface.ts`

**Changes**:
1. Import shared types
2. Remove duplicate type definitions
3. Update all type references

**Implementation**:
```typescript
import type {
  PaginatedResponse as PaginatedResult,
  PaginationParams as PaginationOptions
} from '../../../shared/types/api.types';

// Remove these duplicate definitions:
// - interface PaginatedResult<T>
// - interface PaginationOptions

// All other interfaces remain unchanged
```

### **Phase 3 Detailed Steps: Utility Consolidation**

#### **Step 3.1: Create Shared Utilities**
**File**: `shared/utils/common.ts` (new file)

**Implementation**:
```typescript
/**
 * Shared utility functions for server-side operations
 */

export const DataUtils = {
  /**
   * Create a deep clone of an object
   */
  deepClone: <T>(obj: T): T => {
    return JSON.parse(JSON.stringify(obj));
  },

  /**
   * Sanitize string input by trimming and normalizing whitespace
   */
  sanitizeString: (str: string): string => {
    return str.trim().replace(/\s+/g, ' ');
  },

  /**
   * Normalize whitespace in a string
   */
  normalizeWhitespace: (str: string): string => {
    return str.replace(/\s+/g, ' ').trim();
  }
};

export const ValidationUtils = {
  /**
   * Validate pagination parameters
   */
  validatePagination: (pagination?: { page: number; limit: number }): string[] => {
    const errors: string[] = [];

    if (pagination) {
      if (pagination.page < 1) {
        errors.push('Page must be at least 1');
      }

      if (pagination.limit < 1) {
        errors.push('Limit must be at least 1');
      }

      if (pagination.limit > 100) {
        errors.push('Limit cannot exceed 100');
      }
    }

    return errors;
  }
};
```

#### **Step 3.2: Update Feature Utils**
**File**: `features/discount/discount.utils.ts`

**Changes**:
1. Import shared utilities
2. Remove duplicate implementations
3. Update references

**Implementation**:
```typescript
import { DataUtils } from '../../../shared/utils/common';

// Remove duplicate implementations:
// - deepClone function
// - sanitizeString function
// - normalizeWhitespace function

// Update DataTransformHelpers to use shared utilities
export const DataTransformHelpers = {
  deepClone: DataUtils.deepClone,
  sanitizeString: DataUtils.sanitizeString,
  normalizeWhitespace: DataUtils.normalizeWhitespace,

  // Keep discount-specific transformations
  sanitizeDiscountData: (data: any) => {
    // Discount-specific logic here
  }
};
```

## File-by-File Migration Checklist

### **Files Requiring Updates**

#### **High Priority (Phase 1)**
- [ ] `shared/utils/errors.ts` - Enhance error classes
- [ ] `features/discount/discount.service.errors.ts` - Remove duplicates, add factories
- [ ] `features/discount/discount.engine.interface.ts` - Remove duplicates, add factories
- [ ] `features/discount/discount.service.ts` - Update error imports
- [ ] `features/discount/discount.engine.ts` - Update error imports
- [ ] All test files using these errors

#### **Medium Priority (Phase 2)**
- [ ] `shared/types/api.types.ts` - Add compatibility aliases
- [ ] `features/discount/storage/storage.interface.ts` - Remove duplicates, import shared
- [ ] `features/discount/storage/in-memory.provider.ts` - Update type references
- [ ] `features/discount/discount.repository.ts` - Update type references

#### **Medium Priority (Phase 3)**
- [ ] `shared/utils/common.ts` - Create new shared utilities file
- [ ] `features/discount/discount.utils.ts` - Remove duplicates, import shared
- [ ] `features/discount/storage/storage.utils.ts` - Remove duplicates, import shared
- [ ] Update all files importing these utilities

#### **Low Priority (Phase 4)**
- [ ] `features/discount/discount.config.ts` - Consolidate health check configs

### **Testing Strategy**

#### **Pre-Migration Testing**
1. Run full test suite to establish baseline
2. Document all current test results
3. Identify tests that specifically test duplicate functionality

#### **During Migration Testing**
1. Run tests after each file modification
2. Verify no functionality changes
3. Test error handling scenarios specifically

#### **Post-Migration Testing**
1. Full regression test suite
2. Integration tests for error handling
3. Type checking with TypeScript compiler
4. Manual testing of error scenarios

## Risk Assessment and Mitigation

### **High Risk Areas**
1. **Error Handling Changes**: Risk of breaking existing error handling logic
   - **Mitigation**: Maintain backward compatibility with factory functions
   - **Testing**: Comprehensive error scenario testing

2. **Type System Changes**: Risk of TypeScript compilation errors
   - **Mitigation**: Use type aliases for backward compatibility
   - **Testing**: Full TypeScript compilation check

3. **Import Dependencies**: Risk of circular dependencies
   - **Mitigation**: Careful dependency analysis before changes
   - **Testing**: Dependency graph validation

### **Medium Risk Areas**
1. **Utility Function Changes**: Risk of subtle behavior differences
   - **Mitigation**: Exact function replication in shared utilities
   - **Testing**: Unit tests for all utility functions

2. **Configuration Changes**: Risk of runtime configuration issues
   - **Mitigation**: Gradual migration with fallbacks
   - **Testing**: Configuration validation tests

## Success Metrics

### **Code Quality Metrics**
- **Duplication Reduction**: Target 90% reduction in duplicate code
- **Import Simplification**: Reduce import statements by ~30%
- **Type Safety**: Maintain 100% TypeScript compilation success

### **Maintainability Metrics**
- **Single Source of Truth**: All common functionality centralized
- **Consistent Patterns**: Unified error handling and type usage
- **Documentation Coverage**: All shared utilities documented

## Next Steps

1. **Review and Approval**: Get stakeholder approval for this detailed plan
2. **Test Suite Preparation**: Ensure comprehensive test coverage before changes
3. **Phase 1 Implementation**: Start with error class consolidation
4. **Incremental Rollout**: Implement one phase at a time with validation
5. **Documentation Update**: Update all relevant documentation and examples

---

*This comprehensive plan ensures zero functionality loss while significantly improving code organization and maintainability through systematic elimination of code duplication.*

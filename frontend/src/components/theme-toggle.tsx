"use client"

import * as React from "react"
import { useTheme } from "next-themes"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Sun, Moon } from "lucide-react"

export function ThemeToggle() {
    const { theme, setTheme } = useTheme()

    const toggleTheme = () => {
        if (theme === "light") {
            setTheme("dark")
        } else {
            setTheme("light")
        }
    }

    const getIcon = () => {
        switch (theme) {
            case "light":
                return <Sun className="h-[1.2rem] w-[1.2rem] text-foreground" />
            case "dark":
                return <Moon className="h-[1.2rem] w-[1.2rem] text-foreground" />
            default:
                return <Sun className="h-[1.2rem] w-[1.2rem] text-foreground" />
        }
    }

    return (
        <Button
            variant="outline"
            size="icon"
            onClick={toggleTheme}
            className="h-9 w-9"
        >
            {getIcon()}
            <span className="sr-only">Toggle theme</span>
        </Button>
    )
} 
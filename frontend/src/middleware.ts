import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
    const { pathname } = request.nextUrl;

    // Block admin routes in customer mode (production/test)
    if (process.env.NEXT_PUBLIC_APP_MODE === 'customer' && pathname.startsWith('/admin')) {
        // Redirect to 404 or home page
        return NextResponse.redirect(new URL('/404', request.url));
    }

    // Block admin API routes in customer mode
    if (process.env.NEXT_PUBLIC_APP_MODE === 'customer' && pathname.startsWith('/api/admin')) {
        return new NextResponse(
            JSON.stringify({ error: 'Not Found' }),
            {
                status: 404,
                headers: {
                    'Content-Type': 'application/json',
                },
            }
        );
    }

    return NextResponse.next();
}

export const config = {
    matcher: [
        '/admin/:path*',
        '/api/admin/:path*'
    ]
}; 
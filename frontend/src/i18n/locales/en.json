{"common": {"close": "Close", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "refresh": "Refresh", "remove": "Remove", "retry": "Try Again", "processing": "Processing...", "saving": "Saving...", "loading": "Loading...", "back": "Back", "done": "Done", "unknownError": "Unknown error", "unnamedProduct": "Unnamed Product", "unnamedVariant": "Unnamed Variant", "backToHome": "Back to Home", "category": "Category", "categories": "Categories", "error": "Error"}, "header": {"login": "<PERSON><PERSON>", "account": "Account", "profile": "Profile", "settings": "Settings", "logout": "Logout", "categories": {"ariaLabel": "Open categories menu"}, "mobileMoreMenu": {"title": "More Options", "languageSection": {"title": "Select Language"}, "account": "Account", "logoutButton": "Logout", "loginButton": "<PERSON><PERSON>"}, "accountMenu": {"account": "Account"}}, "navigation": {"home": "Home", "categories": "Categories", "cart": "<PERSON><PERSON>", "sessions": "Sessions"}, "auth": {"loading": "Loading...", "loginModal": {"title": "User Login", "adminTitle": "<PERSON><PERSON><PERSON><PERSON> Required", "subtitleMobile": "Enter your mobile number to receive an OTP.", "adminSubtitle": "<PERSON>gin as <PERSON><PERSON><PERSON><PERSON> to access admin features. You can browse categories and products while logging in.", "subtitleOtp": "Enter the OTP sent to your mobile number.", "label": {"mobileNumber": "Mobile Number"}, "placeholder": {"mobileNumber": "Enter 10-digit mobile number"}, "button": {"sendOtp": "Send OTP", "sendingOtp": "Sending OTP...", "verifyOtp": "Verify OTP", "verifyingOtp": "Verifying OTP...", "resend": "Resend", "resending": "Resending..."}, "error": {"invalidMobile": "Please enter a valid 10-digit mobile number.", "sendOtpFailed": "Failed to send <PERSON><PERSON>. Please try again.", "invalidOtpLength": "OTP must be {{length}} digits.", "verifyOtpFailed": "OTP verification failed. Please try again."}, "otpSentTo": "OTP sent to +91 {{mobileNumber}}.", "otpNotReceived": "Didn't receive the OTP? Check your mobile network or try resending."}}, "session": {"newCustomer": "New Customer", "noCustomerName": "Unnamed", "newSession": "New Cart Session", "editSession": "<PERSON> Cart Session", "name": "Name", "nameRequired": "Name is required", "namePlaceholder": "Enter customer name", "phone": "Phone Number", "phonePlaceholder": "Enter phone number (optional)", "location": "Location", "selectedCoordinates": "Selected coordinates", "noPhone": "No phone", "session": "Session", "guestName": "Guest {{number}}", "defaultCustomerName": "Guest", "guestCustomerName": "Guest Customer", "addNew": "Add New Session", "edit": "Edit Session", "deleteSession": "Delete Session", "confirmDelete": "Delete Session?", "confirmDeleteMessage": "Are you sure you want to delete this session? This action cannot be undone.", "newTab": "Add New Tab", "itemInCart": "item", "itemsInCart": "items", "delete": "Delete Session"}, "categories": {"title": "Categories", "loading": "Loading categories...", "error": "Error loading categories:", "noCategories": "No categories found.", "missingTitle": "Unnamed Category", "collapse": "Collapse sidebar", "expand": "Expand sidebar", "page": {"loading": "Loading...", "loadingCategory": "Loading category information...", "errorCategory": "Error loading category:", "notFound": {"title": "Category Not Found", "messageById": "The category with ID {{categoryId}} was not found."}, "unnamedCategory": "Unnamed Category", "loadingProducts": "Loading products...", "errorProducts": "Error loading products:", "noProducts": "No products found in this category.", "noProductsDescription": "This category doesn't have any products yet."}, "section": {"viewMore": "View More", "error": {"loadingProducts": "Error loading products:", "unknownError": "Unknown error"}, "noProducts": "No products found in this category.", "accessibility": {"scrollLeft": "<PERSON><PERSON> left", "scrollRight": "<PERSON><PERSON> right"}}}, "categoriesDrawer": {"title": "Categories", "loading": "Loading categories...", "error": "Error loading categories:", "noCategories": "No categories found.", "missingTitle": "Unnamed Category", "collapse": "Collapse sidebar", "expand": "Expand sidebar"}, "product": {"addToCart": "Add to Cart", "selectVariant": "Select Option", "price": "Price: ₹{{price}}", "loading": "Loading...", "unnamedProduct": "Unnamed Product", "page": {"loading": "Loading..."}, "detail": {"selectVariant": "Select Size/Variant", "select": "Select...", "addToCart": "Add to Cart", "adding": "Adding...", "decreaseQuantity": "Decrease quantity", "increaseQuantity": "Increase quantity", "description": "Product Description", "selectVariantModalTitle": "Select Variant", "closeModal": "Close modal"}, "card": {"off": "OFF", "selectVariantTooltip": "Select variant", "defaultUnit": "Unit", "adding": "Adding...", "add": "ADD", "decreaseQuantity": "Decrease quantity", "increaseQuantity": "Increase quantity", "selectVariantTitle": "Select Variant", "selected": "Selected"}}, "cart": {"myCart": "My Cart", "loading": "Loading cart...", "updating": "Updating cart...", "validating": "Validating cart...", "emptyCart": "Your cart is empty.", "continueShopping": "Continue Shopping", "validateCart": "Check for availability", "itemsRemoved": "{{count}} item(s) were removed from your cart because they are no longer available.", "totalSavings": "Your total savings", "billDetails": "Bill details", "itemsTotal": "Items total", "saved": "Saved", "deliveryCharge": "Delivery charge", "free": "FREE", "handlingCharge": "Handling charge", "grandTotal": "Grand total", "total": "TOTAL", "proceedToCheckout": "Proceed to Checkout", "loginToProceed": "Login to Proceed", "deliveryTime": "Delivery in 6 minutes", "shipmentOf": "Shipment of", "item": "item", "items": "items", "decreaseQuantity": "Decrease quantity", "increaseQuantity": "Increase quantity", "cancellationPolicy": "Cancellation Policy", "cancellationText": "Orders cannot be cancelled once packed for delivery. In case of unexpected delays, a refund will be provided, if applicable.", "dismissNotification": "Dismiss notification", "checkout": "Place Order"}, "cartDrawer": {"billDetails": "Bill details", "itemsTotal": "Items total", "saved": "Saved", "grandTotal": "Grand total", "totalSavings": "Your total savings", "cancellationPolicy": "Cancellation Policy", "cancellationText": "Orders cannot be cancelled once packed for delivery. In case of unexpected delays, a refund will be provided, if applicable.", "decreaseQuantity": "Decrease quantity", "increaseQuantity": "Increase quantity", "total": "TOTAL", "checkout": "Place Order", "loginToProceed": "Login to Proceed"}, "checkout": {"title": "Checkout", "subtitle": "Please confirm your details to complete the order", "total": "Total: ₹{{total}}", "customerName": "Customer Name", "customerNamePlaceholder": "Enter customer name", "customerPhone": "Phone Number", "customerPhonePlaceholder": "Enter 10-digit phone number", "landmark": "Landmark", "landmarkPlaceholder": "Enter landmark (optional)", "location": "Delivery Location", "selectLocation": "Select delivery location", "locationSelected": "Location selected", "selectLocationTitle": "Select Delivery Location", "resolvingAddress": "Getting address...", "placeOrder": "Place Order - ₹{{total}}", "processing": "Processing Order...", "errors": {"nameRequired": "Customer name is required", "phoneRequired": "Phone number is required", "phoneInvalid": "Please enter a valid 10-digit phone number", "locationRequired": "Location is required"}, "confirmOrder": {"title": "Confirm Your Order", "message": "Are you sure you want to place this order?", "phone": "Phone:", "name": "Name:", "landmark": "Landmark:", "location": "Location:", "total": "Total:", "confirm": "Yes, Place Order"}}, "order": {"success": {"title": "Order Successful!", "message": "Thank you for your order. We'll start preparing it right away!", "callConfirmation": "We will call you for order confirmation", "orderDetails": "Order Details", "orderId": "Order ID:", "customer": "Customer:", "total": "Total:", "estimatedDelivery": "Estimated Delivery:", "deliveryInfo": "Your order will be delivered within 6 minutes!", "continue": "Continue Shopping", "trackOrder": "Track Order", "autoClose": "This dialog will close automatically in a few seconds"}}, "orderSuccess": {"continue": "Continue Shopping", "autoClose": "This dialog will close automatically in a few seconds"}, "map": {"dragMarker": "Drag to set location", "gettingLocation": "Getting your location...", "preview": "Map preview", "noLocation": "No location selected", "useCurrentLocation": "Use Current Location", "selectRandom": "Select Random Location", "myLocation": "Get my location", "readOnly": "Map is read-only", "readOnlyMode": "Read-only", "showVillagePolygons": "Show village boundaries", "hideVillagePolygons": "Hide village boundaries"}, "breadcrumbs": {"home": "Home"}, "homepage": {"loading": "Loading categories...", "error": {"loadingCategories": "Error loading categories:", "unknownError": "Unknown error"}, "noCategories": "No categories found."}, "whatsapp": {"greeting": "Hello! I need assistance with my order.", "arcMessage": "Need Help?", "buttonLabel": "Chat with us on WhatsApp", "buttonTitle": "Click to chat with us on WhatsApp"}, "mobileNav": {"categories": "Categories", "home": "Home", "sessions": "Sessions", "cart": "<PERSON><PERSON>"}, "mobileSessionsSheet": {"title": "Manage Sessions", "noSessions": "No active sessions."}, "search": {"placeholder": "Search products...", "noResults": "No products found", "searching": "Searching..."}, "leads": {"title": "Leads", "loading": "Loading leads...", "error": "Error loading leads:", "noLeads": "No leads found.", "totalLeads": "Total Leads", "newLead": "New Lead", "addLead": "Add Lead", "exportLeads": "Export Leads", "refreshLeads": "Refresh Leads", "searchPlaceholder": "Search by name or mobile number...", "search": {"mobile": "Mobile", "name": "Name"}, "filters": {"all": "All", "status": "Status", "source": "Source", "type": "Type", "dateRange": "Date Range", "dateFrom": "From Date", "dateTo": "To Date", "clearFilters": "Clear Filters", "applyFilters": "Apply Filters"}, "statuses": {"NEW": "New", "CONTACTED": "Contacted", "INTERESTED": "Interested", "CONVERTED": "Converted", "LOST": "Lost", "PENDING": "Pending"}, "sources": {"WHATSAPP": "WhatsApp"}, "types": {"CALLBACK_REQUESTED": "Callback Requested"}, "table": {"name": "Name", "mobile": "Mobile", "source": "Source", "type": "Type", "status": "Status", "created": "Created", "updated": "Updated", "actions": "Actions", "view": "View", "edit": "Edit", "delete": "Delete"}, "detail": {"title": "Lead #{{id}}", "subtitle": "View and manage lead information", "backToLeads": "Back to Leads", "editLead": "Edit Lead", "saveLead": "Save Changes", "cancelEdit": "Cancel", "updateSuccess": "Lead updated successfully", "updateError": "Failed to update lead", "invalidId": "Invalid lead ID", "notFound": "Lead not found", "loadError": "Failed to load lead", "overview": {"title": "Lead Overview", "status": "Status", "type": "Type", "source": "Source", "created": "Created", "updated": "Last Updated", "conversionTime": "Conversion Time", "noConversion": "Not converted"}, "contact": {"title": "Contact Information", "name": "Name", "mobile": "Mobile Number", "namePlaceholder": "Enter lead name", "mobilePlaceholder": "Enter mobile number"}, "statusUpdate": {"title": "Update Lead Status", "currentStatus": "Current Status", "newStatus": "New Status", "reason": "Reason (Optional)", "reasonPlaceholder": "Enter reason for status change...", "update": "Update Status", "cancel": "Cancel", "success": "Status updated successfully", "error": "Failed to update status"}, "notes": {"title": "Notes", "addNote": "Add Note", "notePlaceholder": "Enter your note...", "save": "Save Note", "cancel": "Cancel", "noNotes": "No notes added yet", "addSuccess": "Note added successfully", "addError": "Failed to add note"}, "timeline": {"title": "Timeline", "noTimeline": "No timeline entries yet", "statusChanged": "Status changed", "noteAdded": "Note added", "leadCreated": "Lead created", "leadUpdated": "Lead updated"}, "metadata": {"title": "Additional Information", "customFields": "Custom Fields", "lostReason": "Lost Reason", "lostReasonPlaceholder": "Enter reason for losing this lead...", "nextFollowUp": "Next Follow-up", "leadScore": "Lead Score", "leadScorePlaceholder": "Score (1-10)", "noCustomFields": "No custom fields", "noLostReason": "No reason specified", "noNextFollowUp": "No follow-up scheduled", "noLeadScore": "No score assigned"}, "conversion": {"title": "Conversion Details", "orderId": "Order ID", "orderValue": "Order Value", "convertedAt": "Converted At", "noConversionData": "No conversion data available", "orderIdPlaceholder": "Enter order ID", "orderValuePlaceholder": "Enter order value"}, "actions": {"updateStatus": "Update Status", "addNote": "Add Note", "edit": "Edit", "save": "Save", "cancel": "Cancel", "delete": "Delete"}}, "notes": {"addNote": "Add Note", "noteContent": "Note Content", "save": "Save Note", "cancel": "Cancel", "edit": "Edit Note", "delete": "Delete Note", "confirmDelete": "Are you sure you want to delete this note?", "placeholder": "Enter your note here...", "required": "Note content is required", "saved": "Note saved successfully", "deleted": "Note deleted successfully"}, "actions": {"updateStatus": "Update Status", "statusUpdated": "Lead status updated successfully", "statusUpdateFailed": "Failed to update lead status", "selectStatus": "Select new status", "confirmStatusChange": "Are you sure you want to change the status?", "lostReason": "Reason for marking as lost", "lostReasonRequired": "Please provide a reason for marking the lead as lost"}, "export": {"button": "Export CSV", "exporting": "Exporting...", "success": "Leads exported successfully", "error": "Failed to export leads", "filename": "leads-export-{{date}}"}, "pagination": {"showing": "Showing", "to": "to", "of": "of", "results": "results", "previous": "Previous", "next": "Next", "page": "Page {{page}} of {{total}}"}, "common": {"retry": "Retry", "filters": "Filters", "unexpectedError": "An unexpected error occurred", "lead": "lead", "leads": "leads", "found": "found", "subtitle": "Manage leads and track conversions", "exporting": "Exporting...", "id": "ID"}}, "admin": {"picker": {"title": "Order Picking", "subtitle": "Review and confirm items for order picking", "filters": {"title": "Filter Orders", "summary": "Showing {{count}} confirmed orders"}, "table": {"empty": "No confirmed orders found", "confirmedOrders": "{{count}} Confirmed Orders", "orderId": "Order ID", "status": "Status", "mobile": "Mobile", "itemsCount": "Items", "totalValue": "Total Value", "deliveryDate": "Delivery Date", "facility": "Facility", "noFacility": "No facility"}, "detail": {"title": "Order #{{id}}", "subtitle": "Pick items for this order", "backToPicker": "Back to Picker", "orderInfo": {"title": "Order Information", "status": "Status", "mobile": "Mobile", "deliveryDate": "Delivery Date", "facility": "Facility", "notSet": "Not set", "noFacility": "No facility"}, "summary": {"title": "Picking Summary", "totalOrdered": "Total Ordered", "totalPicked": "Total Picked", "difference": "Difference"}, "items": {"title": "Order Items", "subtitle": "Update quantities as needed", "empty": "No items in this order", "product": "Product", "unit": "Unit", "orderedQty": "Ordered", "pickedQty": "Picked", "unitPrice": "Unit Price", "price": "Price", "total": "Total", "orderTotal": "Order Total", "invalidSku": "Invalid SKU", "skuUnavailable": "SKU details unavailable"}, "instructions": {"title": "Picking Instructions", "verifyItems": "Verify all items against the order", "checkQuantity": "Check quantities carefully before confirming", "checkCondition": "Inspect items for damage or defects", "markWhenComplete": "Mark as picked only when all items are verified"}, "markPicked": {"button": "<PERSON> as Picked", "processing": "Processing...", "modalTitle": "Confirm Order Picked", "modalMessage": "Are you sure you want to mark this order as picked? This action cannot be undone.", "modalNote": "Please ensure all items have been picked and verified before confirming.", "confirmButton": "Confirm Order"}, "error": {"title": "Error Loading Order", "message": "Unable to load order details. Please try again later."}}}, "navigation": {"dashboard": "Dashboard", "orders": "Orders", "carts": "Carts", "categories": "Categories", "skus": "SKUs", "picker": "Picker", "returns": "Returns", "leads": "Leads", "collapse": "Collapse sidebar", "expand": "Expand sidebar"}, "portal": {"title": "WOW Admin", "subtitle": "Admin Portal", "version": "Version", "environment": "Environment", "logoAlt": "W<PERSON>", "branding": "Admin"}, "dashboard": {"title": "Admin Dashboard", "stats": {"totalCategories": "Total Categories", "totalSkus": "Total SKUs", "recentCarts": "Recent Carts", "recentOrders": "Recent Orders", "recentTimeframe": "Yesterday - Today", "categoriesDesc": "Active product categories", "skusDesc": "Total product SKUs", "cartsDesc": "Created in last 24h", "ordersDesc": "Placed in last 24h"}, "welcome": {"title": "Welcome to Admin Portal", "description": "Manage your categories, products, carts, and orders from this central dashboard."}, "quickActions": {"title": "Quick Actions", "subtitle": "Common admin tasks and operations", "createCategory": {"name": "Create Category", "description": "Add a new product category"}, "createSku": {"name": "Create SKU", "description": "Add a new SKU/Product"}, "viewCategories": {"name": "View Categories", "description": "Manage all categories"}, "viewSkus": {"name": "View SKUs", "description": "Manage all SKUs"}, "viewCarts": {"name": "View Carts", "description": "Monitor customer carts"}, "comingSoon": "Coming Soon"}, "recentActivity": {"title": "Recent Activity", "subtitle": "Latest system activities and updates", "placeholder": "Activity tracking will be available in future updates", "coming": "Activity feed coming soon"}}, "user": {"defaultName": "Admin User", "defaultMobile": "+91XXXXXXXXXX", "defaultRole": "Admin", "permission_one": "Permission", "permission_other": "Permissions", "roles": "Roles", "settings": "Settings", "soon": "Soon"}, "systemAdmin": {"title": "System Administration", "description": "High-privilege administrative functions", "menuTitle": "System Admin", "orderManagement": {"title": "Order Management", "schedulePickedOrders": "Schedule Picked Orders", "reassignOrderFacilities": "Reassign Order Facilities", "changeOrderDeliveryDate": "Change Order Delivery Date"}, "breadcrumbs": {"home": "Admin", "systemAdmin": "System Administration"}, "schedulePickedOrders": {"title": "Schedule Picked Orders", "subtitle": "Schedule orders for delivery that were created yesterday and picked today", "description": "Orders created yesterday that have been picked today and are ready for delivery scheduling", "table": {"orderId": "Order ID", "status": "Status", "mobile": "Mobile", "endCustomerName": "Customer Name", "itemsCount": "Items", "totalValue": "Total Value", "deliveryDate": "Delivery Date", "facility": "Facility", "empty": "There are no picked orders need to be scheduled", "loading": "Loading picked orders...", "noMobile": "No Mobile", "noCustomerName": "No Customer Name", "noFacility": "No Facility"}, "buttons": {"scheduleOrders": "Schedule Orders for Delivery", "refresh": "Refresh", "refreshing": "Refreshing..."}, "filters": {"yesterday": "Created Yesterday", "pickedStatus": "Picked Orders"}}, "reassignOrderFacilities": {"title": "Reassign Order Facilities", "subtitle": "Update facility assignments for scheduled orders", "description": "Showing scheduled orders that need facility reassignment", "filters": {"dateFrom": "From Date", "dateTo": "To Date", "dateRange": "Date Range", "scheduledOrders": "Scheduled Orders", "loadingFacilities": "Loading facilities...", "selectFacility": "Select facility"}, "table": {"orderId": "Order ID", "customerName": "Customer Name", "customerPhone": "Customer Phone", "facilityKey": "Facility", "actions": "Actions", "noCustomerName": "No Customer Name", "noCustomerPhone": "No Customer Phone", "noFacility": "No Facility", "empty": "No scheduled orders found for the selected date range", "facilitiesError": "Error loading facilities", "noFacilities": "No facilities available"}, "buttons": {"edit": "Edit", "save": "Save", "cancel": "Cancel", "saving": "Saving...", "refresh": "Refresh", "refreshing": "Refreshing..."}, "messages": {"facilityUpdatedSuccess": "Facility updated successfully", "facilityUpdateError": "Failed to update facility. Please try again."}}, "changeOrderDeliveryDate": {"title": "Change Order Delivery Date", "subtitle": "Update delivery dates for eligible orders", "description": "Showing orders eligible for delivery date changes (NEW, CONFIRMED, PICKED, SCHEDULED, READY_TO_SHIP)", "filters": {"dateFrom": "From Date", "dateTo": "To Date", "dateRange": "Date Range", "eligibleOrders": "Eligible Orders"}, "table": {"orderId": "Order ID", "customerName": "Customer Name", "customerPhone": "Customer Phone", "deliveryDate": "Delivery Date", "actions": "Actions", "noCustomerName": "No Customer Name", "noCustomerPhone": "No Customer Phone", "empty": "No eligible orders found for the selected date range"}, "buttons": {"edit": "Edit", "save": "Save", "cancel": "Cancel", "saving": "Saving...", "refresh": "Refresh", "refreshing": "Refreshing..."}, "messages": {"deliveryDateUpdatedSuccess": "Delivery date updated successfully", "deliveryDateUpdateError": "Failed to update delivery date. Please try again."}}}, "permissions": {"warning": {"title": "Access Restricted", "message": "You do not have permission to access this feature.", "engineeringTeam": "Please contact the engineering team if you need access to this functionality.", "futureAccess": "In future updates, you will not be able to access restricted features without proper permissions.", "continueButton": "Continue Anyway"}}, "actions": {"tryAgain": "Try Again", "back": "Back", "edit": "Edit", "save": "Save", "saving": "Saving...", "cancel": "Cancel", "discard": "Discard", "activate": "Activate", "deactivate": "Deactivate", "clearSelection": "Clear Selection", "actions": "Actions", "manage": "Manage", "dragToReorder": "Drag and drop to reorder", "create": "Create", "selected": "selected", "unsavedChanges": "You have unsaved changes", "dismiss": "<PERSON><PERSON><PERSON>", "close": "Close"}, "filters": {"title": "Filters", "clear": "Clear Filters", "search": "Search", "category": "Category", "allCategories": "All Categories", "status": "Status", "allStatuses": "All Statuses", "minPrice": "Min Price (₹)", "maxPrice": "Max Price (₹)"}, "status": {"active": "Active", "inactive": "Inactive"}, "importExport": {"export": "Export", "exporting": "Exporting...", "import": "Import", "importing": "Importing...", "previewChanges": "Preview Changes", "importWarning": "Review the imported data below before applying changes.", "applyChanges": "Apply Changes"}, "cart": {"title": "Carts", "subtitle": "View and manage customer carts", "export": {"title": "Export Carts", "filteredData": "Export filtered data ({{count}} carts)", "allData": "Export all data in date range ({{total}} carts)", "filteredDescription": "Export only the carts currently shown based on your filters", "allDescription": "Export all carts within the selected date range, ignoring other filters", "processing": "Preparing export...", "success": "Carts exported successfully", "error": "Failed to export carts. Please try again."}, "detail": {"title": "Cart Det<PERSON>", "subtitle": "View and manage cart information", "invalidId": "Invalid Cart ID", "notFound": "Cart Not Found", "loadError": "Failed to load cart", "backToCarts": "Back to Carts", "updateSuccess": "Cart updated successfully", "updateError": "Failed to update cart", "checkoutSuccess": "Cart checkout completed successfully", "checkoutError": "Failed to process checkout", "overview": "Cart Overview", "status": "Status", "created": "Created", "total": "Total", "items": "Items", "customerInfo": "Customer Information", "customerName": "Customer Name", "customerPhone": "Phone Number", "locationTitle": "Delivery Location", "coordinates": "Coordinates", "coordinatesPlaceholder": "Enter coordinates (lat lng)", "noLocation": "No location selected", "serviceability": "Serviceability", "serviceableWith": "Serviceable with {{facility}}", "notServiceable": "Not serviceable in this area", "map": "Map", "cartItems": "Cart Items", "addItem": "Add Item", "invalidItems": "Invalid Items", "product": "Product", "price": "Price", "quantity": "Quantity", "emptyCart": "This cart is empty", "quickActions": "Quick Actions", "processCheckout": "Process Checkout", "summary": "<PERSON>t <PERSON>mma<PERSON>", "itemCount": "<PERSON><PERSON>", "totalQuantity": "Total Quantity", "cartTotal": "Cart Total", "addItems": "Add Items to Cart", "searchSkus": "Search SKUs...", "confirmCheckout": "Confirm Checkout", "checkoutWarning": "This will process the cart for checkout. Are you sure?", "notReachable1": "Not Reachable 1", "notReachable2": "Not Reachable 2", "markingUnreachable": "Marking...", "unreachable": {"title1": "<PERSON> as Not Reachable 1", "title2": "<PERSON> as Not Reachable 2", "message1": "Are you sure you want to mark this cart as 'Not Reachable 1'? This indicates the first attempt to reach the customer was unsuccessful.", "message2": "Are you sure you want to mark this cart as 'Not Reachable 2'? This indicates the second attempt to reach the customer was unsuccessful.", "cancelButton": "Cancel", "confirmButton1": "Mark as Not Reachable 1", "confirmButton2": "<PERSON> as Not Reachable 2", "successMessage1": "<PERSON><PERSON> marked as Not Reachable 1 successfully", "successMessage2": "<PERSON><PERSON> marked as Not Reachable 2 successfully"}}, "list": {"actions": {"view": "View"}, "filters": {"refresh": "Refresh", "refreshing": "Refreshing..."}}}, "orders": {"title": "Orders", "subtitle": "View and manage customer orders", "error": {"title": "Error Loading Orders", "generic": "Failed to load orders. Please try again.", "retry": "Retry"}, "filters": {"title": "Filters", "dateFrom": "From Date", "dateTo": "To Date", "status": "Status", "allStatuses": "All Statuses", "facility": "Facility", "allFacilities": "All Facilities", "showing": "Showing {{count}} of {{total}} orders", "filtered": "Filtered", "clearFilters": "Clear Filters", "clear": "Clear", "show": "Show Filters", "hide": "Hide Filters", "refresh": "Refresh Data", "refreshing": "Refreshing...", "mobile": "Mobile Number", "mobilePlaceholder": "Enter mobile number"}, "export": {"title": "Export Orders", "filteredData": "Export filtered data ({{count}} orders)", "allData": "Export all data in date range ({{total}} orders)", "filteredDescription": "Export only the orders currently shown based on your filters", "allDescription": "Export all orders within the selected date range, ignoring status and facility filters", "processing": "Preparing export...", "success": "Orders exported successfully", "error": "Failed to export orders. Please try again."}, "status": {"pending": "Pending", "confirmed": "Confirmed", "cancelled": "Cancelled", "completed": "Completed"}, "table": {"orderId": "Order ID", "status": "Status", "mobile": "Mobile", "noMobile": "No mobile", "itemsCount": "Items", "totalValue": "Total Value", "createdBy": "Order Placed By", "createdAt": "Created", "deliveryDate": "Delivery Date", "facility": "Facility", "noFacility": "No facility", "empty": "No orders found", "endCustomerName": "Customer Name", "noEndCustomerName": "No customer name"}, "detail": {"title": "Order #{{id}}", "subtitle": "View and manage order details", "invalidId": "Invalid Order ID", "updateSuccess": "Order updated successfully", "updateError": "Failed to update order", "cancelSuccess": "Order cancelled successfully", "cancelError": "Failed to cancel order", "saving": "Saving...", "cancelling": "Cancelling...", "cancelOrder": "Cancel Order", "updateOrder": "Update Order", "confirming": "Confirming...", "confirmOrder": "Confirm Order", "notReachable1": "Not Reachable 1", "notReachable2": "Not Reachable 2", "markingUnreachable": "Marking...", "error": {"title": "Error Loading Order", "notFound": "Order not found or could not be loaded"}, "overview": {"title": "Order Overview", "status": "Status", "total": "Total", "items": "Items", "facility": "Facility", "cancellationReason": "Cancellation Reason", "confirmedNote": "Confirmed orders can only be cancelled", "selectFacility": "Select a facility", "createdByUser": "Order Created by User: {{userId}}", "createdByBhumi": "Order placed by <PERSON><PERSON><PERSON>"}, "customer": {"title": "Customer Information", "mobile": "Mobile", "deliveryDate": "Delivery Date"}, "location": {"title": "Delivery Location", "address": "Address", "addressPlaceholder": "Enter delivery address", "noAddress": "No address provided", "coordinates": "Coordinates", "coordinatesPlaceholder": "Enter coordinates (lat lng)", "serviceability": "Serviceability", "serviceableWith": "Serviceable with {{facility}}", "notServiceable": "Not serviceable in this area", "map": "Map"}, "items": {"title": "Order Items", "addItem": "Add Item", "product": "Product", "unit": "Unit", "quantity": "Quantity", "orderedQty": "Ordered Qty", "pickedQty": "Picked <PERSON><PERSON>", "deliveredQty": "Delivered <PERSON><PERSON>", "price": "Price", "total": "Total", "orderedTotal": "Ordered Total", "pickedTotal": "Picked Total", "deliveredTotal": "Delivered Total", "editMode": "Edit quantities and prices, or add/remove items", "viewMode": "Order items and their details", "emptyOrder": "No items in this order yet.", "addSomeItems": "Add some items", "invalidSku": "Invalid SKU", "loadingSkuDetails": "Loading SKU details...", "skuDetailsUnavailable": "SKU details unavailable", "orderTotal": "Order Total:", "notSet": "—"}, "additional": {"title": "Additional Information", "deliveryInstructions": "Delivery Instructions", "deliveryInstructionsPlaceholder": "Enter any special delivery instructions...", "noDeliveryInstructions": "No delivery instructions provided", "orderComments": "Order Comments", "orderCommentsPlaceholder": "Enter any comments about this order...", "noOrderComments": "No order comments"}, "fulfillment": {"title": "Fulfillment Details", "rosterId": "Roster ID", "orderId": "Order ID", "noOrderId": "No order ID", "type": "Type"}, "skuModal": {"title": "Add Items to Order", "searchPlaceholder": "Search SKUs..."}, "cancellation": {"title": "Cancel Order", "message": "Please provide a reason for cancelling this order:", "reason": "Cancellation Reason", "reasonPlaceholder": "Enter the reason for cancellation...", "reasonRequired": "Cancellation reason is required", "confirm": "Cancel Order"}, "confirmation": {"title": "Confirm Order", "message": "Are you sure you want to confirm this order? This action will update the order status to \"CONFIRMED\".", "successMessage": "Order #{{id}} will be marked as confirmed and ready for fulfillment.", "deliveryDate": {"title": "Select Delivery Date", "required": "Please select a delivery date to confirm the order", "today": "Today", "tomorrow": "Tomorrow"}, "confirmButton": "Confirm Order", "cancelButton": "Cancel"}, "facilityRequired": {"message": "Please select a facility before confirming the order", "hint": "Select a facility from the dropdown above to enable order confirmation"}, "unreachable": {"title1": "Mark as Not Reachable 1", "title2": "<PERSON> as Not Reachable 2", "message1": "Are you sure you want to mark this order as 'Not Reachable 1'? This indicates the customer could not be reached on the first attempt.", "message2": "Are you sure you want to mark this order as 'Not Reachable 2'? This indicates the customer could not be reached on the second attempt.", "confirmButton1": "Mark as Not Reachable 1", "confirmButton2": "<PERSON> as Not Reachable 2", "cancelButton": "Cancel", "successMessage1": "Order #{{id}} has been marked as 'Not Reachable 1'", "successMessage2": "Order #{{id}} has been marked as 'Not Reachable 2'"}}}, "categories": {"title": "Categories", "subtitle": "Manage categories. Drag and drop to reorder.", "backToCategories": "Back to Categories", "create": {"title": "Create New Category", "subtitle": "Add a new product category with English and Tamil names to organize your inventory", "breadcrumb": "Create", "success": "Category created successfully", "error": "Failed to create category", "submitLabel": "Create Category"}, "edit": {"title": "Edit Category", "subtitle": "Update the category information below. Changes will be reflected across all associated SKUs.", "success": "Category updated successfully", "error": "Failed to update category. Please try again.", "submitLabel": "Update Category", "notFound": "Category not found", "notFoundDescription": "The category you're looking for doesn't exist or couldn't be loaded.", "errorTitle": "Error updating category", "loadError": "Failed to load category. Please try again."}, "manageSkus": {"title": "Manage SKUs", "subtitle": "Manage SKUs in this category. Drag and drop to reorder SKUs within the category.", "categoryLabel": "Category:", "unsavedChanges": "You have unsaved changes to the SKUs in this category", "currentSkus": "Current SKUs", "dragHelpText": "Drag and drop rows to reorder SKUs within this category", "removeSelected": "Remove Selected", "addSkus": "Add SKUs", "emptyState": "No SKUs in this category yet.", "addSomeSkus": "Add some SKUs", "addSkusModal": {"title": "Add SKUs to Category", "searchPlaceholder": "Search SKUs..."}, "table": {"order": "Order", "sku": "SKU", "name": "Name", "price": "Price", "discount": "Discount", "variants": "Variants", "mrpLabel": "MRP:", "variantsCount": "variants"}, "messages": {"updateSuccess": "Category updated successfully", "updateError": "Failed to save SKU changes. Please try again.", "categoryNotFound": "Category not found", "loadError": "Failed to load category data"}}}, "skus": {"title": "SKU Management", "description": "Manage all SKUs across categories. View, edit, and organize your product inventory.", "error": {"loadFailed": "Failed to load SKUs"}, "count": "{{filtered}} of {{total}} SKUs", "actions": {"create": "Create SKU", "view": "View SKU", "edit": "Edit SKU"}, "filters": {"searchPlaceholder": "Search SKUs..."}, "save": {"success": "SKU changes saved successfully", "error": "Failed to save SKU changes. Please try again."}, "export": {"validationError": "Export failed due to data validation errors: {{errors}}", "success": "Export completed successfully", "error": "Export failed. Please try again."}, "bulkActions": {"selected": "{{count}} SKU{{plural}} selected"}, "unsavedChanges": "You have unsaved changes to SKUs", "importPreview": {"count": "{{count}} SKUs ready to be imported"}, "validation": {"parentCategoryRequired": "Parent SKUs must be assigned to at least one category for discoverability", "orphanCategoryRequired": "Child SKUs without parents must be assigned to at least one category for discoverability"}, "form": {"categoryRequiredParent": "Categories are required for parent SKUs to ensure they can be discovered by customers", "categoryRequiredOrphan": "Categories are required for child SKUs without parents to ensure they can be discovered by customers", "categoryOptionalChild": "Categories are optional for child SKUs with parents as they inherit discoverability from their parent", "statusDefaultInactive": "New SKUs are created as inactive by default for safety. Activate when ready to sell."}, "table": {"noResultsFiltered": "No SKUs match your current filters.", "noResults": "No SKUs found.", "createFirst": "Create your first SKU", "columns": {"sku": "SKU", "name": "Name", "type": "Type", "categories": "Categories", "price": "Price", "status": "Status", "variants": "Variants", "actions": "Actions"}, "moreCategories": "+{{count}} more", "price": {"mrp": "MRP", "cp": "CP", "sp": "SP"}, "variantsCount": "{{count}} variants"}}, "components": {"adminHeader": {"toggleMenu": "Toggle navigation menu"}, "iconPicker": {"title": "Select an Icon", "close": "Close", "heroIcons": "HeroIcons", "customSvg": "Custom SVG", "current": "Current", "clear": "Clear", "searchPlaceholder": "Search icons...", "iconsFound": "icons found", "recentCustomSvgs": "Recent Custom SVGs", "svgCode": "SVG Code", "preview": "Preview", "invalidSvg": "Invalid SVG", "svgPreviewText": "SVG preview will appear here", "useThisSvg": "Use This SVG", "cancel": "Cancel"}, "sortableTable": {"order": "Order", "name": "Name", "icon": "Icon", "skus": "SKUs", "status": "Status", "actions": "Actions", "noIcon": "No icon", "edit": "Edit", "manageSkus": "Manage SKUs", "saveFirst": "(Save first)", "noCategories": "No categories found", "createFirstCategory": "Create your first category to get started."}, "categoryForm": {"nameEnglish": "Name (English)", "nameEnglishRequired": "Name (English) *", "nameEnglishPlaceholder": "Enter category name in English", "nameTamil": "Name (Tamil)", "nameTamilPlaceholder": "Enter category name in Tamil (optional)", "iconRequired": "Icon *", "selectIcon": "Select an icon", "iconHelpText": "Choose an icon that represents this category", "backgroundColorOptional": "Background Color (Optional)", "noColor": "No color", "colorPlaceholder": "#3B82F6", "clear": "Clear", "addColor": "Add Color", "backgroundHelpText": "Background color for the category card. Leave empty for no background color.", "preview": "Preview", "categoryName": "Category Name", "tamil": "Tamil:", "cancel": "Cancel", "saving": "Saving...", "errors": {"nameRequired": "English name is required", "iconRequired": "Icon is required"}}, "statusBadge": {"active": "Active", "inactive": "Inactive", "pending": "Pending", "confirmed": "Confirmed", "cancelled": "Cancelled", "completed": "Completed", "inProgress": "In Progress", "abandoned": "Abandoned", "converted": "Converted"}}, "returns": {"title": "Returns Management", "subtitle": "Process returns for partially delivered orders", "error": {"title": "Error Loading Orders", "description": "Failed to load orders for returns processing.", "retry": "Retry"}, "filters": {"title": "Date Range Filters", "dateFrom": "From Date", "dateTo": "To Date", "summary": "{{count}} orders available for returns processing"}, "messages": {"noReturnData": "Please enter return quantities for at least one item", "submitSuccess": "Returns submitted successfully for Order #{{id}}", "submitError": "Failed to submit returns. Please try again."}, "table": {"empty": "No Partially Delivered Orders", "emptyDescription": "There are no orders with partially delivered items in the selected date range.", "orderId": "Order ID", "status": "Status", "mobile": "Mobile", "items": "Items", "total": "Total", "deliveryDate": "Delivery Date", "facility": "Facility"}, "items": {"title": "Return Items", "titleForOrder": "Return Items for Order #{{id}}", "product": "Product", "unit": "Unit", "expected": "Expected", "expectedLabel": "Expected", "returned": "Returned", "returnedLabel": "Returned", "loss": "Loss", "inventoryLoss": "Inventory Loss: {{amount}} {{unit}}"}, "actions": {"submitting": "Submitting...", "submitReturns": "Submit Returns"}}}, "account": {"title": "Account", "subtitle": "Manage your account and view your orders", "breadcrumbs": {"home": "Home", "account": "Account", "orders": "Today's Orders", "orderDetail": "Order Details"}, "menu": {"todaysOrder": "Today's Order", "todaysOrderDesc": "View orders placed today"}, "orders": {"title": "Today's Orders", "subtitle": "Orders you placed today", "empty": "No orders placed today", "emptyDesc": "When you place orders, they will appear here.", "error": {"title": "Failed to load orders", "generic": "Something went wrong.", "retry": "Retry"}, "orderId": "Order #{{id}}", "deliveryDate": "Delivery", "itemsCount": "{{count}} items", "placedOn": "Placed on", "customerName": "Customer", "customerMobile": "Mobile", "noCustomerName": "Not provided", "noMobile": "Not provided", "noTime": "Time not available"}, "orderDetail": {"title": "Order #{{id}}", "subtitle": "Order details and status", "invalidId": "Invalid order ID", "error": {"title": "Failed to load order", "notFound": "Order not found.", "retry": "Retry"}, "back": "Back to Orders", "overview": {"title": "Order Overview", "status": "Status", "total": "Total Amount", "items": "Items", "facility": "Facility", "cancellationReason": "Cancellation Reason"}, "customer": {"title": "Customer Information", "name": "Customer Name", "mobile": "Mobile Number", "deliveryDate": "Delivery Date", "noName": "Not provided", "noMobile": "Not provided"}, "location": {"title": "Delivery Location", "address": "Delivery Address", "coordinates": "Coordinates", "noAddress": "No address provided", "noCoordinates": "No coordinates provided"}, "items": {"title": "Order Items", "empty": "No items in this order", "product": "Product", "unit": "Unit", "orderedQty": "Ordered", "pickedQty": "Picked", "deliveredQty": "Delivered", "price": "Price", "total": "Total", "orderTotal": "Order Total", "loadingDetails": "Loading...", "unavailable": "Details unavailable"}, "additional": {"title": "Additional Information", "deliveryInstructions": "Delivery Instructions", "orderComments": "Order Comments", "noInstructions": "No delivery instructions provided", "noComments": "No order comments provided"}}}}
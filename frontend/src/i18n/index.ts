import { createInstance } from 'i18next';
import { initReactI18next } from 'react-i18next';

import enTranslation from './locales/en.json';
import taTranslation from './locales/ta.json';

// Initialize i18n instance
const i18n = createInstance({
    resources: {
        en: { translation: enTranslation },
        ta: { translation: taTranslation },
    },
    lng: process.env.NEXT_PUBLIC_DEFAULT_LANGUAGE || 'en', // Default language
    fallbackLng: 'en',
    interpolation: {
        escapeValue: false, // React already escapes by default
    },
});

i18n.use(initReactI18next).init();

export default i18n; 
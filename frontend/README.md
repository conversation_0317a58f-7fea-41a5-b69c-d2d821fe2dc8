# Infinity Portal Frontend

This is the frontend codebase for the Infinity Portal project, a web application designed to enable pre-ordering of a wide range of SKUs for rural customers ("Bhumis").

## Project Overview

Infinity Portal is designed for three main user types:
- **<PERSON><PERSON><PERSON><PERSON><PERSON> (Cart Operator)**: Uses the app on a tablet to assist <PERSON><PERSON><PERSON> with pre-orders
- **<PERSON><PERSON> (Admin/Customer Care)**: Uses the app on desktop to assist Bhumis and manage products
- **<PERSON><PERSON><PERSON> (Customer)**: Views products via shared links

## Tech Stack

- **Framework**: Next.js
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **State Management**: React Context API

## Code Standards

This project follows coding standards and patterns defined in `.cursor-rules.json`. Key component patterns include:

1. **Component Structure**: Components use TypeScript interfaces for props and follow a consistent structure
2. **Product Catalog Components**: 
   - ProductCard: For displaying individual products with image, name, price
   - VariantSelection: For selecting product variants through a modal/bottom sheet
   - CategorySection: For displaying categorized products in horizontal scrollable sections
3. **Cart Management**: Context-based cart state and functionality
4. **Multi-Tab Session**: Interface for managing multiple customer sessions
5. **Location Capture**: Interactive map for capturing delivery locations
6. **Internationalization**: Support for multiple languages

## Getting Started

```bash
# Install dependencies
npm install

# Run development server
npm run dev

# Build for production
npm run build

# Start production server
npm start
```

## Development Guidelines

- Follow the component patterns in `.cursor-rules.json`
- Use TypeScript interfaces for all props and data structures
- Implement responsive design for tablet, desktop, and mobile views
- Use the i18n system for all user-facing text
- Follow the mobile-first approach with Tailwind's responsive classes

## Folder Structure

- `/src/app`: Next.js App Router components and routes
- `/src/app/[locale]`: Internationalized routes
- `/src/app/components`: Reusable UI components
- `/src/app/context`: React Context providers
- `/src/i18n`: Internationalization configuration and translations
- `/src/lib`: Utility functions, API services, and helpers

## Contributing

Please review the patterns in `.cursor-rules.json` before contributing code to ensure consistency.

## Google Maps Integration

The application uses Google Maps for location selection in the Session Manager. To enable this functionality:

1. Get a Google Maps JavaScript API key from the [Google Cloud Platform Console](https://console.cloud.google.com/).
2. Enable the "Maps JavaScript API" for your project.
3. Create a `.env.local` file in the root of the frontend directory with the following content:

```
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your_actual_api_key_here
```

If no API key is provided, the application will use a fallback mode that simulates map functionality by generating random coordinates.
